import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/BPColorUtil.dart';

import '../../../generated/locales.g.dart';

class CustomCountryPicker extends StatefulWidget {
  static void showCountryPickerBottomSheet({
    bool showCountryPhoneCode = false,
    String? initialCountryCode,
    required Function(Country country) onSelect,
  }) {
    Get.bottomSheet(
      CustomCountryPicker._(
        onSelect: onSelect,
        initialCountryCode: initialCountryCode,
        showCountryPhoneCode: showCountryPhoneCode,
      ),
      ignoreSafeArea: false,
      isScrollControlled: true,
    );
  }

  const CustomCountryPicker._({
    required this.showCountryPhoneCode,
    required this.initialCountryCode,
    required this.onSelect,
  });

  final bool showCountryPhoneCode;
  final String? initialCountryCode;
  final Function(Country country) onSelect;

  @override
  State<CustomCountryPicker> createState() => _CustomCountryPickerState();
}

class _CustomCountryPickerState extends State<CustomCountryPicker> {
  final TextEditingController _searchController = TextEditingController();
  final List<Country> _countries = CountryService().getAll();
  final List<Country> _filteredCountries = [];

  @override
  void initState() {
    super.initState();

    _filteredCountries.addAll(_countries);
  }

  void _onSearchChanged(String value) {
    setState(() {
      _filteredCountries.clear();
      _filteredCountries.addAll(
        _countries.where((country) {
          String inputValue = value.replaceAll("+", "");

          bool containsPhoneCode = country.phoneCode.contains(inputValue);
          bool containsName = country.name.toLowerCase().contains(inputValue.toLowerCase());

          return containsPhoneCode || containsName;
        }),
      );
    });
  }

  void _onCountrySelected(Country country) {
    widget.onSelect(country);
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Card(
        margin: EdgeInsets.zero,
        color: BPColor.grey24,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              style: const TextStyle(color: BPColor.whiteText),
              decoration: InputDecoration(
                hintText: LocaleKeys.common_search.tr,
                focusColor: BPColor.brand,
                fillColor: BPColor.grey2A,
                filled: true,
                contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(99),
                ),
                hintStyle: const TextStyle(color: BPColor.greyText),
                prefixIcon: const Icon(Icons.search, color: BPColor.greyText),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.close, color: BPColor.greyText),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged("");
                        },
                      )
                    : null,
              ),
            ).paddingSymmetric(horizontal: 16, vertical: 16),
            Expanded(
              child: Scrollbar(
                child: ListView.separated(
                  itemCount: _filteredCountries.length,
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox();
                  },
                  itemBuilder: (BuildContext context, int index) {
                    var country = _filteredCountries[index];

                    return ListTile(
                      onTap: () => _onCountrySelected(country),
                      leading: SizedBox(
                        height: 40,
                        width: 40,
                        child: FittedBox(
                          child: Text(country.flagEmoji),
                        ),
                      ),
                      title: Text(
                        country.name,
                        style: const TextStyle(color: BPColor.whiteText),
                      ),
                      subtitle: widget.showCountryPhoneCode
                          ? Text(
                              "+${country.phoneCode}",
                              style: const TextStyle(color: BPColor.greyText),
                            )
                          : null,
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
