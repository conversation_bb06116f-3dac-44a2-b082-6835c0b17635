import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import 'BPBottomPopUp.dart';
import 'BPColorUtil.dart';
import 'BPTextButton.dart';

class BPDatePicker extends StatelessWidget {
  final DateTime initialDate;
  final DateTime? minimumDate;
  final DateTime? maximumDate;
  final Function(DateTime)? onDateTimeChanged;
  final Function(DateTime)? onDone;
  late DateTime _selectedDate;

  BPDatePicker({
    required this.initialDate,
    this.minimumDate,
    this.maximumDate,
    this.onDateTimeChanged,
    this.onDone,
    super.key,
  }) {
    _selectedDate = initialDate;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 230,
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              platformBrightness: Brightness.dark,
            ),
            child: CupertinoTheme(
              data: const CupertinoThemeData(
                brightness: Brightness.dark,
                primaryColor: Colors.white,
                textTheme: CupertinoTextThemeData(
                  dateTimePickerTextStyle: TextStyle(
                    color: Colors.white,
                    fontSize: 22,
                    fontWeight: FontWeight.normal,
                  ),
                  pickerTextStyle: TextStyle(
                    color: Colors.white,
                    fontSize: 21,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: BPColor.backgroundGrey,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  initialDateTime: initialDate,
                  minimumDate: minimumDate,
                  maximumDate: maximumDate,
                  backgroundColor: Colors.transparent,
                  onDateTimeChanged: (date) {
                    _selectedDate = date;
                    if (onDateTimeChanged != null) {
                      onDateTimeChanged!(_selectedDate);
                    }
                  },
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 0),
        SizedBox(
          height: 48,
          child: BPTextButton(
            title: LocaleKeys.common_done.tr,
            backgroundColor: BPColor.brand,
            color: Colors.white,
            borderRadius: 6,
            fontSize: 16,
            onPressed: () {
              if (onDone != null) {
                onDone!(_selectedDate);
              }
              Get.back();
            },
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  static show({
    required BuildContext context,
    required DateTime initialDate,
    DateTime? minimumDate,
    DateTime? maximumDate,
    dynamic Function(DateTime dateTime)? onDateTimeChanged,
    dynamic Function(DateTime dateTime)? onDone,
    Key? key,
  }) {
    BPBottomPopUp.show(
      child: BPDatePicker(
        onDateTimeChanged: onDateTimeChanged,
        onDone: onDone,
        initialDate: initialDate,
        minimumDate: minimumDate,
        maximumDate: maximumDate,
        key: key,
      ),
    );
  }
}
