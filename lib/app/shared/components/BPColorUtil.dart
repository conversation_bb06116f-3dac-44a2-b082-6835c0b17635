import 'dart:ui';

extension BPColor on Color {
  static Color grey(int scale) => Color(0xFF000000 + scale * 0x10000 + scale * 0x100 + scale * 0x1);
  static const Color brand = Color(0xFFc5a880);
  static const Color brandLight = Color(0x55c5a880);
  static const Color black = Color(0xFF09081C);
  static const Color lightBrandText = Color(0xFF70758E);
  static const Color grayText = Color(0xFFBDC2C5);
  static const Color greyE4 = Color(0xFFE4E4E4);
  static const Color grayE6 = Color(0xFFE6E6E6);
  static const Color gray44 = Color(0xFF444444);
  static const Color whiteBG = Color(0xFFF5F5F8);
  static const Color infoRed = Color(0xFFEC464A);
  static const Color infoBlue = Color(0xFF3190FF);
  static const Color lightBG = Color(0xFFF5F6F7);
  static const Color lightPurpleBG = Color(0xFFCCCCF8);
  static const Color lightGrayText = Color(0xFFA6A6CB);
  static const Color dashGrayText = Color(0xFF7B7D83);
  static const Color dashPurpleBG = Color(0xFFF1F1FF);
  static const Color dashGoldBG = Color(0xFFFBF3DB);
  static const Color darkGold = Color(0xFFc5a880);
  static const Color secondaryGrayText = Color(0xFF70758D);
  static const Color thirdLightPurple = Color(0xFFD5D5F4);
  static const Color thirdLightGray = Color(0xFFF5F6F7);
  static const Color linkHighlightBlue = Color(0xFF4597F7);
  static const Color notificationRed = Color(0xFFF44336);
  static const Color tipLightGray = Color(0xFF9EA3AE);

  static const Color background = Color(0xFF0f0f0f);
  static const Color backgroundGrey = Color(0xFF1d1e1f);
  static const Color gold = Color(0xFFc5a880);
  static const Color goldText = Color(0xFFc5a880);
  static const Color whiteText = Color(0xFFe6e6e6);
  static const Color greyText = Color(0xFF828282);
  static const Color grey2A = Color(0xFF2A2A2C);
  static const Color greyAA = Color(0xFFAAAAAA);
  static const Color grey66 = Color(0xFF666666);
  static const Color grey1B = Color(0xFF1B1B1B);
  static const Color grey24 = Color(0xFF242424);
}
