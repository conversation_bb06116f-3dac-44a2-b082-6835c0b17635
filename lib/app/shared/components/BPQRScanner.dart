import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../modules/QRScanner/views/qr_scanner_page.dart';
import 'BPEasyLoading.dart';
import '../../../generated/locales.g.dart';

class BPQRScanner {
  /// 启动QR扫码功能
  /// [onScanResult] 扫码成功的回调函数
  /// [onError] 扫码失败或权限被拒绝的回调函数
  static Future<void> startScan({
    required Function(String data) onScanResult,
    Function(String error)? onError,
  }) async {
    try {
      // 检查相机权限
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) {
        onError?.call(LocaleKeys.asset_verification_qr_scanner_permission_denied.tr);
        return;
      }

      // 导航到扫码页面
      final result = await Get.to<String>(
        () => const QRScannerPage(),
        transition: Transition.cupertino,
        duration: const Duration(milliseconds: 300),
      );

      // 处理扫码结果
      if (result != null && result.isNotEmpty) {
        onScanResult(result);
      }
    } catch (e) {
      onError?.call('${LocaleKeys.asset_verification_qr_scanner_start_failed.tr}: ${e.toString()}');
      BPEasyLoading.showError(LocaleKeys.asset_verification_qr_scanner_start_failed.tr);
    }
  }

  /// 检查相机权限
  static Future<bool> _checkCameraPermission() async {
    // 检查当前权限状态
    final status = await Permission.camera.status;
    
    if (status.isGranted) {
      return true;
    }
    
    if (status.isDenied) {
      // 请求权限
      final result = await Permission.camera.request();
      return result.isGranted;
    }
    
    if (status.isPermanentlyDenied) {
      // 权限被永久拒绝，引导用户去设置
      await _showPermissionDialog();
      return false;
    }
    
    return false;
  }

  /// 显示权限对话框
  static Future<void> _showPermissionDialog() async {
    await Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: Text(
          LocaleKeys.permission_camera_alert_title.tr,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          LocaleKeys.permission_camera_alert_content.tr,
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 16,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: const Text(
              'Settings',
              style: TextStyle(color: Colors.blue),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// 验证QR码数据格式
  static bool isValidQRData(String data) {
    if (data.isEmpty) return false;
    
    // 这里可以添加更多的验证逻辑
    // 比如检查是否是有效的URL、特定格式的字符串等
    
    return true;
  }

  /// 解析QR码数据
  static Map<String, dynamic> parseQRData(String data) {
    try {
      // 如果是URL
      if (data.startsWith('http://') || data.startsWith('https://')) {
        return {
          'type': 'url',
          'data': data,
          'url': data,
        };
      }
      
      // 如果是JSON格式
      if (data.startsWith('{') && data.endsWith('}')) {
        // 可以尝试解析JSON
        return {
          'type': 'json',
          'data': data,
          'raw': data,
        };
      }
      
      // 如果是邮箱
      if (data.contains('@') && data.contains('.')) {
        return {
          'type': 'email',
          'data': data,
          'email': data,
        };
      }
      
      // 如果是电话号码
      if (RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(data)) {
        return {
          'type': 'phone',
          'data': data,
          'phone': data,
        };
      }
      
      // 默认为文本
      return {
        'type': 'text',
        'data': data,
        'text': data,
      };
    } catch (e) {
      return {
        'type': 'text',
        'data': data,
        'text': data,
      };
    }
  }
} 