import 'package:flutter/material.dart';

class BPTextButton extends StatelessWidget {
  final String title;
  final void Function()? onPressed;
  final double fontSize;
  final String? fontFamily;
  final FontWeight fontWeight;
  final FontStyle? fontStyle;
  final Color? color;
  final Color? backgroundColor;
  final EdgeInsets? padding;
  final double borderRadius;
  final Size minimumSize;
  final Size? maximumSize;
  final Widget? leftIcon;
  final Widget? rightIcon;
  final double borderSideWidth;
  final Color borderSideColor;
  final double underlineWidth;
  final Color? underlineColor;
  final MainAxisSize mainAxisSize;
  final MainAxisAlignment mainAxisAlignment;
  const BPTextButton({
    super.key,
    this.title = '',
    this.onPressed,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w400,
    this.fontFamily,
    this.fontStyle,
    this.color,
    this.backgroundColor,
    this.borderRadius = 4,
    this.minimumSize = Size.zero,
    this.maximumSize,
    this.leftIcon,
    this.rightIcon,
    this.borderSideWidth = 0.0,
    this.borderSideColor = const Color(0x00000000),
    this.underlineWidth = 0.0,
    this.underlineColor,
    this.mainAxisSize = MainAxisSize.max,
    this.mainAxisAlignment = MainAxisAlignment.spaceBetween,
    this.padding,
  });
  @override
  Widget build(BuildContext context) {
    List<Widget> rowChildren = [
      leftIcon ?? const SizedBox(width: 8),
      Text(
        title,
        style: TextStyle(
          fontFamily: fontFamily,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          color: color ?? const Color(0xff444444),
        ),
      ),
      rightIcon ?? Container(),
    ];

    return TextButton(
      style: ButtonStyle(
        side: WidgetStatePropertyAll(
          BorderSide(width: borderSideWidth, color: borderSideColor),
        ),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        backgroundColor: WidgetStatePropertyAll(backgroundColor),
        padding: WidgetStatePropertyAll(
          padding ?? const EdgeInsets.only(left: 0, top: 5, right: 0, bottom: 5),
        ),
        minimumSize: WidgetStatePropertyAll(minimumSize),
        maximumSize: maximumSize == null ? null : WidgetStatePropertyAll(maximumSize!),
      ),
      onPressed: onPressed,
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: underlineWidth < 0.1
              ? null
              : Border(
                  bottom: BorderSide(
                    color: underlineColor ?? color ?? const Color(0xff444444),
                    width: underlineWidth,
                  ),
                ),
        ),
        child: Row(
          mainAxisSize: mainAxisSize,
          mainAxisAlignment: mainAxisAlignment,
          children: rowChildren,
        ),
      ),
    );
  }
}
