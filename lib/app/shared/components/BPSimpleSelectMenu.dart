import 'package:flutter/material.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'BPColorUtil.dart';

class BPMenuItemData {
  final Widget? leading;
  final String text;
  final bool activated;
  const BPMenuItemData({
    this.leading,
    required this.text,
    this.activated = true,
  });
}

class BPSimpleSelectMenu extends StatefulWidget {
  final Function(int)? onChanged;
  final List<BPMenuItemData> menuItemData;
  final Color? backgroundColor;
  final double height;
  final Color? primaryColor;
  final String? titleText;
  final String? errorText;
  final String? placeholderText;
  final int? initialIndex;
  final Widget? topRightIcon;
  final bool requested;
  final bool highlight;
  final bool autoOpen;
  final Color? textColor;
  final int? selectedValue;
  final Color? borderColor;
  final Color? iconColor;

  const BPSimpleSelectMenu({this.onChanged, this.menuItemData = const [], this.backgroundColor, this.height = 44, this.primaryColor, this.titleText, this.errorText, this.placeholderText, this.requested = false, this.highlight = false, this.autoOpen = false, this.topRightIcon, this.initialIndex, super.key, this.textColor = BPColor.black, this.selectedValue, this.borderColor, this.iconColor});

  @override
  _SimpleSelectMenuState createState() => _SimpleSelectMenuState();
}

class _SimpleSelectMenuState extends State<BPSimpleSelectMenu> {
  int? value;
  final dropdownKey = GlobalKey<DropdownButton2State>();

  @override
  void initState() {
    super.initState();
    print('widget.selectedValue: ${widget.selectedValue}');

    value = widget.initialIndex ?? widget.selectedValue;

    if (widget.autoOpen) {
      Future<void>.delayed(const Duration(milliseconds: 600), () {
        dropdownKey.currentState!.callTap();
      });
    }
  }

  setValue(dynamic val) {
    setState(() {
      value = val;
    });
  }

  @override
  Widget build(BuildContext context) {
    Widget buildBaseItem({Widget? leading, required String label, Color? textColor}) {
      return Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (leading != null) ...[
              leading,
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                label,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: textColor,
                ),
              ),
            ),
          ],
        ),
      );
    }

    List<DropdownMenuItem>? buildMenuItems() {
      return widget.menuItemData.map(
        (data) {
          final index = widget.menuItemData.indexOf(data);
          bool isSelected = widget.selectedValue == index;
          const needWhiteColor = false; //kIsWeb
          return DropdownMenuItem(
            value: index,
            child: Row(
              children: [
                Expanded(
                  child: buildBaseItem(
                    leading: data.leading,
                    label: data.text,
                    textColor: needWhiteColor ? (isSelected ? Colors.white : (data.activated ? BPColor.grayText : BPColor.grayText)) : (isSelected ? BPColor.brand : (data.activated ? BPColor.black : BPColor.grayText)),
                  ),
                ),
                if (isSelected) ...[
                  const Icon(
                    Icons.check,
                    color: needWhiteColor ? Colors.white : BPColor.brand,
                  ),
                  // const SizedBox(width: 10)
                ],
              ],
            ),
          );
        },
      ).toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.titleText != null || widget.topRightIcon != null) ...[
          Row(children: [
            Text(widget.titleText!,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: BPColor.black,
                ),),
            Text(widget.requested ? ' *' : '',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Colors.red,
                ),),
            const Spacer(),
            if (widget.topRightIcon != null) ...[widget.topRightIcon!],
          ],),
          const SizedBox(height: 8),
        ],
        Container(
          height: widget.height,
          padding: const EdgeInsets.only(left: 0, right: 0),
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? BPColor.lightBG,
            border: widget.borderColor != null ? Border.all(width: 1, color: widget.borderColor!) : Border.all(width: 1, color: (widget.highlight ? BPColor.infoRed : widget.backgroundColor ?? BPColor.lightBG)),
            borderRadius: BorderRadius.circular(8),
          ),
          // child: Theme(
          //   data: Theme.of(context).copyWith(
          //     focusColor: widget.primaryColor,
          //   ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton2(
              key: dropdownKey,
              value: widget.selectedValue ?? value,
              // iconEnabledColor: const Color(0xFFA0A0A0),
              // iconDisabledColor: const Color(0xFFF0F0F0),
              // dropdownColor: const Color(0xFFFAFAFA),
              // menuMaxHeight: 200,
              // icon: const Icon(Icons.keyboard_arrow_down_rounded),
              hint: Text(widget.placeholderText ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: BPColor.grayText,
                  ),),
              isExpanded: true,
              buttonStyleData: const ButtonStyleData(padding: EdgeInsets.only(left: 10), width: double.infinity),
              iconStyleData: IconStyleData(
                icon: const Padding(padding: EdgeInsets.only(left: 5, right: 10), child: Icon(Icons.keyboard_arrow_down_rounded)),
                iconEnabledColor: widget.iconColor ?? BPColor.black,
                iconDisabledColor: const Color(0xFFF0F0F0),
              ),
              dropdownStyleData: const DropdownStyleData(maxHeight: 200, offset: Offset(0, -2), scrollPadding: EdgeInsets.symmetric(vertical: 10)),
              selectedItemBuilder: (BuildContext context) => widget.menuItemData.map(
                (data) {
                  return buildBaseItem(label: data.text, textColor: widget.textColor);
                },
              ).toList(),
              onChanged: widget.onChanged != null
                  ? (dynamic val) {
                      setValue(val);
                      widget.onChanged!(val);
                    }
                  : null,
              items: buildMenuItems(),
            ),
          ),
          // ),
        ),
        if (widget.errorText != null) ...[
          const SizedBox(height: 5),
          Row(children: [
            Text(widget.errorText!,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: BPColor.infoRed,
                ),),
          ],),
        ],
      ],
    );
  }
}
