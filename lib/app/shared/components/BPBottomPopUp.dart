import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'BPColorUtil.dart';

class BPBottomPopUp extends StatelessWidget {
  final Widget child;
  const BPBottomPopUp({required this.child, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      padding: const EdgeInsets.only(left: 25, right: 25),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          Container(
            width: 40,
            height: 4,
            decoration: const BoxDecoration(
              color: BPColor.greyText,
              borderRadius: BorderRadius.all(Radius.circular(2)),
            ),
          ),
          const SizedBox(height: 20),
          SafeArea(child: child),
        ],
      ),
    );
  }

  static show({required Widget child, Color? backgroundColor}) {
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: backgroundColor ?? BPColor.backgroundGrey,
      builder: (ct) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(Get.context!).viewInsets.bottom,
          ),
          child: BPBottomPopUp(child: child),
        );
      },
    );
  }
}
