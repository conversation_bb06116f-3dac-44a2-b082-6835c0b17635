import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart' as getx;
import 'package:uuid/uuid.dart';
import '../../data/BPCardModels.dart';
import '../../data/BPCommunityModels.dart';
import '../../data/BPNotificationModels.dart';
import '../../data/BPSettingsModels.dart';
import 'package:retrofit/retrofit.dart';

import '../../data/BPAccountModels.dart';
import '../../data/BPCommonModels.dart';
import '../../data/BPInterestModels.dart';
import '../../data/BPMeecoinModels.dart';
import '../../data/BPTermsAndConditionModel.dart';
import '../../data/BPUniversityModels.dart';
import '../../data/BPUserModels.dart';
import '../../data/OtpResponseModel.dart';
import '../../data/scdata/mv_benefits_models.dart';
import '../../data/scdata/mv_star_comment_model.dart';
import '../../data/scdata/mv_star_event_model.dart';
import '../../data/scdata/mv_star_model.dart';
import '../BPConfigure.dart';
import '../components/BPEasyLoading.dart';
import 'BPAPIError.dart';
import 'BPBaseDio.dart';
part 'BPRestAPI.g.dart';

extension DioError on Object {
  bool get isCancel => BPBaseDio.instance.getDioError(this).type == BPAPIErrorType.cancel;
}

catchedError(Object obj) {
  final error = BPBaseDio.instance.getDioError(obj);
  if (error.type == BPAPIErrorType.cancel || error.message.isEmpty) {
    return;
  }

  SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
    MVToast.showError(error.message, duration: kDebugMode ? 10 : 3);
  });
}

class MVTaskToken {
  bool get isLoading => isLoadingRx.value; //obx supported
  final isLoadingRx = false.obs;
  CancelToken? get task => _task.value;

  String? _identifier;
  final _task = getx.Rx<CancelToken?>(null);

  void _create({required String idf}) {
    _identifier = idf;
    if (_task.value != null) {
      _task.value?.cancel();
    }
    _task.value = CancelToken();
    isLoadingRx.value = true;
    _task.value?.whenCancel.then((value) {
      completed(idf: idf);
    });
  }

  String create() {
    final idf = const Uuid().v4();
    _create(idf: idf);
    return idf;
  }

  bool completed({required String idf}) {
    if (_identifier == idf) {
      _task.value = null;
      isLoadingRx.value = false;
      return true;
    }
    return false;
  }

  void cancel() {
    _task.value?.cancel();
  }
}

@RestApi()
abstract class BPRestClient {
  static _BPRestClient? _atRestClient;

  factory BPRestClient({Dio? dio, String? baseUrl}) {
    _atRestClient ??= _BPRestClient(dio ?? BPBaseDio.instance.getDio(baseUrl: baseUrl ?? BPConfigure.serverAPIHost), baseUrl: baseUrl ?? BPConfigure.serverAPIHost);
    return _atRestClient!;
  }

  static clear() {
    _atRestClient = null;
  }

  @POST('/api/upload')
  @MultiPart()
  Future<List<BPFileUploadResult>> uploadFile({@Part(name: 'files') required File file, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/upload')
  @Extra({'loadingIndicator': false})
  @MultiPart()
  Future<List<BPFileUploadResult>> uploadFileDetail({@Part(name: 'files') required File file, @SendProgress() ProgressCallback? sendProgress, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/upload')
  @Extra({'loadingIndicator': false})
  @MultiPart()
  Future<List<BPFileUploadResult>> uploadMultipartFiles({@Part() required List<MultipartFile> files, @SendProgress() ProgressCallback? sendProgress, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/auth/zalo/callback')
  Future<BPAccountSignedInModel> signInZalo({@Query('access_token') required String accessToken, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/auth/facebook/callback')
  Future<BPAccountSignedInModel> signInFacebook({@Query('access_token') required String accessToken, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/auth/google/callback')
  Future<BPAccountSignedInModel> signInGoogle({@Query('access_token') required String accessToken, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/auth/apple/callback')
  Future<BPAccountSignedInModel> signInApple({@Query('access_token') required String accessToken, @Query('clientId') String? clientId, @CancelRequest() CancelToken? cancelToken});

  @PUT('/api/users/{id}')
  @Extra({'loadingIndicator': false})
  Future<BPUserModel> editMeInfo({@Path() required int id, @Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/users/me')
  @Extra({'loadingIndicator': false})
  Future<BPUserModel> me({@CancelRequest() CancelToken? cancelToken});

  @GET('//api/users/balance')
  @Extra({'loadingIndicator': false})
  Future<dynamic> balance({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/captcha/request')
  Future<BPCaptchaResponse> authCodeRequest({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/captcha/verify')
  Future<OperationResponse> checkAuthCodeRequest({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/universities')
  Future<List<BPUniversity>> universities({@Query('limit') int? limit, @Query('offset') int? offset, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/interests')
  Future<List<BPInterest>> interests({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/send-email-verification')
  Future<BPUserModel> sendEmailVerificationCode({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/email-verification')
  Future<BPUserModel> emailVerification({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/certificate-verification')
  Future<BPUserModel> certPaperVerification({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/maps/geocode')
  Future<dynamic> geocodeReverse({@Query('latitude') required String latitude, @Query('longitude') required String longitude, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/hello')
  @Extra({'loadingIndicator': false})
  Future<dynamic> hello({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/users/{id}')
  Future<BPUserModel> getUserInfo({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/new-today')
  @Extra({'loadingIndicator': false})
  Future<Map<String, List<BPCardModel>>> getTodayUsers({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/passed')
  @Extra({'loadingIndicator': false})
  Future<Map<String, List<BPCardModel>>> getPassedUsers({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/matched')
  Future<List<BPCardModel>> getMatchedUsers({@Query('limit') int? limit, @Query('offset') int? offset, @Query('coll') String? coll, @Query('status') String? status, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/got')
  Future<List<BPCardModel>> getGotOkUsers({@Query('limit') int? limit, @Query('offset') int? offset, @Query('coll') String? coll, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/sent')
  Future<List<BPCardModel>> getSentOkUsers({@Query('limit') int? limit, @Query('offset') int? offset, @Query('coll') String? coll, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/cards/{id}/unlock')
  Future<BPCardModel> unlockCard({@Path() required int id, @Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/premium-rule')
  Future<dynamic> premiumRule({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/cards/premium')
  Future<List<BPCardModel>> purchasePremium({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/{id}')
  Future<BPUserModel> cardDetail({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/cards/{id}/rating')
  Future<RatingResponse> rating({@Path() required int id, @Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/cards/{id}/heart')
  Future<SendHeartResponse> heart({@Path() required int id, @Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/high-eval')
  Future<List<BPCardModel>> highEvaluation({@Query('limit') int? limit, @Query('offset') int? offset, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/transactions')
  Future<List<BPMCTransaction>> transactions({@Query('limit') int? limit, @Query('offset') int? offset, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/meecoins')
  Future<List<BPMCShopItem>> mcShop({@CancelRequest() CancelToken? cancelToken});



  @POST('/api/recharges/order')
  Future<BPOrderModel> createOrder({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/recharges/verify-receipt')
  Future<VerifyResultModel> verifyReceiptApple({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/blacks')
  Future<OperationResponse> addBlack({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/blacks/remove')
  Future<OperationResponse> deleteBlock({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/blacks')
  Future<dynamic> blackList({@Query('offset') required int offset, @Query('limit') required int limit, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/reports')
  Future<OperationResponse> report({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/check-nickname')
  @Extra({'loadingIndicator': false})
  Future<dynamic> checkNickname({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/interest-categories')
  Future<List<BPInterestCollection>> interestsCategories({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/confirmation')
  Future<BPUserModel> confirmReg({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/referral-verification')
  Future<dynamic> checkReferral({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/users/reg-bonus')
  Future<dynamic> getBonus({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/feedback-categories')
  Future<List<FeedbackCategory>> getFeedbackCategories({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/feedbacks')
  Future<OperationResponse> addFeedback({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/delete')
  Future<OperationResponse> deleteAccount({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/notifications/badge')
  @Extra({'loadingIndicator': false})
  Future<dynamic> getNotificationBadge({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/notifications')
  @Extra({'loadingIndicator': false})
  Future<List<BPNotification>> notifications({@Query('offset') required int offset, @Query('limit') required int limit, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/notifications/{id}/view')
  @Extra({'loadingIndicator': false})
  Future<OperationResponse> viewNotification({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/notifications/viewall')
  @Extra({'loadingIndicator': false})
  Future<OperationResponse> viewAllNotification({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/reset')
  Future<BPUserModel> resetInfo({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});
  @POST('/api/users/connect-community')
  Future<BPUserModel> connectCommunity({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/asset-verification')
  Future<BPUserModel> addAssetVerification({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/users/confirm-assets')
  @Extra({'loadingIndicator': false})
  Future<dynamic> confirmAssets({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/boards')
  @Extra({'loadingIndicator': false})
  Future<List<BPCommunityBoard>> getBoards({@Query('perm') required RequestBoardsType perm, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/posts')
  @Extra({'loadingIndicator': false})
  Future<List<BPCommunityPost>> postList({
    // @Query('offset') required int offset,
    // @Query('limit') required int limit,
    @Query('boardId') required int boardId,
    @Query('ids') required String ids,
    @CancelRequest() CancelToken? cancelToken,
  });

  @POST('/api/community/posts/{id}/like')
  Future<PostExtra> postLike({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/community/posts/{id}/dislike')
  Future<PostExtra> postDislike({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/community/comments/{id}/like')
  Future<PostCommentExtra> postCommentLike({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/community/comments/{id}/dislike')
  Future<PostCommentExtra> postCommentDislike({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/community/comments')
  Future<PostComment> createComment({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/comments')
  Future<List<PostComment>> commentsList({@Query('offset') required int offset, @Query('limit') required int limit, @Query('postId') required int postId, @CancelRequest() CancelToken? cancelToken});

  @DELETE('/api/community/comments/{id}')
  Future<OperationResponse> removeComment({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/community/posts')
  Future<BPCommunityPost> createPost({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/community/reports')
  Future<OperationResponse> communityReport({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @DELETE('/api/community/posts/{id}')
  Future<OperationResponse> removePost({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/jobs/today')
  Future<List<BPCardModel>> todayJobs({@Query('coll') String? coll, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/cards/jobs/{id}')
  Future<BPUserModel> jobDetail({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/cards/jobs/{id}/send')
  Future<dynamic> sendResume({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/recharges/google-verify-receipt')
  Future<VerifyResultModel> verifyReceiptGoogle({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/check-version')
  @Extra({'loadingIndicator': false})
  Future<BPCheckAppVersionResult> checkVersion({@CancelRequest() CancelToken? cancelToken});

  @GET('/api/users/verification/state')
  @Extra({'loadingIndicator': false})
  Future<BPUserModel> verificationState({@CancelRequest() CancelToken? cancelToken});



  @POST('/api/cards/{id}/accept')
  Future<AcceptHeartResponse> acceptHeart({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/cards/{id}/reject')
  Future<dynamic> reject({
    @Path() required int id,
    @Body() required Map<String, dynamic> body,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/community/users/{id}')
  Future<AuthorUser> authorDetail({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/users/{id}/posts')
  Future<List<BPCommunityPost>> communityAuthorPosts({@Path() required int id, @Query('offset') required int offset, @Query('limit') required int limit, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/users/{id}/comments')
  Future<List<PostComment>> communityAuthorComments({@Path() required int id, @Query('offset') required int offset, @Query('limit') required int limit, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/posts/{id}')
  Future<BPCommunityPost> communityPostDetail({@Path() required int id, @Query('commentId') int? commentId, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/posts/{id}/likes')
  Future<List<AuthorUser>> communityPostLikes({@Path() required int id, @Query('offset') required int offset, @Query('limit') required int limit, @CancelRequest() CancelToken? cancelToken});

  @PUT('/api/community/posts/{id}')
  Future<BPCommunityPost> editPost({@Path() required int id, @Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/community/posts/{id}/suggestions')
  Future<List<MentionUser>> postMentionSuggestions({@Path() required int id, @Query('name') String? name, @Query('offset') required int offset, @Query('limit') required int limit, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/universities/regions')
  Future<List<BPRegion>> getRegionsUniversities({@CancelRequest() CancelToken? cancelToken});

  @POST('/api/cards/{id}/delete')
  Future<OperationResponse> deleteMatched({@Path() required int id, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/ads-insights')
  Future<OperationResponse> recordAdsClick({@Body() required Map<String, dynamic> body, @CancelRequest() CancelToken? cancelToken});

  @GET('http://localhost:5337/api/term')
  Future<BPTermsAndConditionModel> getTermsAndConditions({
    @CancelRequest() CancelToken? cancelToken,
  });

  @POST('/api/send-verification-code')
  Future<OtpResponseModel> sendSms({
    @Field() required String phoneNumber,
    @Field() required num userId,
  });

  @POST('/api/verify-code')
  Future<OtpResponseModel> verifySms({
    @Field() required String verificationId,
    @Field() required String verificationCode,
  });

  //-------------------------------------------------------------Starcheck-------------------------------------------------------------------------
  @GET("/api/stars/current")
  Future<HomeModel> currentStar({
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET("/api/stars/past")
  Future<List<StarModel>> pastStar({
    @Query('offset') int? offset,
    @Query('limit') int? limit,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/star-events/{id}')
  Future<StarEventModel> getEvent({
    @Path() required int id,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/star-events/sections')
  Future<AllEventsModel> getEventSections({
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/star-events')
  Future<List<StarEventModel>> getEvents({
    @Query('offset') required int offset,
    @Query('limit') required int limit,
    @Query('exclude') String? exclude,
    @Query('type') String? type,
    @Query('futureLimited') bool? futureLimited,
    @Query('pastLimited') bool? pastLimited,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/star-events/my-past')
  Future<List<StarEventModel>> getMyPastEvents({
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/star-comments/events/{id}')
  Future<List<StarCommentModel>> eventComments({
    @Path() required int id,
    @Query('offset') required int offset,
    @Query('limit') required int limit,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/star-event-members/{id}/members')
  Future<List<BPUserModel>> eventMembers({
    @Path() required int id,
    @Query('offset') required int offset,
    @Query('limit') required int limit,
    @CancelRequest() CancelToken? cancelToken,
  });

  @POST('/api/star-comments/events/{id}')
  Future<StarCommentModel> createEventComment({
    @Path() required int id,
    @Body() required Map<String, dynamic> body,
    @CancelRequest() CancelToken? cancelToken,
  });

  @POST('/api/users/check-fakename')
  Future<dynamic> communityCheckFakeName({@Field() required String fakeName, @CancelRequest() CancelToken? cancelToken});

  @POST('/api/star-event-members/{id}/join')
  Future<StarEventModel> joinEvent({
    @Path() required int id,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/benefits/boards')
  Future<List<BPBenefitsBoard>> getBenefitsBoards({@Query('perm') required BenefitsBoardsType perm, @CancelRequest() CancelToken? cancelToken});

  @GET('/api/benefits/coupons')
  Future<List<BPBenefitsCoupon>> getBenefitsCoupons({
    @Query('offset') int? offset,
    @Query('limit') int? limit,
    @Query('boardId') required int boardId,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/benefits/coupons/{id}')
  Future<BPBenefitsCoupon> getCoupon({
    @Path() required int id,
    @CancelRequest() CancelToken? cancelToken,
  });

  @POST('/api/users/generate-ai-avatar')
  @Extra({'loadingIndicator': false})
  Future<dynamic> generateAIAvatar({
    @Body() required Map<String, dynamic> body,
    @CancelRequest() CancelToken? cancelToken,
  });

  @POST('/api/users/save-ai-avatar')
  Future<BPUserModel> saveAIAvatar({
    @Body() required Map<String, dynamic> body,
    @CancelRequest() CancelToken? cancelToken,
  });

  // 新增异步AI头像生成接口
  @POST('/api/users/generate-ai-avatar-async')
  @Extra({'loadingIndicator': false})
  Future<dynamic> generateAIAvatarAsync({
    @Body() required Map<String, dynamic> body,
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/users/ai-avatar-task/{taskId}')
  @Extra({'loadingIndicator': false})
  Future<dynamic> getAIAvatarTaskStatus({
    @Path() required String taskId,
    @CancelRequest() CancelToken? cancelToken,
  });

  @DELETE('/api/users/ai-avatar-task/{taskId}')
  @Extra({'loadingIndicator': false})
  Future<dynamic> cancelAIAvatarTask({
    @Path() required String taskId,
    @CancelRequest() CancelToken? cancelToken,
  });

  // QR Code related APIs
  @GET('/api/users/myqrcode/me')
  @Extra({'loadingIndicator': false})
  Future<dynamic> getMyQRCode({
    @CancelRequest() CancelToken? cancelToken,
  });

  @GET('/api/users/scan/{userId}')
  @Extra({'loadingIndicator': false})
  Future<dynamic> scanUser({
    @Path() required String userId,
    @CancelRequest() CancelToken? cancelToken,
  });

  // Received Users API
  @GET('/api/users/assetCertification/received-users')
  Future<ReceivedUsersResponse> getReceivedUsers({
    @Query('page') int? page,
    @Query('pageSize') int? pageSize,
    @Query('sort') String? sort,
    @CancelRequest() CancelToken? cancelToken,
  });
}
