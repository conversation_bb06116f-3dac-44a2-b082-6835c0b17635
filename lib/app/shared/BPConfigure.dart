import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'multiple_platform/multiple_platform_tools_interface.dart';

class BPSharedPreferences extends GetxService {
  SharedPreferences? shared;
  Future<BPSharedPreferences> init() async {
    shared ??= await SharedPreferences.getInstance();
    return this;
  }

  static SharedPreferences? get getShared => Get.find<BPSharedPreferences>().shared;
}

class BPConfigure {
  static const signinWithAppleIosClientId = 'com.atreez.starcheck';
  static const signinWithAppleWebClientId = 'com.starcheck.www';
  static const oneSignalAppId = '************************************';

  static bool get isDevelopmentServer {
    if (kIsWeb) {
      final host = MultiplePlatformTools().host() ?? '';
      return host.contains('atreez.com') || host.contains('localhost') || host.contains('127.0.0.1');
    } else {
      return _developmentServerStored ?? kDebugMode;
    }
  }

  static String get serverAPIHost {
    if (isDevelopmentServer) {
      return 'https://starcheck-backend.atreez.com/';
      // return kDebugMode ? 'http://*************:5337' : 'https://starcheck-backend.atreez.com/';
      // return 'http://*************:5337';
    } else {
      return 'https://starcheck-backend.atreez.com/';
    }
  }

  static String get imApiUrl {
    if (isDevelopmentServer) {
      return 'http://*************:10002';
    } else {
      return 'http://*************:10002';
    }
  }

  static String get imWsUrl {
    if (isDevelopmentServer) {
      return 'ws://*************:10001';
    } else {
      return 'ws://*************:10001';
    }
  }

  static const __serverStoredKey = 'starcheck.server.stored.key';
  static bool? get _developmentServerStored {
    return Get.find<BPSharedPreferences>().shared?.getBool(__serverStoredKey);
  }

  static Future<void> setDevelopmentServerStored({required bool value}) async {
    await Get.find<BPSharedPreferences>().shared?.setBool(__serverStoredKey, value);
  }

  static String get webHost => '';
}
