// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();

  static const NOT_FOUND = _Paths.NOT_FOUND;
  static const SIGN_IN = _Paths.SIGN_IN;
  static const SPLASH = _Paths.SPLASH;

  static const PERSONAL_BASIC_INFO = _Paths.PERSONAL_BASIC_INFO;
  static const UNIVERSITY_INFO = _Paths.UNIVERSITY_INFO;
  static const SUBMIT_PHOTOS = _Paths.SUBMIT_PHOTOS;
  static const HOBBIES = _Paths.HOBBIES;
  static const INTRODUCE = _Paths.INTRODUCE;
  static const PREVIEW_PROFILE = _Paths.PREVIEW_PROFILE;
  static const ABOUT_MEETOK = _Paths.ABOUT_MEETOK;

  static const MY_PAGE = _Paths.MY_PAGE;
  static const PARTNER = _Paths.PARTNER;
  static const MEECOIN_HISTORY = _Paths.MEECOIN_HISTORY;
  static const VIEW_ALL_CARDS = _Paths.VIEW_ALL_CARDS;
  static const UNLOCK_PREMIUM_CARDS = _Paths.UNLOCK_PREMIUM_CARDS;
  static const MEECOIN_CHARGING = _Paths.MEECOIN_CHARGING;
  static const PURCHASE_PLAN_COMPARE = _Paths.PURCHASE_PLAN_COMPARE;
  static const BLACK_LIST = _Paths.BLACK_LIST;
  static const REPORT = _Paths.REPORT;
  static const SETTINGS_INDEX = _Paths.SETTINGS_INDEX;
  static const EDIT_PROFILE = _Paths.EDIT_PROFILE;
  static const LANGUAGE = _Paths.LANGUAGE;
  static const INVITATION = _Paths.INVITATION;
  static const DOC_VIEWER = _Paths.DOC_VIEWER;
  static const FEEDBACK = _Paths.FEEDBACK;
  static const QUIT = _Paths.QUIT;
  static const NOTIFICATION = _Paths.NOTIFICATION;
  static const COMMUNITY_IMAGES = _Paths.COMMUNITY_IMAGES;
  static const COMMUNITY_REPORT = _Paths.COMMUNITY_REPORT;
  static const FORKING = _Paths.FORKING;
  static const JOB_MATCH_SPLASH = _Paths.JOB_MATCH_SPLASH;
  static const JM_HOME = _Paths.JM_HOME;
  static const JM_SUGGESTED_JOBS = _Paths.JM_SUGGESTED_JOBS;
  static const JM_JOB_DETAIL = _Paths.JM_JOB_DETAIL;
  static const JM_REPORT = _Paths.JM_REPORT;
  static const COMMUNITY_FEED = _Paths.COMMUNITY_FEED;
  static const COMMUNITY_DETAIL = _Paths.COMMUNITY_DETAIL;
  static const COMMUNITY_PROFILE = _Paths.COMMUNITY_PROFILE;
  static const COMMUNITY_SIGN_UP = _Paths.COMMUNITY_SIGN_UP;
  static const COMMUNITY_CREATE_POST = _Paths.COMMUNITY_CREATE_POST;
  static const AVATAR_PREVIEW = _Paths.AVATAR_PREVIEW;
  static const TERMS_AND_CONDITIONS = _Paths.TERMS_AND_CONDITIONS;
  static const OTP = _Paths.OTP;
  static const MAIN = _Paths.MAIN;
  static const SC_HOME = _Paths.SC_HOME;
  static const EDIT_TEXT = _Paths.EDIT_TEXT;
  static const EVENTS = _Paths.EVENTS;
  static const NORMAL_EVENT_DETAIL = _Paths.NORMAL_EVENT_DETAIL;
  static const BENEFITS = _Paths.BENEFITS;
  static const BENEFIT_DETAIL = _Paths.BENEFIT_DETAIL;
  static const S_C_MY = _Paths.S_C_MY;
  static const SETTINGS = _Paths.SETTINGS;
  static const EVENT_JOIN_COMFIRM = _Paths.EVENT_JOIN_COMFIRM;
  static const EVENT_ALL_COMMENTS = _Paths.EVENT_ALL_COMMENTS;
  static const SPECIAL_GUEST_EVENT_DETAIL = _Paths.SPECIAL_GUEST_EVENT_DETAIL;
  static const EVENT_ALL_MEMBERS = _Paths.EVENT_ALL_MEMBERS;
  static const PAST_SPECIAL_GUEST = _Paths.PAST_SPECIAL_GUEST;
  static const EVENT_JOIN_INTRO = _Paths.EVENT_JOIN_INTRO;
  static const ASSET_VERIFICATION = _Paths.ASSET_VERIFICATION;
  static const ASSET_UNDER_REVIEW = _Paths.ASSET_UNDER_REVIEW;
  static const ASSET_VERIFICATION_SUCCESS = _Paths.ASSET_VERIFICATION_SUCCESS;
  static const AVATAR_GENERATION = _Paths.AVATAR_GENERATION;
}

abstract class _Paths {
  _Paths._();

  static const NOT_FOUND = '/not-found';
  static const SIGN_IN = '/sign-in';
  static const SPLASH = '/splash';

  static const PERSONAL_BASIC_INFO = '/personal-basic-info';
  static const UNIVERSITY_INFO = '/university-info';
  static const SUBMIT_PHOTOS = '/submit-photos';
  static const HOBBIES = '/hobbies';
  static const INTRODUCE = '/introduce';
  static const PREVIEW_PROFILE = '/preview-profile';
  static const ABOUT_MEETOK = '/about-meetok';

  static const MY_PAGE = '/my-page';
  static const PARTNER = '/partner';
  static const MEECOIN_HISTORY = '/meecoin-history';
  static const VIEW_ALL_CARDS = '/view-all-cards';
  static const UNLOCK_PREMIUM_CARDS = '/unlock-premium-cards';
  static const MEECOIN_CHARGING = '/meecoin-charging';
  static const PURCHASE_PLAN_COMPARE = '/purchase-plan-compare';
  static const BLACK_LIST = '/black-list';
  static const REPORT = '/report';
  static const SETTINGS_INDEX = '/settings-index';
  static const EDIT_PROFILE = '/edit-profile';
  static const LANGUAGE = '/language';
  static const INVITATION = '/invitation';
  static const DOC_VIEWER = '/doc-viewer';
  static const FEEDBACK = '/feedback';
  static const QUIT = '/quit';
  static const NOTIFICATION = '/notification';
  static const COMMUNITY_IMAGES = '/community-images';
  static const COMMUNITY_REPORT = '/community-report';
  static const FORKING = '/forking';
  static const JOB_MATCH_SPLASH = '/job-match-splash';
  static const JM_HOME = '/jm-home';
  static const JM_SUGGESTED_JOBS = '/jm-suggested-jobs';
  static const JM_JOB_DETAIL = '/jm-job-detail';
  static const JM_REPORT = '/jm-report';
  static const COMMUNITY_FEED = '/community-feed';
  static const COMMUNITY_DETAIL = '/community-detail';
  static const COMMUNITY_PROFILE = '/community-profile';
  static const COMMUNITY_SIGN_UP = '/community-sign-up';
  static const COMMUNITY_CREATE_POST = '/community-create-post';
  static const AVATAR_PREVIEW = '/avatar-preview';
  static const TERMS_AND_CONDITIONS = '/terms-and-conditions';
  static const OTP = '/otp';
  static const MAIN = '/main';
  static const SC_HOME = '/sc-home';
  static const EDIT_TEXT = '/edit-text';
  static const EVENTS = '/events';
  static const NORMAL_EVENT_DETAIL = '/normal-event-detail';
  static const BENEFITS = '/benefits';
  static const BENEFIT_DETAIL = '/benefit-detail';
  static const S_C_MY = '/s-c-my';
  static const SETTINGS = '/settings';
  static const EVENT_JOIN_COMFIRM = '/event-join-comfirm';
  static const EVENT_ALL_COMMENTS = '/event-all-comments';
  static const SPECIAL_GUEST_EVENT_DETAIL = '/special-guest-event-detail';
  static const EVENT_ALL_MEMBERS = '/event-all-members';
  static const PAST_SPECIAL_GUEST = '/past-special-guest';
  static const EVENT_JOIN_INTRO = '/event-join-intro';
  static const ASSET_VERIFICATION = '/asset-verification';
  static const ASSET_UNDER_REVIEW = '/asset-under-review';
  static const ASSET_VERIFICATION_SUCCESS = '/asset-verification-success';
  static const AVATAR_GENERATION = '/avatar-generation';
}
