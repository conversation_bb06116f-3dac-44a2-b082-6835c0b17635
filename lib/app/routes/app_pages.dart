import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../modules/AssetVerification/bindings/asset_verification_binding.dart';
import '../modules/AssetVerification/views/asset_verification_view.dart';
import '../modules/AssetVerification/views/asset_under_review_page.dart';
import '../modules/AssetVerification/views/asset_verification_success_page.dart';
import '../modules/AvatarGeneration/bindings/avatar_generation_binding.dart';
import '../modules/AvatarGeneration/views/avatar_generation_view.dart';
import '../modules/CommunityImages/bindings/community_images_binding.dart';
import '../modules/CommunityImages/views/community_images_view.dart';
import '../modules/CommunityReport/bindings/community_report_binding.dart';
import '../modules/CommunityReport/views/community_report_view.dart';
import '../modules/CommunityV2/AvatarPreview/bindings/avatar_preview_binding.dart';
import '../modules/CommunityV2/AvatarPreview/views/avatar_preview_view.dart';
import '../modules/CommunityV2/CommunityCreatePost/bindings/community_create_post_binding.dart';
import '../modules/CommunityV2/CommunityCreatePost/views/community_create_post_view.dart';
import '../modules/CommunityV2/CommunityDetail/bindings/community_detail_binding.dart';
import '../modules/CommunityV2/CommunityDetail/views/community_detail_view.dart';
import '../modules/CommunityV2/CommunityFeed/bindings/community_feed_binding.dart';
import '../modules/CommunityV2/CommunityFeed/views/community_feed_view.dart';
import '../modules/CommunityV2/CommunityProfile/bindings/community_profile_binding.dart';
import '../modules/CommunityV2/CommunityProfile/views/community_profile_view.dart';
import '../modules/CommunityV2/CommunitySignUp/bindings/community_sign_up_binding.dart';
import '../modules/CommunityV2/CommunitySignUp/views/community_sign_up_view.dart';
import '../modules/CreatingAccount/AboutMeetok/bindings/about_meetok_binding.dart';
import '../modules/CreatingAccount/AboutMeetok/views/about_meetok_view.dart';
import '../modules/CreatingAccount/Hobbies/bindings/hobbies_binding.dart';
import '../modules/CreatingAccount/Hobbies/views/hobbies_view.dart';
import '../modules/CreatingAccount/Introduce/bindings/introduce_binding.dart';
import '../modules/CreatingAccount/Introduce/views/introduce_view.dart';
import '../modules/CreatingAccount/Invitation/bindings/invitation_binding.dart';
import '../modules/CreatingAccount/Invitation/views/invitation_view.dart';
import '../modules/CreatingAccount/PersonalBasicInfo/bindings/personal_basic_info_binding.dart';
import '../modules/CreatingAccount/PersonalBasicInfo/views/personal_basic_info_view.dart';
import '../modules/CreatingAccount/PreviewProfile/bindings/preview_profile_binding.dart';
import '../modules/CreatingAccount/PreviewProfile/views/preview_profile_view.dart';
import '../modules/CreatingAccount/SignIn/bindings/sign_in_binding.dart';
import '../modules/CreatingAccount/SignIn/views/sign_in_view.dart';
import '../modules/CreatingAccount/SubmitPhotos/bindings/submit_photos_binding.dart';
import '../modules/CreatingAccount/SubmitPhotos/views/submit_photos_view.dart';
import '../modules/CreatingAccount/UniversityInfo/bindings/university_info_binding.dart';
import '../modules/CreatingAccount/UniversityInfo/views/university_info_view.dart';
import '../modules/DocViewer/bindings/doc_viewer_binding.dart';
import '../modules/DocViewer/views/doc_viewer_view.dart';
import '../modules/EditProfile/bindings/edit_profile_binding.dart';
import '../modules/EditProfile/views/edit_profile_view.dart';
import '../modules/Feedback/bindings/feedback_binding.dart';
import '../modules/Feedback/views/feedback_view.dart';
import '../modules/Meecoin/MeecoinCharging/bindings/meecoin_charging_binding.dart';
import '../modules/Meecoin/MeecoinCharging/views/meecoin_charging_view.dart';
import '../modules/Meecoin/MeecoinHistory/bindings/meecoin_history_binding.dart';
import '../modules/Meecoin/MeecoinHistory/views/meecoin_history_view.dart';
import '../modules/Meecoin/PurchasePlanCompare/bindings/purchase_plan_compare_binding.dart';
import '../modules/Meecoin/PurchasePlanCompare/views/purchase_plan_compare_view.dart';
import '../modules/MyPage/bindings/my_page_binding.dart';
import '../modules/MyPage/views/my_page_view.dart';
import '../modules/NotFound/bindings/not_found_binding.dart';
import '../modules/NotFound/views/not_found_view.dart';
import '../modules/Notification/bindings/notification_binding.dart';
import '../modules/Notification/views/notification_view.dart';
import '../modules/OtpVerify/bindings/otp_verify_page_binding.dart';
import '../modules/OtpVerify/pages/otp_verify_page.dart';
import '../modules/Partner/bindings/partner_binding.dart';
import '../modules/Partner/views/partner_view.dart';
import '../modules/Quit/bindings/quit_binding.dart';
import '../modules/Quit/views/quit_view.dart';
import '../modules/Report/bindings/report_binding.dart';
import '../modules/Report/views/report_view.dart';
import '../modules/Settings/BlackList/bindings/black_list_binding.dart';
import '../modules/Settings/BlackList/views/black_list_view.dart';
import '../modules/Settings/Language/bindings/language_binding.dart';
import '../modules/Settings/Language/views/language_view.dart';
import '../modules/Settings/SettingsIndex/bindings/settings_index_binding.dart';
import '../modules/Settings/SettingsIndex/views/settings_index_view.dart';
import '../modules/Splash/bindings/splash_binding.dart';
import '../modules/Splash/views/splash_view.dart';
import '../modules/TermsAndConditions/bindings/terms_and_condition_page_binding.dart';
import '../modules/TermsAndConditions/views/terms_and_condition_page.dart';
import '../modules/UnlockPurchase/UnlockPremiumCards/bindings/unlock_premium_cards_binding.dart';
import '../modules/UnlockPurchase/UnlockPremiumCards/views/unlock_premium_cards_view.dart';
import '../modules/ViewAllCards/bindings/view_all_cards_binding.dart';
import '../modules/ViewAllCards/views/view_all_cards_view.dart';
import '../modules/ascm/BenefitDetail/bindings/benefit_detail_binding.dart';
import '../modules/ascm/BenefitDetail/views/benefit_detail_view.dart';
import '../modules/ascm/Benefits/bindings/benefits_binding.dart';
import '../modules/ascm/Benefits/views/benefits_view.dart';
import '../modules/ascm/EditText/bindings/edit_text_binding.dart';
import '../modules/ascm/EditText/views/edit_text_view.dart';
import '../modules/ascm/EventAllComments/bindings/event_all_comments_binding.dart';
import '../modules/ascm/EventAllComments/views/event_all_comments_view.dart';
import '../modules/ascm/EventAllMembers/bindings/event_all_members_binding.dart';
import '../modules/ascm/EventAllMembers/views/event_all_members_view.dart';
import '../modules/ascm/EventJoinComfirm/bindings/event_join_comfirm_binding.dart';
import '../modules/ascm/EventJoinComfirm/views/event_join_comfirm_view.dart';
import '../modules/ascm/Events/bindings/events_binding.dart';
import '../modules/ascm/Events/views/events_view.dart';
import '../modules/ascm/Main/bindings/main_binding.dart';
import '../modules/ascm/Main/views/main_view.dart';
import '../modules/ascm/NormalEventDetail/bindings/normal_event_detail_binding.dart';
import '../modules/ascm/NormalEventDetail/views/normal_event_detail_view.dart';
import '../modules/ascm/PastSpecialGuest/bindings/past_special_guest_binding.dart';
import '../modules/ascm/PastSpecialGuest/views/past_special_guest_view.dart';
import '../modules/ascm/SCMy/bindings/s_c_my_binding.dart';
import '../modules/ascm/SCMy/views/s_c_my_view.dart';
import '../modules/ascm/ScHome/bindings/sc_home_binding.dart';
import '../modules/ascm/ScHome/views/sc_home_view.dart';
import '../modules/ascm/Settings/bindings/settings_binding.dart';
import '../modules/ascm/Settings/views/settings_view.dart';
import '../modules/ascm/SpecialGuestEventDetail/bindings/special_guest_event_detail_binding.dart';
import '../modules/ascm/SpecialGuestEventDetail/views/special_guest_event_detail_view.dart';
import '../modules/eventJoinIntro/bindings/event_join_intro_binding.dart';
import '../modules/eventJoinIntro/views/event_join_intro_view.dart';
import '../shared/BPAPPFork.dart';
import '../shared/account/BPSessionManager.dart';

// ignore_for_file: unused_local_variable, constant_identifier_names

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: _Paths.NOT_FOUND,
      page: () => const NotFoundView(),
      binding: NotFoundBinding(),
    ),
    GetPage(
      name: _Paths.SIGN_IN,
      page: () => const SignInView(),
      binding: SignInBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.PERSONAL_BASIC_INFO,
      page: () => const PersonalBasicInfoView(),
      binding: PersonalBasicInfoBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.UNIVERSITY_INFO,
      page: () => const UniversityInfoView(),
      binding: UniversityInfoBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.SUBMIT_PHOTOS,
      page: () => const SubmitPhotosView(),
      binding: SubmitPhotosBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.HOBBIES,
      page: () => const HobbiesView(),
      binding: HobbiesBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.INTRODUCE,
      page: () => const IntroduceView(),
      binding: IntroduceBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.PREVIEW_PROFILE,
      page: () => const PreviewProfileView(),
      binding: PreviewProfileBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.ABOUT_MEETOK,
      page: () => const AboutMeetokView(),
      binding: AboutMeetokBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.MY_PAGE,
      page: () => const MyPageView(),
      binding: MyPageBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.PARTNER,
      page: () => const PartnerView(),
      binding: PartnerBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.MEECOIN_HISTORY,
      page: () => const MeecoinHistoryView(),
      binding: MeecoinHistoryBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.VIEW_ALL_CARDS,
      page: () => const ViewAllCardsView(),
      binding: ViewAllCardsBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.UNLOCK_PREMIUM_CARDS,
      page: () => const UnlockPremiumCardsView(),
      binding: UnlockPremiumCardsBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.MEECOIN_CHARGING,
      page: () => const MeecoinChargingView(),
      binding: MeecoinChargingBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.PURCHASE_PLAN_COMPARE,
      page: () => const PurchasePlanCompareView(),
      binding: PurchasePlanCompareBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.BLACK_LIST,
      page: () => const BlackListView(),
      binding: BlackListBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.REPORT,
      page: () => const ReportView(),
      binding: ReportBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.SETTINGS_INDEX,
      page: () => const SettingsIndexView(),
      binding: SettingsIndexBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.EDIT_PROFILE,
      page: () => const EditProfileView(),
      binding: EditProfileBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.LANGUAGE,
      page: () => const LanguageView(),
      binding: LanguageBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.INVITATION,
      page: () => const InvitationView(),
      binding: InvitationBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.DOC_VIEWER,
      page: () => const DocViewerView(),
      binding: DocViewerBinding(),
      // middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.FEEDBACK,
      page: () => const FeedbackView(),
      binding: FeedbackBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.QUIT,
      page: () => const QuitView(),
      binding: QuitBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.NOTIFICATION,
      page: () => const NotificationView(),
      binding: NotificationBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.COMMUNITY_IMAGES,
      page: () => const CommunityImagesView(),
      binding: CommunityImagesBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.COMMUNITY_REPORT,
      page: () => const CommunityReportView(),
      binding: CommunityReportBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.COMMUNITY_FEED,
      page: () => const CommunityFeedView(),
      binding: CommunityFeedBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.COMMUNITY_DETAIL,
      page: () => const CommunityDetailView(),
      binding: CommunityDetailBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.COMMUNITY_PROFILE,
      page: () => const CommunityProfileView(),
      binding: CommunityProfileBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.COMMUNITY_SIGN_UP,
      page: () => const CommunitySignUpView(),
      binding: CommunitySignUpBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.COMMUNITY_CREATE_POST,
      page: () => const CommunityCreatePostView(),
      binding: CommunityCreatePostBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.AVATAR_PREVIEW,
      page: () => const AvatarPreviewView(),
      binding: AvatarPreviewBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.TERMS_AND_CONDITIONS,
      page: () => const TermsAndConditionsPage(),
      binding: TermsAndConditionsPageBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.OTP,
      page: () => const OtpVerifyPage(),
      binding: OtpVerifyPageBinding(),
      middlewares: [SignInMiddleware()],
    ),
    GetPage(
      name: _Paths.MAIN,
      page: () => const MainView(),
      binding: MainBinding(),
    ),
    GetPage(
      name: _Paths.SC_HOME,
      page: () => const ScHomeView(),
      binding: ScHomeBinding(),
    ),
    GetPage(
      name: _Paths.EDIT_TEXT,
      page: () => const EditTextView(),
      binding: EditTextBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS,
      page: () => const EventsView(),
      binding: EventsBinding(),
    ),
    GetPage(
      name: _Paths.NORMAL_EVENT_DETAIL,
      page: () => const NormalEventDetailView(),
      binding: NormalEventDetailBinding(),
    ),
    GetPage(
      name: _Paths.BENEFITS,
      page: () => const BenefitsView(),
      binding: BenefitsBinding(),
    ),
    GetPage(
      name: _Paths.BENEFIT_DETAIL,
      page: () => const BenefitDetailView(),
      binding: BenefitDetailBinding(),
    ),
    GetPage(
      name: _Paths.S_C_MY,
      page: () => const SCMyView(),
      binding: SCMyBinding(),
    ),
    GetPage(
      name: _Paths.SETTINGS,
      page: () => const SettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: _Paths.EVENT_JOIN_COMFIRM,
      page: () => const EventJoinComfirmView(),
      binding: EventJoinComfirmBinding(),
    ),
    GetPage(
      name: _Paths.EVENT_ALL_COMMENTS,
      page: () => const EventAllCommentsView(),
      binding: EventAllCommentsBinding(),
    ),
    GetPage(
      name: _Paths.SPECIAL_GUEST_EVENT_DETAIL,
      page: () => const SpecialGuestEventDetailView(),
      binding: SpecialGuestEventDetailBinding(),
    ),
    GetPage(
      name: _Paths.EVENT_ALL_MEMBERS,
      page: () => const EventAllMembersView(),
      binding: EventAllMembersBinding(),
    ),
    GetPage(
      name: _Paths.PAST_SPECIAL_GUEST,
      page: () => const PastSpecialGuestView(),
      binding: PastSpecialGuestBinding(),
    ),
    GetPage(
      name: _Paths.EVENT_JOIN_INTRO,
      page: () => const EventJoinIntroView(),
      binding: EventJoinIntroBinding(),
    ),
    GetPage(
      name: _Paths.ASSET_VERIFICATION,
      page: () => const AssetVerificationView(),
      binding: AssetVerificationBinding(),
    ),
    GetPage(
      name: _Paths.ASSET_UNDER_REVIEW,
      page: () => const AssetUnderReviewPage(),
    ),
    GetPage(
      name: _Paths.ASSET_VERIFICATION_SUCCESS,
      page: () => const AssetVerificationSuccessPage(),
    ),
    GetPage(
      name: _Paths.AVATAR_GENERATION,
      page: () => const AvatarGenerationView(),
      binding: AvatarGenerationBinding(),
    ),
  ];
}

class SignInMiddleware extends GetMiddleware {
  final bool restoreTask;
  final bool talentOnly;
  final Map<String, String>? Function(String? route)? parametersCallback;

  SignInMiddleware(
      {this.restoreTask = false,
      this.talentOnly = false,
      this.parametersCallback,
      super.priority});
  @override
  RouteSettings? redirect(String? route) {
    if (kDebugMode) {
      // debugPrint('SignInMiddleware:$route');
    }
    if (!BPSessionManager.instance.isAccountSignedIn) {
      if (restoreTask) {
        // BPLaunching.setPendingTaskDependSession(value: route);
      }
      Map<String, String> parameters = {};
      if (parametersCallback != null) {
        parameters = parametersCallback!(route) ?? {};
      }

      return GetPage(
        name: _Paths.SPLASH,
        page: () => const SplashView(),
        binding: SplashBinding(),
      );
    }
    return null;
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    // // debugPrint('onPageCalled: ${page?.name ?? ''}');
    return page;
  }
}
