import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../generated/locales.g.dart';
import 'BPUserModels.dart';
part 'BPAccountModels.g.dart';

@JsonSerializable()
class BPAccountSession {
  final BPAccountCredential accountCredential;
  BPUserModel accountModel;
  final String jwt;
  BPAccountSession(this.accountCredential, this.accountModel, this.jwt);
  factory BPAccountSession.fromJson(Map<String, dynamic> srcJson) => _$BPAccountSessionFromJson(srcJson);
  Map<String, dynamic> toJson() => _$BPAccountSessionToJson(this);
}

enum BPAccountType {
  @JsonValue('zalo')
  zalo,
  @JsonValue('google')
  google,
  @JsonValue('facebook')
  facebook,
  @JsonValue('apple')
  apple,
  unknown
}

enum BPUserGender {
  @JsonValue('M')
  male,
  @JsonValue('F')
  female,
  @JsonValue('unknown')
  unknown
}

enum BPVerificationType {
  @JsonValue('email')
  email,
  @JsonValue('certificate')
  certificate
}

enum BPVerificationStatus {
  @JsonValue('incomplete')
  incomplete,
  @JsonValue('pending')
  pending,
  @JsonValue('not_verified_yet')
  notVerifiedYet,
  @JsonValue('verified')
  verified,
  @JsonValue('rejected')
  rejected
}

enum BPFinancialStatus {
  @JsonValue('doNotAuthorize')
  doNotAuthorize,
  @JsonValue('submitted')
  submitted,
  @JsonValue('moreThan100K')
  moreThan100K,
  @JsonValue('moreThan500K')
  moreThan500K,
  @JsonValue('moreThan1M')
  moreThan1M,
  @JsonValue('moreThan5M')
  moreThan5M,
  @JsonValue('moreThan10M')
  moreThan10M,
  @JsonValue('moreThan50M')
  moreThan50M,
  @JsonValue('moreThan100M')
  moreThan100M;

  String get localeName {
    switch (this) {
      case BPFinancialStatus.doNotAuthorize:
        return LocaleKeys.university_info_financial_status_doNotAuthorize.tr;
      case BPFinancialStatus.submitted:
        return LocaleKeys.university_info_financial_status_submitted.tr;
      case BPFinancialStatus.moreThan100K:
        return LocaleKeys.university_info_financial_status_moreThan100K.tr;
      case BPFinancialStatus.moreThan500K:
        return LocaleKeys.university_info_financial_status_moreThan500K.tr;
      case BPFinancialStatus.moreThan1M:
        return LocaleKeys.university_info_financial_status_moreThan1M.tr;
      case BPFinancialStatus.moreThan5M:
        return LocaleKeys.university_info_financial_status_moreThan5M.tr;
      case BPFinancialStatus.moreThan10M:
        return LocaleKeys.university_info_financial_status_moreThan10M.tr;
      case BPFinancialStatus.moreThan50M:
        return LocaleKeys.university_info_financial_status_moreThan50M.tr;
      case BPFinancialStatus.moreThan100M:
        return LocaleKeys.university_info_financial_status_moreThan100M.tr;
    }
  }
}

@JsonSerializable()
class BPAccountCredential extends Object {
  @JsonKey(defaultValue: '')
  String identifier;
  @JsonKey(defaultValue: BPAccountType.unknown, unknownEnumValue: BPAccountType.unknown)
  BPAccountType accountType;
  @JsonKey(defaultValue: '')
  String password;
  BPAccountCredential({required this.identifier, required this.accountType, required this.password});
  factory BPAccountCredential.fromJson(Map<String, dynamic> srcJson) => _$BPAccountCredentialFromJson(srcJson);
  Map<String, dynamic> toJson() => _$BPAccountCredentialToJson(this);
}

@JsonSerializable()
class BPAccountSignedInModel {
  @JsonKey(defaultValue: '')
  String jwt;
  BPUserModel user;
  BPAccountSignedInModel(this.jwt, this.user);
  factory BPAccountSignedInModel.fromJson(Map<String, dynamic> srcJson) => _$BPAccountSignedInModelFromJson(srcJson);
  Map<String, dynamic> toJson() => _$BPAccountSignedInModelToJson(this);
}


