// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';

import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:starchex/app/data/AssetVerificationModel.dart';

import '../../generated/locales.g.dart';
import 'BPAccountModels.dart';
import 'BPCardModels.dart';
import 'BPCommonModels.dart';
import 'BPInterestModels.dart';
import 'BPUniversityModels.dart';
import 'scdata/mv_star_comment_model.dart';
import 'scdata/mv_star_event_model.dart';

part 'BPUserModels.g.dart';

@JsonSerializable()
class BPLikeMode {
  @JsonKey(includeIfNull: false, unknownEnumValue: BPLikeLevel.unknown)
  BPLikeLevel? level;
  @JsonKey(defaultValue: 0)
  int price;

  BPLikeMode({this.level, required this.price});
  factory BPLikeMode.fromJson(Map<String, dynamic> srcJson) => _$BPLikeModeFromJson(srcJson);
  Map<String, dynamic> toJson() => _$BPLikeModeToJson(this);
}

@JsonSerializable()
class BPUserData {
  @JsonKey(includeIfNull: false)
  bool? hearted;
  @JsonKey(unknownEnumValue: BPLikeLevel.unknown)
  BPLikeLevel? heartedLevel;
  @JsonKey(includeIfNull: false)
  bool? rated;
  @JsonKey(defaultValue: 0)
  double ratingScore;
  @JsonKey(defaultValue: 0)
  double? ratingBonus;
  @JsonKey(defaultValue: [])
  List<BPLikeMode> heartList;
  @JsonKey(defaultValue: false)
  bool subscribed;
  @JsonKey(defaultValue: [])
  List<BPSubscriptionModel>? subscriptionPlans;
  @JsonKey(includeIfNull: false)
  bool? gotHeart;
  @JsonKey(includeIfNull: false)
  BPLikeLevel? gotLevel;
  @JsonKey(includeIfNull: false)
  bool? matched;
  @JsonKey(includeIfNull: false)
  bool? rejected;
  @JsonKey(includeIfNull: false, defaultValue: null)
  String? rejectedReason;

  BPUserData({this.hearted, this.heartedLevel, this.rated, required this.ratingScore, this.ratingBonus, required this.heartList, required this.subscribed, this.gotHeart, this.gotLevel, this.matched});

  factory BPUserData.fromJson(Map<String, dynamic> srcJson) => _$BPUserDataFromJson(srcJson);
  Map<String, dynamic> toJson() => _$BPUserDataToJson(this);

  BPLikeMode? get lowLevelLike => heartList.firstWhereOrNull((element) => element.level == BPLikeLevel.low);
  BPLikeMode? get highLevelLike => heartList.firstWhereOrNull((element) => element.level == BPLikeLevel.high);
}

@JsonSerializable()
class BPSubscriptionModel {
  @JsonKey(defaultValue: 0)
  int id;
  @JsonKey(defaultValue: '')
  String name;
  @JsonKey(defaultValue: '')
  String description;
  @JsonKey(defaultValue: 0)
  int price;
  @JsonKey(defaultValue: 1)
  int? status;
  @JsonKey(defaultValue: BPLikeLevel.low)
  BPLikeLevel? level;

  BPSubscriptionModel({required this.id, required this.name, required this.description, required this.price, this.status, this.level});
  factory BPSubscriptionModel.fromJson(Map<String, dynamic> srcJson) => _$BPSubscriptionModelFromJson(srcJson);
  Map<String, dynamic> toJson() => _$BPSubscriptionModelToJson(this);
}

enum BPGraduateStatus {
  @JsonValue('GRADUATE')
  graduate,
  @JsonValue('UNDERGRADUATE')
  undergraduate,
  @JsonValue('NONE')
  none
}

enum BPPhoneVerificationStatus {
  @JsonValue('no')
  no,
  @JsonValue('pass')
  pass,
  @JsonValue('done')
  done,
}

enum BPUserRole {
  @JsonValue('talent')
  talent,
  @JsonValue('business')
  business
}

enum AssetResult {
  @JsonValue('moreThan100K')
  moreThan100K,
  @JsonValue('moreThan500K')
  moreThan500K,
  @JsonValue('moreThan1M')
  moreThan1M,
  @JsonValue('moreThan5M')
  moreThan5M,
  @JsonValue('moreThan10M')
  moreThan10M,
  @JsonValue('moreThan50M')
  moreThan50M,
  @JsonValue('moreThan100M')
  moreThan100M,
}

@JsonSerializable()
class ReviewUser {
  @JsonKey(defaultValue: 0)
  int id;
  @JsonKey(includeIfNull: false)
  String? firstname;
  @JsonKey(includeIfNull: false)
  String? lastname;
  @JsonKey(includeIfNull: false)
  BPURLModel? avatar;

  ReviewUser({
    required this.id,
    this.firstname,
    this.lastname,
    this.avatar,
  });

  factory ReviewUser.fromJson(Map<String, dynamic> srcJson) => _$ReviewUserFromJson(srcJson);
  Map<String, dynamic> toJson() => _$ReviewUserToJson(this);
}

@JsonSerializable()
class AssetCertificationModel {
  @JsonKey(defaultValue: 0)
  int id;
  @JsonKey(includeIfNull: false)
  DateTime? issuedate;
  @JsonKey(includeIfNull: false)
  DateTime? expirydate;
  @JsonKey(includeIfNull: false)
  DateTime? createdAt;
  @JsonKey(includeIfNull: false)
  DateTime? updatedAt;
  @JsonKey(includeIfNull: false, name: 'asset_verifications')
  List<AssetVerificationModel>? assetVerifications;
  @JsonKey(includeIfNull: false, name: 'asset_result')
  AssetResult? assetResult;
  @JsonKey(includeIfNull: false, name: 'review_users')
  List<ReviewUser>? reviewUsers;

  AssetCertificationModel({
    required this.id,
    this.issuedate,
    this.expirydate,
    this.createdAt,
    this.updatedAt,
    this.assetVerifications,
    this.assetResult,
    this.reviewUsers,
  });

  factory AssetCertificationModel.fromJson(Map<String, dynamic> srcJson) => _$AssetCertificationModelFromJson(srcJson);
  Map<String, dynamic> toJson() => _$AssetCertificationModelToJson(this);
}

@JsonSerializable()
class ReceivedUserModel {
  @JsonKey(defaultValue: 0)
  int id;
  @JsonKey(includeIfNull: false)
  DateTime? scannedAt;
  @JsonKey(includeIfNull: false)
  BPUserModel? user;

  ReceivedUserModel({
    required this.id,
    this.scannedAt,
    this.user,
  });

  factory ReceivedUserModel.fromJson(Map<String, dynamic> srcJson) => _$ReceivedUserModelFromJson(srcJson);
  Map<String, dynamic> toJson() => _$ReceivedUserModelToJson(this);
}

@JsonSerializable()
class ReceivedUsersResponse {
  @JsonKey(defaultValue: [])
  List<ReceivedUserModel> data;
  @JsonKey(includeIfNull: false)
  PaginationMeta? meta;

  ReceivedUsersResponse({
    required this.data,
    this.meta,
  });

  factory ReceivedUsersResponse.fromJson(Map<String, dynamic> srcJson) => _$ReceivedUsersResponseFromJson(srcJson);
  Map<String, dynamic> toJson() => _$ReceivedUsersResponseToJson(this);
}

@JsonSerializable()
class PaginationMeta {
  @JsonKey(includeIfNull: false)
  PaginationInfo? pagination;

  PaginationMeta({
    this.pagination,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> srcJson) => _$PaginationMetaFromJson(srcJson);
  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

@JsonSerializable()
class PaginationInfo {
  @JsonKey(defaultValue: 1)
  int page;
  @JsonKey(defaultValue: 20)
  int pageSize;
  @JsonKey(defaultValue: 1)
  int pageCount;
  @JsonKey(defaultValue: 0)
  int total;
  @JsonKey(defaultValue: false)
  bool hasNextPage;
  @JsonKey(defaultValue: false)
  bool hasPreviousPage;

  PaginationInfo({
    required this.page,
    required this.pageSize,
    required this.pageCount,
    required this.total,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> srcJson) => _$PaginationInfoFromJson(srcJson);
  Map<String, dynamic> toJson() => _$PaginationInfoToJson(this);
}

@JsonSerializable()
class BPUserModel {
  @JsonKey(defaultValue: 0)
  int id;
  @JsonKey(includeIfNull: false)
  String? username;
  @JsonKey(includeIfNull: false)
  String? fullname;
  @JsonKey(includeIfNull: false)
  String? title;
  @JsonKey(includeIfNull: false)
  String? email;
  @JsonKey(includeIfNull: false)
  String? verificationEmail;
  @JsonKey(includeIfNull: false)
  String? phone;
  @JsonKey(includeIfNull: false)
  DateTime? birthday;
  @JsonKey(includeIfNull: false)
  BPUserGender? gender;
  @JsonKey(includeIfNull: false)
  BPVerificationType? verificationType;
  @JsonKey(includeIfNull: false)
  BPUniversity? university;
  @JsonKey(includeIfNull: false)
  BPURLModel? avatar;
  @JsonKey(includeIfNull: false)
  bool? agreed;
  @JsonKey(includeIfNull: false)
  DateTime? emailSentAt;
  @JsonKey(includeIfNull: false)
  BPVerificationStatus? verificationStatus;
  // @JsonKey(includeIfNull: false, defaultValue: false)
  // bool? phoneVerified;
  @JsonKey(includeIfNull: false)
  BPFinancialStatus? financialStatus;
  @JsonKey(includeIfNull: false)
  String? rejectionReason;
  @JsonKey(includeIfNull: true)
  List<BPURLModel>? certificateList;
  @JsonKey(includeIfNull: false)
  List<BPURLModel>? photos;
  @JsonKey(includeIfNull: false)
  List<BPInterest>? interests;
  @JsonKey(includeIfNull: false)
  String? location;
  @JsonKey(includeIfNull: false)
  double? latitude;
  @JsonKey(includeIfNull: false)
  double? longitude;
  @JsonKey(includeIfNull: false)
  String? introduction;
  @JsonKey(includeIfNull: false)
  int? balance;
  @JsonKey(includeIfNull: false)
  BPUserData? data;
  @JsonKey(includeIfNull: false)
  String? referralCode;
  @JsonKey(includeIfNull: false)
  bool? confirmed;
  @JsonKey(includeIfNull: false)
  String? regionCode;
  @JsonKey(includeIfNull: false)
  String? countryCode;
  @JsonKey(includeIfNull: false)
  String? nationalityCode;
  @JsonKey(includeIfNull: false)
  BPGraduateStatus? status;
  @JsonKey(includeIfNull: false, defaultValue: BPPhoneVerificationStatus.pass)
  BPPhoneVerificationStatus? phoneVerificationStatus;
  @JsonKey(includeIfNull: false)
  String? fakeName;
  @JsonKey(includeIfNull: false)
  String? fakeRandomAvatar;
  @JsonKey(includeIfNull: false)
  BPURLModel? fakeAvatar;
  @JsonKey(includeIfNull: false)
  bool? isWhitelisted;
  @JsonKey(includeIfNull: false, defaultValue: BPUserRole.talent, unknownEnumValue: BPUserRole.talent)
  BPUserRole? type;
  @JsonKey(includeIfNull: false)
  String? regRefCode;
  @JsonKey(includeIfNull: false)
  bool? visibility;
  @JsonKey(includeIfNull: false)
  String? code;
  @JsonKey(includeIfNull: false)
  bool? firstTimeUpdateProfile;
  @JsonKey(includeIfNull: false, includeFromJson: false)
  DateTime? createdAt;
  @JsonKey(includeIfNull: false, includeFromJson: false)
  DateTime? updatedAt;
  @JsonKey(includeIfNull: false)
  int? level;
  @JsonKey(includeIfNull: false)
  BPURLModel? splashImage;
  @JsonKey(includeIfNull: false)
  BPURLModel? idcard;
  @JsonKey(includeIfNull: false, name: 'asset_verifications')
  List<AssetVerificationModel>? assetVerifications;
  @JsonKey(includeIfNull: false, name: 'asset_certification')
  AssetCertificationModel? assetCertification;

  BPUserModel({required this.id, this.username, this.fullname, this.title, this.email, this.verificationEmail, this.phone, this.birthday, this.regionCode, this.gender, this.verificationType, this.financialStatus, this.university, this.avatar, this.agreed, this.emailSentAt, this.verificationStatus, this.rejectionReason, this.photos, this.interests, this.location, this.latitude, this.longitude, this.introduction, this.balance, this.data, this.referralCode, this.confirmed, this.countryCode, this.phoneVerificationStatus, this.status, this.fakeName, this.isWhitelisted, this.type, this.regRefCode, this.visibility, this.code, this.nationalityCode, this.certificateList, this.firstTimeUpdateProfile, this.fakeRandomAvatar, this.fakeAvatar, this.level, this.splashImage, this.idcard, this.assetVerifications, this.assetCertification});
  factory BPUserModel.fromJson(Map<String, dynamic> srcJson) => _$BPUserModelFromJson(srcJson);
  Map<String, dynamic> toJson() => _$BPUserModelToJson(this);

  int get _yearOld => birthday != null ? max((DateTime.now().difference(birthday!).inDays / 365.0).round(), 1) : 0;

  String get ageText {
    if (_yearOld > 0) {
      return '$_yearOld';
    }
    return '';
  }

  String get genderText => gender != null ? (gender == BPUserGender.female ? LocaleKeys.personal_basic_info_female.tr : LocaleKeys.personal_basic_info_male.tr) : '';

  BPUserModel copyWith({
    int? id,
    String? username,
    String? title,
    String? fullname,
    String? email,
    String? verificationEmail,
    String? phone,
    DateTime? birthday,
    BPUserGender? gender,
    BPVerificationType? verificationType,
    BPUniversity? university,
    BPURLModel? avatar,
    bool? agreed,
    DateTime? emailSentAt,
    BPVerificationStatus? verificationStatus,
    BPFinancialStatus? financialStatus,
    String? rejectionReason,
    BPURLModel? certificate,
    List<BPURLModel>? certificateList,
    List<BPURLModel>? photos,
    List<BPInterest>? interests,
    String? location,
    double? latitude,
    double? longitude,
    String? introduction,
    int? balance,
    BPUserData? data,
    String? referralCode,
    bool? confirmed,
    String? regionCode,
    String? countryCode,
    String? nationalityCode,
    BPGraduateStatus? status,
    BPPhoneVerificationStatus? phoneVerificationStatus,
    String? fakeName,
    bool? isWhitelisted,
    BPUserRole? type,
    String? regRefCode,
    bool? visibility,
    String? code,
    BPURLModel? splashImage,
    AssetCertificationModel? assetCertification,
  }) {
    return BPUserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      fullname: fullname ?? this.fullname,
      title: title ?? this.title,
      email: email ?? this.email,
      verificationEmail: verificationEmail ?? this.verificationEmail,
      phone: phone ?? this.phone,
      birthday: birthday ?? this.birthday,
      gender: gender ?? this.gender,
      verificationType: verificationType ?? this.verificationType,
      university: university ?? this.university,
      avatar: avatar ?? this.avatar,
      agreed: agreed ?? this.agreed,
      emailSentAt: emailSentAt ?? this.emailSentAt,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      financialStatus: financialStatus ?? this.financialStatus,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      certificateList: certificateList ?? this.certificateList,
      photos: photos ?? this.photos,
      interests: interests ?? this.interests,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      introduction: introduction ?? this.introduction,
      balance: balance ?? this.balance,
      data: data ?? this.data,
      referralCode: referralCode ?? this.referralCode,
      confirmed: confirmed ?? this.confirmed,
      regionCode: regionCode ?? this.regionCode,
      countryCode: countryCode ?? this.countryCode,
      nationalityCode: nationalityCode ?? this.nationalityCode,
      status: status ?? this.status,
      phoneVerificationStatus: phoneVerificationStatus ?? this.phoneVerificationStatus,
      fakeName: fakeName ?? this.fakeName,
      isWhitelisted: isWhitelisted ?? this.isWhitelisted,
      type: type ?? this.type,
      regRefCode: regRefCode ?? this.regRefCode,
      visibility: visibility ?? this.visibility,
      code: code ?? this.code,
      splashImage: splashImage ?? this.splashImage,
      assetCertification: assetCertification ?? this.assetCertification,
    );
  }
}
