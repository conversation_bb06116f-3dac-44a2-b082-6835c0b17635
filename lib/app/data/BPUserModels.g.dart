// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'BPUserModels.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BPLikeMode _$BPLikeModeFromJson(Map<String, dynamic> json) => BPLikeMode(
      level: $enumDecodeNullable(_$BPLikeLevelEnumMap, json['level'],
          unknownValue: BPLikeLevel.unknown),
      price: (json['price'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$BPLikeModeToJson(BPLikeMode instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('level', _$BPLikeLevelEnumMap[instance.level]);
  val['price'] = instance.price;
  return val;
}

const _$BPLikeLevelEnumMap = {
  BPLikeLevel.high: 'high',
  BPLikeLevel.low: 'low',
  BPLikeLevel.unknown: 'unknown',
};

BPUserData _$BPUserDataFromJson(Map<String, dynamic> json) => BPUserData(
      hearted: json['hearted'] as bool?,
      heartedLevel: $enumDecodeNullable(
          _$BPLikeLevelEnumMap, json['heartedLevel'],
          unknownValue: BPLikeLevel.unknown),
      rated: json['rated'] as bool?,
      ratingScore: (json['ratingScore'] as num?)?.toDouble() ?? 0,
      ratingBonus: (json['ratingBonus'] as num?)?.toDouble() ?? 0,
      heartList: (json['heartList'] as List<dynamic>?)
              ?.map((e) => BPLikeMode.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      subscribed: json['subscribed'] as bool? ?? false,
      gotHeart: json['gotHeart'] as bool?,
      gotLevel: $enumDecodeNullable(_$BPLikeLevelEnumMap, json['gotLevel']),
      matched: json['matched'] as bool?,
    )
      ..subscriptionPlans = (json['subscriptionPlans'] as List<dynamic>?)
              ?.map((e) =>
                  BPSubscriptionModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          []
      ..rejected = json['rejected'] as bool?
      ..rejectedReason = json['rejectedReason'] as String?;

Map<String, dynamic> _$BPUserDataToJson(BPUserData instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('hearted', instance.hearted);
  val['heartedLevel'] = _$BPLikeLevelEnumMap[instance.heartedLevel];
  writeNotNull('rated', instance.rated);
  val['ratingScore'] = instance.ratingScore;
  val['ratingBonus'] = instance.ratingBonus;
  val['heartList'] = instance.heartList;
  val['subscribed'] = instance.subscribed;
  val['subscriptionPlans'] = instance.subscriptionPlans;
  writeNotNull('gotHeart', instance.gotHeart);
  writeNotNull('gotLevel', _$BPLikeLevelEnumMap[instance.gotLevel]);
  writeNotNull('matched', instance.matched);
  writeNotNull('rejected', instance.rejected);
  writeNotNull('rejectedReason', instance.rejectedReason);
  return val;
}

BPSubscriptionModel _$BPSubscriptionModelFromJson(Map<String, dynamic> json) =>
    BPSubscriptionModel(
      id: (json['id'] as num?)?.toInt() ?? 0,
      name: json['name'] as String? ?? '',
      description: json['description'] as String? ?? '',
      price: (json['price'] as num?)?.toInt() ?? 0,
      status: (json['status'] as num?)?.toInt() ?? 1,
      level: $enumDecodeNullable(_$BPLikeLevelEnumMap, json['level']) ??
          BPLikeLevel.low,
    );

Map<String, dynamic> _$BPSubscriptionModelToJson(
        BPSubscriptionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'status': instance.status,
      'level': _$BPLikeLevelEnumMap[instance.level],
    };

ReviewUser _$ReviewUserFromJson(Map<String, dynamic> json) => ReviewUser(
      id: (json['id'] as num?)?.toInt() ?? 0,
      firstname: json['firstname'] as String?,
      lastname: json['lastname'] as String?,
      avatar: json['avatar'] == null
          ? null
          : BPURLModel.fromJson(json['avatar'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ReviewUserToJson(ReviewUser instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('firstname', instance.firstname);
  writeNotNull('lastname', instance.lastname);
  writeNotNull('avatar', instance.avatar);
  return val;
}

AssetCertificationModel _$AssetCertificationModelFromJson(
        Map<String, dynamic> json) =>
    AssetCertificationModel(
      id: (json['id'] as num?)?.toInt() ?? 0,
      issuedate: json['issuedate'] == null
          ? null
          : DateTime.parse(json['issuedate'] as String),
      expirydate: json['expirydate'] == null
          ? null
          : DateTime.parse(json['expirydate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      assetVerifications: (json['asset_verifications'] as List<dynamic>?)
          ?.map(
              (e) => AssetVerificationModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      assetResult:
          $enumDecodeNullable(_$AssetResultEnumMap, json['asset_result']),
      reviewUsers: (json['review_users'] as List<dynamic>?)
          ?.map((e) => ReviewUser.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AssetCertificationModelToJson(
    AssetCertificationModel instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('issuedate', instance.issuedate?.toIso8601String());
  writeNotNull('expirydate', instance.expirydate?.toIso8601String());
  writeNotNull('createdAt', instance.createdAt?.toIso8601String());
  writeNotNull('updatedAt', instance.updatedAt?.toIso8601String());
  writeNotNull('asset_verifications', instance.assetVerifications);
  writeNotNull('asset_result', _$AssetResultEnumMap[instance.assetResult]);
  writeNotNull('review_users', instance.reviewUsers);
  return val;
}

const _$AssetResultEnumMap = {
  AssetResult.moreThan100K: 'moreThan100K',
  AssetResult.moreThan500K: 'moreThan500K',
  AssetResult.moreThan1M: 'moreThan1M',
  AssetResult.moreThan5M: 'moreThan5M',
  AssetResult.moreThan10M: 'moreThan10M',
  AssetResult.moreThan50M: 'moreThan50M',
  AssetResult.moreThan100M: 'moreThan100M',
};

ReceivedUserModel _$ReceivedUserModelFromJson(Map<String, dynamic> json) =>
    ReceivedUserModel(
      id: (json['id'] as num?)?.toInt() ?? 0,
      scannedAt: json['scannedAt'] == null
          ? null
          : DateTime.parse(json['scannedAt'] as String),
      user: json['user'] == null
          ? null
          : BPUserModel.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ReceivedUserModelToJson(ReceivedUserModel instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('scannedAt', instance.scannedAt?.toIso8601String());
  writeNotNull('user', instance.user);
  return val;
}

ReceivedUsersResponse _$ReceivedUsersResponseFromJson(
        Map<String, dynamic> json) =>
    ReceivedUsersResponse(
      data: (json['data'] as List<dynamic>?)
              ?.map(
                  (e) => ReceivedUserModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      meta: json['meta'] == null
          ? null
          : PaginationMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ReceivedUsersResponseToJson(
    ReceivedUsersResponse instance) {
  final val = <String, dynamic>{
    'data': instance.data,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('meta', instance.meta);
  return val;
}

PaginationMeta _$PaginationMetaFromJson(Map<String, dynamic> json) =>
    PaginationMeta(
      pagination: json['pagination'] == null
          ? null
          : PaginationInfo.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaginationMetaToJson(PaginationMeta instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('pagination', instance.pagination);
  return val;
}

PaginationInfo _$PaginationInfoFromJson(Map<String, dynamic> json) =>
    PaginationInfo(
      page: (json['page'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 20,
      pageCount: (json['pageCount'] as num?)?.toInt() ?? 1,
      total: (json['total'] as num?)?.toInt() ?? 0,
      hasNextPage: json['hasNextPage'] as bool? ?? false,
      hasPreviousPage: json['hasPreviousPage'] as bool? ?? false,
    );

Map<String, dynamic> _$PaginationInfoToJson(PaginationInfo instance) =>
    <String, dynamic>{
      'page': instance.page,
      'pageSize': instance.pageSize,
      'pageCount': instance.pageCount,
      'total': instance.total,
      'hasNextPage': instance.hasNextPage,
      'hasPreviousPage': instance.hasPreviousPage,
    };

BPUserModel _$BPUserModelFromJson(Map<String, dynamic> json) => BPUserModel(
      id: (json['id'] as num?)?.toInt() ?? 0,
      username: json['username'] as String?,
      fullname: json['fullname'] as String?,
      title: json['title'] as String?,
      email: json['email'] as String?,
      verificationEmail: json['verificationEmail'] as String?,
      phone: json['phone'] as String?,
      birthday: json['birthday'] == null
          ? null
          : DateTime.parse(json['birthday'] as String),
      regionCode: json['regionCode'] as String?,
      gender: $enumDecodeNullable(_$BPUserGenderEnumMap, json['gender']),
      verificationType: $enumDecodeNullable(
          _$BPVerificationTypeEnumMap, json['verificationType']),
      financialStatus: $enumDecodeNullable(
          _$BPFinancialStatusEnumMap, json['financialStatus']),
      university: json['university'] == null
          ? null
          : BPUniversity.fromJson(json['university'] as Map<String, dynamic>),
      avatar: json['avatar'] == null
          ? null
          : BPURLModel.fromJson(json['avatar'] as Map<String, dynamic>),
      agreed: json['agreed'] as bool?,
      emailSentAt: json['emailSentAt'] == null
          ? null
          : DateTime.parse(json['emailSentAt'] as String),
      verificationStatus: $enumDecodeNullable(
          _$BPVerificationStatusEnumMap, json['verificationStatus']),
      rejectionReason: json['rejectionReason'] as String?,
      photos: (json['photos'] as List<dynamic>?)
          ?.map((e) => BPURLModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      interests: (json['interests'] as List<dynamic>?)
          ?.map((e) => BPInterest.fromJson(e as Map<String, dynamic>))
          .toList(),
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      introduction: json['introduction'] as String?,
      balance: (json['balance'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : BPUserData.fromJson(json['data'] as Map<String, dynamic>),
      referralCode: json['referralCode'] as String?,
      confirmed: json['confirmed'] as bool?,
      countryCode: json['countryCode'] as String?,
      phoneVerificationStatus: $enumDecodeNullable(
              _$BPPhoneVerificationStatusEnumMap,
              json['phoneVerificationStatus']) ??
          BPPhoneVerificationStatus.pass,
      status: $enumDecodeNullable(_$BPGraduateStatusEnumMap, json['status']),
      fakeName: json['fakeName'] as String?,
      isWhitelisted: json['isWhitelisted'] as bool?,
      type: $enumDecodeNullable(_$BPUserRoleEnumMap, json['type'],
              unknownValue: BPUserRole.talent) ??
          BPUserRole.talent,
      regRefCode: json['regRefCode'] as String?,
      visibility: json['visibility'] as bool?,
      code: json['code'] as String?,
      nationalityCode: json['nationalityCode'] as String?,
      certificateList: (json['certificateList'] as List<dynamic>?)
          ?.map((e) => BPURLModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      firstTimeUpdateProfile: json['firstTimeUpdateProfile'] as bool?,
      fakeRandomAvatar: json['fakeRandomAvatar'] as String?,
      fakeAvatar: json['fakeAvatar'] == null
          ? null
          : BPURLModel.fromJson(json['fakeAvatar'] as Map<String, dynamic>),
      level: (json['level'] as num?)?.toInt(),
      splashImage: json['splashImage'] == null
          ? null
          : BPURLModel.fromJson(json['splashImage'] as Map<String, dynamic>),
      idcard: json['idcard'] == null
          ? null
          : BPURLModel.fromJson(json['idcard'] as Map<String, dynamic>),
      assetVerifications: (json['asset_verifications'] as List<dynamic>?)
          ?.map(
              (e) => AssetVerificationModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      assetCertification: json['asset_certification'] == null
          ? null
          : AssetCertificationModel.fromJson(
              json['asset_certification'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BPUserModelToJson(BPUserModel instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('username', instance.username);
  writeNotNull('fullname', instance.fullname);
  writeNotNull('title', instance.title);
  writeNotNull('email', instance.email);
  writeNotNull('verificationEmail', instance.verificationEmail);
  writeNotNull('phone', instance.phone);
  writeNotNull('birthday', instance.birthday?.toIso8601String());
  writeNotNull('gender', _$BPUserGenderEnumMap[instance.gender]);
  writeNotNull('verificationType',
      _$BPVerificationTypeEnumMap[instance.verificationType]);
  writeNotNull('university', instance.university);
  writeNotNull('avatar', instance.avatar);
  writeNotNull('agreed', instance.agreed);
  writeNotNull('emailSentAt', instance.emailSentAt?.toIso8601String());
  writeNotNull('verificationStatus',
      _$BPVerificationStatusEnumMap[instance.verificationStatus]);
  writeNotNull(
      'financialStatus', _$BPFinancialStatusEnumMap[instance.financialStatus]);
  writeNotNull('rejectionReason', instance.rejectionReason);
  val['certificateList'] = instance.certificateList;
  writeNotNull('photos', instance.photos);
  writeNotNull('interests', instance.interests);
  writeNotNull('location', instance.location);
  writeNotNull('latitude', instance.latitude);
  writeNotNull('longitude', instance.longitude);
  writeNotNull('introduction', instance.introduction);
  writeNotNull('balance', instance.balance);
  writeNotNull('data', instance.data);
  writeNotNull('referralCode', instance.referralCode);
  writeNotNull('confirmed', instance.confirmed);
  writeNotNull('regionCode', instance.regionCode);
  writeNotNull('countryCode', instance.countryCode);
  writeNotNull('nationalityCode', instance.nationalityCode);
  writeNotNull('status', _$BPGraduateStatusEnumMap[instance.status]);
  writeNotNull('phoneVerificationStatus',
      _$BPPhoneVerificationStatusEnumMap[instance.phoneVerificationStatus]);
  writeNotNull('fakeName', instance.fakeName);
  writeNotNull('fakeRandomAvatar', instance.fakeRandomAvatar);
  writeNotNull('fakeAvatar', instance.fakeAvatar);
  writeNotNull('isWhitelisted', instance.isWhitelisted);
  writeNotNull('type', _$BPUserRoleEnumMap[instance.type]);
  writeNotNull('regRefCode', instance.regRefCode);
  writeNotNull('visibility', instance.visibility);
  writeNotNull('code', instance.code);
  writeNotNull('firstTimeUpdateProfile', instance.firstTimeUpdateProfile);
  writeNotNull('level', instance.level);
  writeNotNull('splashImage', instance.splashImage);
  writeNotNull('idcard', instance.idcard);
  writeNotNull('asset_verifications', instance.assetVerifications);
  writeNotNull('asset_certification', instance.assetCertification);
  return val;
}

const _$BPUserGenderEnumMap = {
  BPUserGender.male: 'M',
  BPUserGender.female: 'F',
  BPUserGender.unknown: 'unknown',
};

const _$BPVerificationTypeEnumMap = {
  BPVerificationType.email: 'email',
  BPVerificationType.certificate: 'certificate',
};

const _$BPFinancialStatusEnumMap = {
  BPFinancialStatus.doNotAuthorize: 'doNotAuthorize',
  BPFinancialStatus.submitted: 'submitted',
  BPFinancialStatus.moreThan100K: 'moreThan100K',
  BPFinancialStatus.moreThan500K: 'moreThan500K',
  BPFinancialStatus.moreThan1M: 'moreThan1M',
  BPFinancialStatus.moreThan5M: 'moreThan5M',
  BPFinancialStatus.moreThan10M: 'moreThan10M',
  BPFinancialStatus.moreThan50M: 'moreThan50M',
  BPFinancialStatus.moreThan100M: 'moreThan100M',
};

const _$BPVerificationStatusEnumMap = {
  BPVerificationStatus.incomplete: 'incomplete',
  BPVerificationStatus.pending: 'pending',
  BPVerificationStatus.notVerifiedYet: 'not_verified_yet',
  BPVerificationStatus.verified: 'verified',
  BPVerificationStatus.rejected: 'rejected',
};

const _$BPPhoneVerificationStatusEnumMap = {
  BPPhoneVerificationStatus.no: 'no',
  BPPhoneVerificationStatus.pass: 'pass',
  BPPhoneVerificationStatus.done: 'done',
};

const _$BPGraduateStatusEnumMap = {
  BPGraduateStatus.graduate: 'GRADUATE',
  BPGraduateStatus.undergraduate: 'UNDERGRADUATE',
  BPGraduateStatus.none: 'NONE',
};

const _$BPUserRoleEnumMap = {
  BPUserRole.talent: 'talent',
  BPUserRole.business: 'business',
};
