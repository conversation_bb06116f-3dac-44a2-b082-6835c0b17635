// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'BPCommunityModels.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BPCommunityBoard _$BPCommunityBoardFromJson(Map<String, dynamic> json) =>
    BPCommunityBoard(
      id: (json['id'] as num?)?.toInt() ?? 0,
      name: json['name'] as String?,
      description: json['description'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
    );

Map<String, dynamic> _$BPCommunityBoardToJson(BPCommunityBoard instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('name', instance.name);
  writeNotNull('description', instance.description);
  val['isDefault'] = instance.isDefault;
  return val;
}

BPCommunityPost _$BPCommunityPostFromJson(Map<String, dynamic> json) =>
    BPCommunityPost(
      id: (json['id'] as num?)?.toInt() ?? 0,
      title: json['title'] as String?,
      content: json['content'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      publishedAt: json['publishedAt'] == null
          ? null
          : DateTime.parse(json['publishedAt'] as String),
      media: (json['media'] as List<dynamic>?)
          ?.map((e) => BPURLModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      authorUser: json['authorUser'] == null
          ? null
          : AuthorUser.fromJson(json['authorUser'] as Map<String, dynamic>),
      comments: (json['comments'] as List<dynamic>?)
          ?.map((e) => PostComment.fromJson(e as Map<String, dynamic>))
          .toList(),
      data: json['data'] == null
          ? null
          : PostExtra.fromJson(json['data'] as Map<String, dynamic>),
      pinned: json['pinned'] as bool? ?? false,
      board: json['board'] == null
          ? null
          : BPCommunityBoard.fromJson(json['board'] as Map<String, dynamic>),
      removed: json['removed'] as bool? ?? false,
      adUrl: json['adUrl'] as String?,
      weight: json['weight'] as num? ?? 0.0,
    );

Map<String, dynamic> _$BPCommunityPostToJson(BPCommunityPost instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('title', instance.title);
  writeNotNull('content', instance.content);
  writeNotNull('createdAt', instance.createdAt?.toIso8601String());
  writeNotNull('updatedAt', instance.updatedAt?.toIso8601String());
  writeNotNull('publishedAt', instance.publishedAt?.toIso8601String());
  writeNotNull('media', instance.media);
  writeNotNull('data', instance.data);
  writeNotNull('authorUser', instance.authorUser);
  writeNotNull('comments', instance.comments);
  writeNotNull('pinned', instance.pinned);
  writeNotNull('removed', instance.removed);
  writeNotNull('board', instance.board);
  writeNotNull('adUrl', instance.adUrl);
  writeNotNull('weight', instance.weight);
  return val;
}

PostExtra _$PostExtraFromJson(Map<String, dynamic> json) => PostExtra(
      liked: json['liked'] as bool? ?? false,
      likeCount: (json['likeCount'] as num?)?.toInt() ?? 0,
      disliked: json['disliked'] as bool? ?? false,
      dislikeCount: (json['dislikeCount'] as num?)?.toInt() ?? 0,
      commentCount: (json['commentCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$PostExtraToJson(PostExtra instance) => <String, dynamic>{
      'liked': instance.liked,
      'likeCount': instance.likeCount,
      'disliked': instance.disliked,
      'dislikeCount': instance.dislikeCount,
      'commentCount': instance.commentCount,
    };

AuthorUserExtraData _$AuthorUserExtraDataFromJson(Map<String, dynamic> json) =>
    AuthorUserExtraData(
      postCount: (json['postCount'] as num?)?.toInt() ?? 0,
      commentCount: (json['commentCount'] as num?)?.toInt() ?? 0,
      likeCount: (json['likeCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$AuthorUserExtraDataToJson(
        AuthorUserExtraData instance) =>
    <String, dynamic>{
      'postCount': instance.postCount,
      'commentCount': instance.commentCount,
      'likeCount': instance.likeCount,
    };

AuthorUser _$AuthorUserFromJson(Map<String, dynamic> json) => AuthorUser(
      id: (json['id'] as num?)?.toInt() ?? 0,
      fakeName: json['fakeName'] as String?,
      university: json['university'] == null
          ? null
          : BPUniversity.fromJson(json['university'] as Map<String, dynamic>),
      status: $enumDecodeNullable(_$BPGraduateStatusEnumMap, json['status']),
      data: json['data'] == null
          ? null
          : AuthorUserExtraData.fromJson(json['data'] as Map<String, dynamic>),
      posts: (json['posts'] as List<dynamic>?)
          ?.map((e) => BPCommunityPost.fromJson(e as Map<String, dynamic>))
          .toList(),
      regionCode: json['regionCode'] as String?,
      verificationStatus: $enumDecodeNullable(
              _$BPVerificationStatusEnumMap, json['verificationStatus']) ??
          BPVerificationStatus.incomplete,
      financialStatus: $enumDecodeNullable(
              _$BPFinancialStatusEnumMap, json['financialStatus']) ??
          BPFinancialStatus.doNotAuthorize,
      fakeAvatar: json['fakeAvatar'] == null
          ? null
          : BPURLModel.fromJson(json['fakeAvatar'] as Map<String, dynamic>),
      fakeRandomAvatar: json['fakeRandomAvatar'] as String?,
      level: (json['level'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AuthorUserToJson(AuthorUser instance) {
  final val = <String, dynamic>{
    'id': instance.id,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('fakeName', instance.fakeName);
  writeNotNull('university', instance.university);
  writeNotNull('status', _$BPGraduateStatusEnumMap[instance.status]);
  writeNotNull('data', instance.data);
  writeNotNull('posts', instance.posts);
  writeNotNull('regionCode', instance.regionCode);
  writeNotNull(
      'financialStatus', _$BPFinancialStatusEnumMap[instance.financialStatus]);
  writeNotNull('verificationStatus',
      _$BPVerificationStatusEnumMap[instance.verificationStatus]);
  writeNotNull('fakeAvatar', instance.fakeAvatar);
  writeNotNull('fakeRandomAvatar', instance.fakeRandomAvatar);
  writeNotNull('level', instance.level);
  return val;
}

const _$BPGraduateStatusEnumMap = {
  BPGraduateStatus.graduate: 'GRADUATE',
  BPGraduateStatus.undergraduate: 'UNDERGRADUATE',
  BPGraduateStatus.none: 'NONE',
};

const _$BPVerificationStatusEnumMap = {
  BPVerificationStatus.incomplete: 'incomplete',
  BPVerificationStatus.pending: 'pending',
  BPVerificationStatus.notVerifiedYet: 'not_verified_yet',
  BPVerificationStatus.verified: 'verified',
  BPVerificationStatus.rejected: 'rejected',
};

const _$BPFinancialStatusEnumMap = {
  BPFinancialStatus.doNotAuthorize: 'doNotAuthorize',
  BPFinancialStatus.submitted: 'submitted',
  BPFinancialStatus.moreThan100K: 'moreThan100K',
  BPFinancialStatus.moreThan500K: 'moreThan500K',
  BPFinancialStatus.moreThan1M: 'moreThan1M',
  BPFinancialStatus.moreThan5M: 'moreThan5M',
  BPFinancialStatus.moreThan10M: 'moreThan10M',
  BPFinancialStatus.moreThan50M: 'moreThan50M',
  BPFinancialStatus.moreThan100M: 'moreThan100M',
};

PostComment _$PostCommentFromJson(Map<String, dynamic> json) => PostComment(
      id: (json['id'] as num?)?.toInt() ?? 0,
      content: json['content'] as String,
      blocked: json['blocked'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      data: json['data'] == null
          ? null
          : PostCommentExtra.fromJson(json['data'] as Map<String, dynamic>),
      authorUser: json['authorUser'] == null
          ? null
          : AuthorUser.fromJson(json['authorUser'] as Map<String, dynamic>),
      post: json['post'] == null
          ? null
          : BPCommunityPost.fromJson(json['post'] as Map<String, dynamic>),
      mentions: (json['mentions'] as List<dynamic>?)
          ?.map((e) => MentionUser.fromJson(e as Map<String, dynamic>))
          .toList(),
    )..removed = json['removed'] as bool?;

Map<String, dynamic> _$PostCommentToJson(PostComment instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'content': instance.content,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('blocked', instance.blocked);
  writeNotNull('createdAt', instance.createdAt?.toIso8601String());
  writeNotNull('updatedAt', instance.updatedAt?.toIso8601String());
  writeNotNull('removed', instance.removed);
  writeNotNull('data', instance.data);
  writeNotNull('authorUser', instance.authorUser);
  writeNotNull('post', instance.post);
  writeNotNull('mentions', instance.mentions);
  return val;
}

PostCommentExtra _$PostCommentExtraFromJson(Map<String, dynamic> json) =>
    PostCommentExtra(
      liked: json['liked'] as bool? ?? false,
      likeCount: (json['likeCount'] as num?)?.toInt() ?? 0,
      disliked: json['disliked'] as bool? ?? false,
      dislikeCount: (json['dislikeCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$PostCommentExtraToJson(PostCommentExtra instance) =>
    <String, dynamic>{
      'liked': instance.liked,
      'likeCount': instance.likeCount,
      'disliked': instance.disliked,
      'dislikeCount': instance.dislikeCount,
    };
