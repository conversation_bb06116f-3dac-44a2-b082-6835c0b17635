import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'dart:convert';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../shared/components/BPEasyLoading.dart';
import '../../../../generated/locales.g.dart';

class QRScannerPage extends StatefulWidget {
  const QRScannerPage({super.key});

  @override
  State<QRScannerPage> createState() => _QRScannerPageState();
}

class _QRScannerPageState extends State<QRScannerPage> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  String? scannedData;
  bool isScanning = true;
  bool isProcessing = false;

  @override
  void reassemble() {
    super.reassemble();
    if (controller != null) {
      controller!.pauseCamera();
      controller!.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.arrow_back, color: Colors.white),
        ),
        title: MVText(
          LocaleKeys.asset_verification_qr_scanner_title.tr,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _toggleFlash,
            icon: const Icon(Icons.flash_on, color: Colors.white),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 4,
            child: Stack(
              children: [
                QRView(
                  key: qrKey,
                  onQRViewCreated: _onQRViewCreated,
                  overlay: QrScannerOverlayShape(
                    borderColor: BPColor.gold,
                    borderRadius: 10,
                    borderLength: 30,
                    borderWidth: 10,
                    cutOutSize: 250,
                  ),
                ),
                // Instructions overlay
                Positioned(
                  top: 50,
                  left: 20,
                  right: 20,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: MVText(
                      LocaleKeys.asset_verification_qr_scanner_instruction.tr,
                      fontSize: 16,
                      color: Colors.white,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                // Processing overlay
                if (isProcessing)
                  Positioned.fill(
                    child: Container(
                      color: Colors.black.withOpacity(0.7),
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              color: Colors.black,
              width: double.infinity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (scannedData != null && !_isUserData(scannedData!)) ...[
                    MVText(
                      LocaleKeys.asset_verification_qr_scanner_scanned_data.tr,
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: BPColor.gold.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: BPColor.gold),
                      ),
                      child: MVText(
                        scannedData!,
                        fontSize: 14,
                        color: Colors.white,
                        textAlign: TextAlign.center,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          onPressed: _continueScan,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: BPColor.backgroundGrey,
                            foregroundColor: Colors.white,
                          ),
                          child: MVText(
                            LocaleKeys.asset_verification_qr_scanner_continue.tr,
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                        ElevatedButton(
                          onPressed: _processScannedData,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: BPColor.gold,
                            foregroundColor: Colors.black,
                          ),
                          child: MVText(
                            LocaleKeys.asset_verification_qr_scanner_use_data.tr,
                            fontSize: 14,
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ] else ...[
                    MVText(
                      LocaleKeys.asset_verification_qr_scanner_position_hint.tr,
                      fontSize: 16,
                      color: Colors.white70,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    setState(() {
      this.controller = controller;
    });
    
    controller.scannedDataStream.listen((scanData) {
      if (isScanning && scanData.code != null && !isProcessing) {
        final data = scanData.code!;

        // Check if this is user data that should be processed automatically
        if (_isUserData(data)) {
          setState(() {
            isProcessing = true;
            isScanning = false;
          });
          controller.pauseCamera();
          
          // Process user data directly without showing UI
          _processUserDataDirectly(data);
        } else {
          // For non-user data, show the traditional UI
          setState(() {
            scannedData = data;
            isScanning = false;
          });
          controller.pauseCamera();
        }
      }
    });
  }

  /// Check if the scanned data is user-related data
  bool _isUserData(String data) {
    // Check if it's a pure number (user ID)
    if (RegExp(r'^\d+$').hasMatch(data)) {
      return true;
    }

    // Check if it's a user profile URL
    if (_isUserProfileUrl(data)) {
      return true;
    }

    // Check if it's JSON with user data
    try {
      final jsonData = json.decode(data);
      if (jsonData is Map<String, dynamic>) {
        return _containsUserData(jsonData);
      }
    } catch (e) {
      // Not JSON, continue other checks
    }

    return false;
  }

  /// Check if URL contains user profile patterns
  bool _isUserProfileUrl(String data) {
    final userUrlPatterns = [
      RegExp(r'/user/(\d+)'),
      RegExp(r'/profile/(\d+)'),
      RegExp(r'user_id=(\d+)'),
      RegExp(r'userId=(\d+)'),
      RegExp(r'id=(\d+)'),
    ];

    return userUrlPatterns.any((pattern) => pattern.hasMatch(data));
  }

  /// Check if JSON contains user data
  bool _containsUserData(Map<String, dynamic> jsonData) {
    // Check for user ID fields
    if (jsonData.containsKey('userId') || jsonData.containsKey('user_id') || jsonData.containsKey('id')) {
      return true;
    }

    // Check for user profile fields
    if (jsonData.containsKey('fullname') || jsonData.containsKey('fakeName') || jsonData.containsKey('username') || jsonData.containsKey('avatar') || jsonData.containsKey('fakeAvatar')) {
      return true;
    }

    return false;
  }

  /// Process user data directly without showing confirmation UI
  void _processUserDataDirectly(String data) {
    // Return the data immediately with success message
    Get.back(result: data);
    BPEasyLoading.showSuccess(LocaleKeys.asset_verification_qr_scanner_success.tr);
  }

  void _toggleFlash() async {
    if (controller != null) {
      await controller!.toggleFlash();
    }
  }

  void _continueScan() {
    setState(() {
      scannedData = null;
      isScanning = true;
    });
    controller?.resumeCamera();
  }

  void _processScannedData() {
    if (scannedData != null) {
      // Return the scanned data to the previous page
      Get.back(result: scannedData);
      
      // Show success message
      BPEasyLoading.showSuccess(LocaleKeys.asset_verification_qr_scanner_success.tr);
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
} 