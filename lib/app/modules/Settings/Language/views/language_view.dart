import 'package:flutter/material.dart';

import 'package:get/get.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../../shared/components/BPGetView.dart';
import '../../../../shared/components/BPLanguageUtil.dart';
import '../../../../../generated/locales.g.dart';
import '../controllers/language_controller.dart';

class LanguageView extends GetView<LanguageController> {
  const LanguageView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: createAppBar(title: LocaleKeys.settings_language.tr, backgroundColor: Colors.black, foregroundColor: Colors.white),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
          child: Obx(
            () => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: BPColor.grey24,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SelectMenu(
                    title: 'English',
                    checked: controller.curLang.value == BPLanguage.en.name,
                    onTap: () {
                      controller.setLanguage(BPLanguage.en);
                    },
                  ),
                  Divider(color: BPColor.grey66),
                  SelectMenu(
                    title: '한국어',
                    checked: controller.curLang.value == BPLanguage.ko.name,
                    onTap: () {
                      controller.setLanguage(BPLanguage.ko);
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class SelectMenu extends StatelessWidget {
  final String title;
  final Function? onTap;
  final bool? checked;
  const SelectMenu({
    super.key,
    required this.title,
    this.onTap,
    this.checked = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onTap?.call();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 14),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            checked ?? false ? const Icon(Icons.check, color: BPColor.gold, size: 16) : const SizedBox(),
          ],
        ),
      ),
    );
  }
}
