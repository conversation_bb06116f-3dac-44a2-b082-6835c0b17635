import 'package:flutter/material.dart';

import 'package:get/get.dart';
import '../../../../routes/app_pages.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../../shared/components/BPGetView.dart';
import '../../../../../generated/locales.g.dart';

import '../controllers/settings_index_controller.dart';

class SettingsIndexView extends GetView<SettingsIndexController> {
  const SettingsIndexView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: createAppBar(title: LocaleKeys.settings_title.tr, backgroundColor: Colors.black, foregroundColor: Colors.white),
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
        child: Column(
          children: [
            ...[
              LocaleKeys.settings_language.tr,
              // LocaleKeys.settings_manage_black_list.tr,
              LocaleKeys.settings_quit.tr,
            ].map(
              (e) => InkWell(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  child: Row(
                    children: [
                      Text(e, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: Colors.white)),
                      const Spacer(),
                      const Icon(
                        Icons.arrow_forward_ios,
                        color: BPColor.greyText,
                        size: 15,
                      ),
                    ],
                  ),
                ),
                onTap: () {
                  if (e == LocaleKeys.settings_language.tr) {
                    Get.toNamed(Routes.LANGUAGE);
                  }
                  if (e == LocaleKeys.settings_manage_black_list.tr) {
                    Get.toNamed(Routes.BLACK_LIST);
                  }
                  if (e == LocaleKeys.settings_quit.tr) {
                    Get.toNamed(Routes.QUIT);
                  }
                },
              ),
            ),
            const Spacer(),
            Text('Version: ${controller.appVersion}', style: const TextStyle(color: BPColor.greyText)),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
