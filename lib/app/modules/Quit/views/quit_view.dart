import 'package:flutter/material.dart';

import 'package:get/get.dart';
import '../../../shared/components/BPAlert.dart';
import '../../../shared/components/BPBaseInfoInputFiled.dart';
import '../../../shared/components/BPCheckbox.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/BPGetView.dart';
import '../../../shared/components/BPTextButton.dart';
import '../../../../generated/locales.g.dart';

import '../controllers/quit_controller.dart';

class QuitView extends GetView<QuitController> {
  const QuitView({super.key});
  @override
  Widget build(BuildContext context) {
    checkboxItem(bool isSelected, String title, bool redHl, Function(bool) onChanged) {
      return Padding(
          padding: const EdgeInsets.only(top: 5, bottom: 10),
          child: BPCheckbox(
              value: isSelected,
              title: title,
              onChangeValue: (value) {
                onChanged(value ?? false);
              },
              redHighlight: redHl,),);
    }

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: createAppBar(title: LocaleKeys.settings_quit.tr, backgroundColor: Colors.black, foregroundColor: Colors.white),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Obx(() => Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Text(
                    '${controller.username}, ${LocaleKeys.quit_main_title.tr}',
                    style: const TextStyle(
                    color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LocaleKeys.quit_main_desc.tr,
                    style: const TextStyle(
                    color: BPColor.greyText,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Container(
                  decoration: BoxDecoration(color: BPColor.grey24, borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.quit_content_title.tr,
                          style: const TextStyle(
                          color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ContentRow(rowIndex: '1', title: LocaleKeys.quit_list_1.tr),
                        const SizedBox(height: 5),
                        ContentRow(title: LocaleKeys.quit_list_1_a.tr),
                        const SizedBox(height: 16),
                        ContentRow(rowIndex: '2', title: LocaleKeys.quit_list_2.tr),
                        const SizedBox(height: 5),
                        ContentRow(title: LocaleKeys.quit_list_2_a.tr),
                        const SizedBox(height: 16),
                        ContentRow(rowIndex: '3', title: LocaleKeys.quit_list_3.tr),
                        const SizedBox(height: 5),
                        ContentRow(title: LocaleKeys.quit_list_3_a.tr),
                        const SizedBox(height: 16),
                        ContentRow(rowIndex: '4', title: LocaleKeys.quit_list_4.tr),
                        const SizedBox(height: 5),
                        ContentRow(title: LocaleKeys.quit_list_4_a.tr),
                        const SizedBox(height: 16),
                        ContentRow(rowIndex: '5', title: LocaleKeys.quit_list_5.tr),
                        const SizedBox(height: 5),
                        ContentRow(title: LocaleKeys.quit_list_5_a.tr),
                        const SizedBox(height: 16),
                        ContentRow(rowIndex: '6', title: LocaleKeys.quit_list_6.tr),
                        const SizedBox(height: 5),
                        ContentRow(title: LocaleKeys.quit_list_6_a.tr),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 24),
                    child: BPCheckbox(
                        value: controller.hasAgreed.value,
                        title: LocaleKeys.quit_agreement_title.tr,
                        onChangeValue: (value) {
                          controller.hasAgreed.value = value ?? false;
                        },),
                  ),
                  Text(
                    LocaleKeys.quit_reason_title.tr,
                  style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 16),
                  ...controller.items
                      .asMap()
                      .entries
                      .map((e) => checkboxItem(e.value.isSelected, e.value.title, false, (value) {
                            controller.selectItem(e.value);
                          }),)
                      ,
                  const SizedBox(height: 6),
                BPBaseInfoInputFiled(backgroundColor: BPColor.grey24, hintText: LocaleKeys.quit_reason_placeholder.tr, maxLines: 6, keyboardType: TextInputType.multiline, controller: controller.contentTextController, contentPadding: const EdgeInsets.all(16)),
                  const SizedBox(height: 40),
                  Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    Expanded(
                      child: SizedBox(
                        height: 44,
                        child: BPTextButton(
                          title: LocaleKeys.quit_quit.tr,
                          borderRadius: 44,
                          borderSideWidth: 1,
                          borderSideColor: controller.isValidate ? BPColor.gold : BPColor.greyText,
                          color: controller.isValidate ? BPColor.gold : BPColor.greyText,
                          fontWeight: FontWeight.w600,
                          onPressed: () {
                            if (!controller.isValidate) return;
                            BPAlert.show(
                                title: LocaleKeys.quit_confirm_title.tr,
                                content: LocaleKeys.quit_confirm_popup.tr,
                                actionTitles: [
                                  LocaleKeys.quit_quit.tr,
                                  LocaleKeys.common_cancel.tr,
                                ],
                                highlightIndex: 1,
                                completion: (index) async {
                                  if (index == 0) {
                                    await controller.confirmRemove();
                                  }
                                },);
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: SizedBox(
                        height: 44,
                        child: BPTextButton(
                          title: LocaleKeys.common_cancel.tr,
                          borderRadius: 44,
                          backgroundColor: BPColor.gold,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          onPressed: () {
                            Get.back();
                          },
                        ),
                      ),
                    ),
                  ],),
                ],),),
          ),
        ),);
  }
}

class ContentRow extends StatelessWidget {
  final String? rowIndex;
  final String title;
  const ContentRow({
    super.key,
    this.rowIndex,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
            width: 14,
            child: rowIndex != null
                ? Text(
                    "$rowIndex.",
                  style: const TextStyle(fontSize: 14, color: Colors.white),
                  )
                : const Text(
                    "\u2022",
                  style: TextStyle(fontSize: 18, color: Colors.white),
                  ),),
        Expanded(
          child: Text(title, style: const TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}
