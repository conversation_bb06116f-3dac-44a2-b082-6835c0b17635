import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/web.dart';
import '../../../data/BPAccountModels.dart';
import '../../../data/BPCommonModels.dart';
import '../../../data/BPInterestModels.dart';
import '../../../data/BPUserModels.dart';
import '../../CreatingAccount/PersonalBasicInfo/controllers/personal_basic_info_controller.dart';
import '../../../routes/app_pages.dart';
import '../../../shared/account/BPSessionManager.dart';
import '../../../shared/components/BPAlert.dart';
import '../../../shared/components/BPEasyLoading.dart';
import '../../../shared/components/BPMediaSection.dart';
import '../../../shared/components/BPTimeUtil.dart';
import '../../../shared/components/SaveProgress.dart';
import '../../../shared/networking/BPRestAPI.dart';
import 'package:dio/dio.dart' as dio;
import '../../../../generated/locales.g.dart';
import 'package:rxdart/subjects.dart';

import '../../OtpVerify/pages/otp_verify_page.dart';

const _personalNameInfoRegExp = '^[^\n\r]{1,30}\$';
const _phoneNumberRegExp = '^[0-9]{1,14}\$';

class DatingEditProfileController extends GetxController {
  // Use these keys to automatically scroll to the error field
  final nameKey = GlobalKey();
  final birthKey = GlobalKey();
  final genderKey = GlobalKey();
  final regionKey = GlobalKey();
  final nationalityKey = GlobalKey();
  final phoneKey = GlobalKey();
  final interestsKey = GlobalKey();
  final introductionKey = GlobalKey();
  Country? selectedCountryForPhoneCode;

  BPUserModel? get accountModel => BPSessionManager.instance.accountSession?.accountModel;

  final tempUser = BPUserModel(id: 0).obs;

  late String location = accountModel?.location ?? '';
  late List<BPInterest> interests = accountModel?.interests ?? [];
  late String introduction = accountModel?.introduction ?? '';

  final addressError = ''.obs;
  final interestsError = ''.obs;
  final introductionError = ''.obs;
  final genderError = ''.obs;

  bool get canLeave => !hasModified.value;

  BPEPInfoInputItem name = BPEPInfoInputItem(maxLength: 30);
  BPEPInfoInputItem birth = BPEPInfoInputItem(maxLength: 1000);
  BPEPInfoInputItem region = BPEPInfoInputItem(maxLength: 1000);
  BPEPInfoInputItem nationality = BPEPInfoInputItem(maxLength: 1000);
  BPEPInfoInputItem phoneCode = BPEPInfoInputItem(maxLength: 1000);
  BPEPInfoInputItem phoneNumber = BPEPInfoInputItem(maxLength: 14);
  bool isNameValid = false;
  dio.CancelToken? _checkNicknameTask;

  final TextEditingController nameTEController = TextEditingController();
  final TextEditingController birthTEController = TextEditingController();
  final TextEditingController regionTEController = TextEditingController();
  final TextEditingController nationalityTEController = TextEditingController();
  final TextEditingController phoneCodeTEController = TextEditingController();
  final TextEditingController phoneNumberTEController = TextEditingController();

  final gender = BPUserGender.unknown.obs;

  final birthValueSubject = BehaviorSubject<DateTime>();
  final regionValueSubject = BehaviorSubject<String>();
  final nationalityValueSubject = BehaviorSubject<String>();

  bool get isBirthValid => birthValueSubject.valueOrNull != null;
  bool get isRegionValid => regionValueSubject.valueOrNull != null;
  bool get isNationalityValid => nationalityValueSubject.valueOrNull != null;
  bool get isPhoneCodeValid => phoneCodeTEController.text.isNotEmpty;
  bool get isPhoneNumberValid => RegExp(_phoneNumberRegExp).hasMatch(phoneNumber.value);

  BPVerificationStatus get verificationStatus => BPSessionManager.accountModelConvenience?.verificationStatus ?? BPVerificationStatus.incomplete;

  final ProgressController progressController = Get.put<ProgressController>(ProgressController());

  final isGraduated = false.obs;

  bool get isRejected => verificationStatus == BPVerificationStatus.rejected;
  bool get isIncomplete => verificationStatus == BPVerificationStatus.incomplete;
  bool get isPending => verificationStatus == BPVerificationStatus.pending;
  bool get isNotVerifiedYet => verificationStatus == BPVerificationStatus.notVerifiedYet;
  bool get isVerified => verificationStatus == BPVerificationStatus.verified;

  final hasModified = false.obs;
  final hasPhoneModified = false.obs;

  void _nameTEControllerListener() {
    name.value = nameTEController.text.trim();
    name.errorMsg = null;
    update();
  }

  void _phoneNumberTEControllerListener() {
    phoneNumber.value = phoneNumberTEController.text;
    phoneNumber.errorMsg = null;
    update();
  }

  Future onTapIntroduction() async {
    introductionError.value = '';
    final curVal = tempUser.value.introduction ?? introduction;
    final model = await Get.toNamed(Routes.INTRODUCE, arguments: {'fromEdit': true, 'introduction': curVal});
    if (model != null) {
      tempUser.value.introduction = model.introduction;
      tempUser.refresh();
      hasModified.value = true;
    }
  }

  Future onTapInterests() async {
    interestsError.value = '';
    final curVal = tempUser.value.interests ?? interests;
    final model = await Get.toNamed(Routes.HOBBIES, arguments: {'fromEdit': true, 'interests': curVal});
    if (model != null) {
      tempUser.value.interests = model.interests;
      tempUser.refresh();
      hasModified.value = true;
    }
  }

  void genderChanged({required BPUserGender setGender}) {
    genderError.value = '';
    gender.value = setGender;
    hasModified.value = accountModel?.gender != setGender;
    update();
  }

  void initPreSet() async {
    // basic info load
    if (accountModel?.phone != null) {
      name.value = accountModel?.username ?? '';
      nameTEController.text = name.value;
      birthValueSubject.add(accountModel?.birthday ?? DateTime.now());
      regionValueSubject.add(accountModel?.regionCode ?? '');
      nationalityValueSubject.add(accountModel?.nationalityCode ?? '');
      phoneCodeTEController.text = accountModel?.countryCode != null ? "+${accountModel?.countryCode.toString().replaceAll('+', '')}" : "";
      gender.value = accountModel?.gender ?? BPUserGender.unknown;
      phoneNumberTEController.text = accountModel?.phone ?? '';
      phoneNumber.value = accountModel?.phone ?? '';

      // currentCountryCodeIndex = max(0, countryCodes.indexWhere((element) => element.replaceFirst('+', '') == accountModel?.countryCode));
      update();
    }

    // other info load
    tempUser.value.location = accountModel?.location;
    tempUser.value.interests = accountModel?.interests;
    tempUser.value.introduction = accountModel?.introduction;
    tempUser.value.gender = accountModel?.gender ?? BPUserGender.unknown;
  }

  Future onClickSave() async {
    // Only enable the button if the user has made changes
    if (hasModified.value) {
      // Validate the user's input, and if it fails, return
      if (await validate() == false) {
        _scrollToErrorField();

        return;
      }

      // Show a confirmation dialog
      await BPAlert.show(
        title: LocaleKeys.edit_profile_edit_save_tip_title.tr,
        content: LocaleKeys.edit_profile_edit_save_tip_content.tr,
        actionTitles: [LocaleKeys.common_cancel.tr, LocaleKeys.common_save.tr],
        highlightIndex: 1,
        barrierDismissible: false,
        completion: (index) async {
          if (index == 1) {
            await saveAll();
            // if (hasPhoneModified.value) {
            //   var result = await OtpVerifyPage.goToOtpVerifyPage(
            //     countryCode: selectedCountryForPhoneCode?.countryCode,
            //     phoneNumber: phoneNumberTEController.text,
            //   );

            //   if (result == true) {
            //     await saveAll();
            //   }
            // } else {
            //   await saveAll();
            // }
          }
        },
      );
    }
  }

  bool checkFirstTimeUpdateProfile() {
    return accountModel?.firstTimeUpdateProfile ?? false;
  }

  Future saveAll() async {
    if (!await validate()) return;

    try {
      progressController.show();

      final model = BPUserModel(
        id: accountModel!.id,
        status: isGraduated.value ? BPGraduateStatus.graduate : BPGraduateStatus.undergraduate,
        introduction: tempUser.value.introduction ?? introduction,
        interests: tempUser.value.interests ?? interests,
        gender: gender.value,
        username: name.value.trim(),
        birthday: birthValueSubject.valueOrNull,
        regionCode: regionValueSubject.valueOrNull,
        nationalityCode: nationalityValueSubject.valueOrNull,
        countryCode: phoneCodeTEController.text,
        phone: phoneNumberTEController.text,
        phoneVerificationStatus: BPPhoneVerificationStatus.done,
        firstTimeUpdateProfile: false,
      );

      var body = model.toJson();
      final result = await BPRestClient().editMeInfo(id: model.id, body: body);
      BPSessionManager.instance.synchronizeAccountBaseInfo(result);
      BPEasyLoading.showToast(LocaleKeys.common_successful.tr);
      hasModified.value = false;
      progressController.setProgress(1.0);
      progressController.hide();
      // Future.delayed(const Duration(milliseconds: 500), () async {
      //   if (firstUpdate && (accountModel?.regRefCode?.isEmpty ?? true)) {
      //     await Get.toNamed(Routes.INVITATION);

      //     Get.back();
      //   } else {
      //     Get.back();
      //   }
      // });
    } catch (e) {
      progressController.hide();
      catchedError(e);
    }
  }

  // ===================== Private Methods =====================

  Future<bool> validate() async {
    bool isPassed = true;

    await validateName();

    if (!isBirthValid) {
      birth.errorMsg = LocaleKeys.edit_profile_profile_birth_required.tr;
      isPassed = false;
    } else {
      birth.errorMsg = null;
    }

    if (!isRegionValid) {
      region.errorMsg = LocaleKeys.edit_profile_profile_region_required.tr;
      isPassed = false;
    } else {
      region.errorMsg = null;
    }

    if (!isNationalityValid) {
      nationality.errorMsg = LocaleKeys.personal_basic_info_please_enter_your_nationality.tr;
      isPassed = false;
    } else {
      nationality.errorMsg = null;
    }

    if (!isPhoneCodeValid) {
      phoneCode.errorMsg = LocaleKeys.edit_profile_profile_phone_required.tr;
      isPassed = false;
    } else {
      phoneCode.errorMsg = null;
    }

    if (!isPhoneNumberValid) {
      phoneNumber.errorMsg = LocaleKeys.edit_profile_profile_phone_required.tr;
      isPassed = false;
    } else {
      phoneNumber.errorMsg = null;
    }

    if (tempUser.value.interests == null || tempUser.value.interests!.isEmpty) {
      interestsError.value = LocaleKeys.edit_profile_interests_require.tr;
      isPassed = false;
    } else {
      interestsError.value = '';
    }

    if (tempUser.value.introduction == null || tempUser.value.introduction!.trim() == '') {
      introductionError.value = LocaleKeys.edit_profile_introduction_require.tr;
      isPassed = false;
    } else {
      introductionError.value = '';
    }

    if (gender.value == BPUserGender.unknown) {
      genderError.value = LocaleKeys.edit_profile_profile_gender_required.tr;
      isPassed = false;
    } else {
      genderError.value = '';
    }

    update();

    return isPassed;
  }

  void _scrollToErrorField() {
    if (!isBirthValid) {
      _scrollToKey(birthKey);
      return;
    }

    if (gender.value == BPUserGender.unknown) {
      _scrollToKey(genderKey);
      return;
    }

    if (!isRegionValid) {
      _scrollToKey(regionKey);
      return;
    }

    if (!isNationalityValid) {
      _scrollToKey(nationalityKey);
      return;
    }

    if (!isPhoneCodeValid) {
      _scrollToKey(phoneKey);
      return;
    }

    if (!isPhoneNumberValid) {
      _scrollToKey(phoneKey);
      return;
    }

    if (tempUser.value.interests == null || tempUser.value.interests!.isEmpty) {
      _scrollToKey(interestsKey);
      return;
    }

    if (tempUser.value.introduction == null || tempUser.value.introduction!.trim() == '') {
      _scrollToKey(introductionKey);
      return;
    }
  }

  void _scrollToKey(GlobalKey key) {
    // 检查 currentContext 是否为 null，避免空指针异常
    if (key.currentContext == null) {
      Logger().w('_scrollToKey: currentContext is null for key: $key, retrying in 100ms');
      // 延迟重试，给Widget时间完成构建
      Future.delayed(const Duration(milliseconds: 100), () {
        if (key.currentContext != null) {
          _scrollToKeyImmediate(key);
        } else {
          Logger().w('_scrollToKey: currentContext still null after retry, skipping scroll');
        }
      });
      return;
    }

    _scrollToKeyImmediate(key);
  }

  void _scrollToKeyImmediate(GlobalKey key) {
    try {
      var duration = const Duration(milliseconds: 500);
      var curve = Curves.easeInOut;
      Scrollable.ensureVisible(
        key.currentContext!,
        duration: duration,
        curve: curve,
      );
    } catch (e) {
      Logger().e('_scrollToKeyImmediate: Error scrolling to key: $e');
    }
  }

  Future<bool> validateName() async {
    if (!RegExp(_personalNameInfoRegExp).hasMatch(name.value)) {
      _scrollToKey(nameKey);
      name.errorMsg = LocaleKeys.edit_profile_profile_nickname_required.tr;
      isNameValid = false;
      update();
      return false;
    }

    if (await checkNicknameExists(name.value)) {
      _scrollToKey(nameKey);
      name.errorMsg = LocaleKeys.personal_basic_info_nick_name_exist_tip.tr;
      isNameValid = false;
      update();
      return false;
    }

    isNameValid = true;
    return true;
  }

  Future checkNicknameExists(String text) async {
    _checkNicknameTask?.cancel();
    _checkNicknameTask = dio.CancelToken();
    try {
      final res = await BPRestClient().checkNickname(body: {'username': text}, cancelToken: _checkNicknameTask);
      return res['exists'];
    } catch (e) {
      return false;
    } finally {
      if (_checkNicknameTask?.isCancelled == false) {
        _checkNicknameTask = null;
      }
    }
  }

  @override
  void onInit() {
    super.onInit();

    Logger().i(BPSessionManager.instance.accountSession?.accountModel.toJson());

    nameTEController.addListener(_nameTEControllerListener);
    birthValueSubject.stream.listen((event) {
      final value = BPTimeUtil.formatDate(event.toLocal());
      birth.value = value;
      birthTEController.text = value;
      birth.errorMsg = null;
      update();
    });

    regionTEController.text = Country.tryParse(accountModel?.regionCode ?? '')?.name ?? '';
    nationalityTEController.text = Country.tryParse(accountModel?.nationalityCode ?? '')?.name ?? '';
    phoneCodeTEController.text = accountModel?.countryCode != null ? "+${accountModel?.countryCode.toString().replaceAll('+', '')}" : "";

    isGraduated.value = BPSessionManager.accountModelConvenience?.status == BPGraduateStatus.graduate;
    phoneNumberTEController.addListener(_phoneNumberTEControllerListener);

    tempUser.value = accountModel?.copyWith() ?? BPUserModel(id: 0);
  }

  @override
  void onReady() {
    super.onReady();
    initPreSet();
  }

  @override
  void onClose() {
    nameTEController.removeListener(_nameTEControllerListener);
    phoneNumberTEController.removeListener(_phoneNumberTEControllerListener);
    super.onClose();
  }

  Future<void> onPhoneInputTap() async {
    var currentCountry = CountryService().getAll().firstWhereOrNull((element) => element.phoneCode == phoneCodeTEController.text.replaceAll('+', ''));

    var result = await OtpVerifyPage.goToOtpVerifyPage(
      countryCode: CountryService()
          .getAll()
          .firstWhereOrNull(
            (element) => element.phoneCode == currentCountry?.phoneCode,
          )
          ?.countryCode,
      phoneNumber: tempUser.value.phone,
      isUpdatePhone: true,
    );

    if (result != null && result is Map<String, dynamic>) {
      phoneCodeTEController.text = "+${result['countryCode'].toString().replaceAll('+', '')}";
      tempUser.value.countryCode = result['countryCode'].toString().replaceAll('+', '');
      phoneNumberTEController.text = result['phoneNumber'] ?? '';
      tempUser.value.phone = result['phoneNumber'] ?? '';
      hasModified.value = true;
    }
  }
}
