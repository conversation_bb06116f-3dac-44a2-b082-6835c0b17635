import 'package:flutter/material.dart';
import '../../../shared/components/BPColorUtil.dart';

class SectionContainer extends StatelessWidget {
  final Widget child;
  final String? errorMessage;
  final Color? borderColor;

  const SectionContainer({super.key, required this.child, this.errorMessage, this.borderColor = BPColor.grey2A});
  bool get isError => errorMessage != null && errorMessage != '';
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(width: double.infinity, constraints: const BoxConstraints(minHeight: 48), decoration: BoxDecoration(border: Border.all(color: isError ? BPColor.infoRed : borderColor ?? BPColor.grey2A), color: BPColor.grey1B, borderRadius: BorderRadius.circular(12)), padding: const EdgeInsets.all(12), child: child),
        if (isError) ...[
          const SizedBox(height: 6),
          Text(errorMessage ?? '',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: BPColor.infoRed,
              ),),
        ],
      ],
    );
  }
}
