import 'package:flutter/material.dart';
import '../../../shared/components/BPColorUtil.dart';

class SectionTitle extends StatelessWidget {
  final String title;
  const SectionTitle({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: const TextStyle(
        color: BPColor.whiteText,
        fontSize: 14,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}

class MainSectionTitle extends StatelessWidget {
  final String title;
  final bool? isRequired;
  final Color? textColor;

  const MainSectionTitle({super.key, required this.title, this.isRequired, this.textColor});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(title,
            style: TextStyle(
              color: textColor ?? const Color(0xFF9EA3AE),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),),
          if (isRequired == true)
            const Text(
              ' *',
              style: TextStyle(
                color: Color(0xFFFF9595),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
        ],
      ),
    );
  }
}
