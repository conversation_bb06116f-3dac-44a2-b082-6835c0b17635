import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/custom_country_picker.dart';
import '../../../../main.dart';
import '../../../data/BPAccountModels.dart';
import '../../../shared/components/BPAlert.dart';
import '../../../shared/components/BPBaseInfoInputFiled.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/BPDatePicker.dart';
import '../../../shared/components/BPGetView.dart';
import '../../../shared/components/BPImageUtil.dart';
import '../../../shared/components/BPTextButton.dart';
import '../../../shared/components/touch_close_keyboard.dart';
import '../../../../generated/locales.g.dart';

import '../controllers/dating_edit_profile_controller.dart';
import '../widgets/SectionContainer.dart';

class DatingEditProfile extends GetView<DatingEditProfileController> {
  const DatingEditProfile({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DatingEditProfileController>(
      builder: (controller) => PopScope(
        canPop: controller.canLeave,
        onPopInvokedWithResult: (bool didPop, dynamic result) async {
          if (!didPop) {
            BPAlert.show(
              title: LocaleKeys.edit_profile_unsaved_change_title.tr,
              content: LocaleKeys.edit_profile_unsaved_change_content.tr,
              actionTitles: [LocaleKeys.edit_profile_leave.tr, LocaleKeys.edit_profile_stay.tr],
              highlightIndex: 1,
              completion: (index) {
                if (index == 0) {
                  Get.back();
                }
              },
            );

            // if (result == true) {
            //   Get.back();
            // }
          }
        },
        // onPopInvoked: (bool didPop) async {
        //   if (!didPop) {
        //     BPAlert.show(
        //       title: LocaleKeys.edit_profile_unsaved_change_title.tr,
        //       content: LocaleKeys.edit_profile_unsaved_change_content.tr,
        //       actionTitles: [LocaleKeys.edit_profile_leave.tr, LocaleKeys.edit_profile_stay.tr],
        //       highlightIndex: 1,
        //       completion: (index) {
        //         if (index == 0) {
        //           Get.back();
        //         }
        //       },
        //     );
        //   }
        // },
        child: TouchCloseSoftKeyboard(
          child: Scaffold(
            backgroundColor: BPColor.background,
            appBar: createAppBar(
              title: LocaleKeys.profile_title.tr,
              backgroundColor: BPColor.background,
              foregroundColor: BPColor.whiteText,
              actions: [
                // IconButton(
                //   onPressed: () {
                //     Get.toNamed(Routes.INVITATION);
                //   },
                //   icon: const Icon(Icons.email_rounded),
                // ),
                Obx(
                  () => TextButton(
                    onPressed: controller.hasModified.value ? () => controller.onClickSave() : null,
                    child: Text(
                      LocaleKeys.common_save.tr,
                      style: TextStyle(
                        color: controller.hasModified.value ? BPColor.gold : BPColor.greyText,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            body: SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
                  child: GetBuilder<DatingEditProfileController>(
                    builder: (controller) => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Name field
                        Focus(
                          onFocusChange: (hasFocus) async {
                            if (!hasFocus) {
                              await controller.validateName();
                            }
                          },
                          child: BPBaseInfoInputFiled(
                            key: controller.nameKey,
                            title: LocaleKeys.personal_basic_info_nick_name.tr,
                            titleStyle: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: BPColor.whiteText,
                            ),
                            maxLength: controller.name.maxLength,
                            hintText: LocaleKeys.personal_basic_info_nick_name_PH.tr,
                            errorMessage: controller.name.errorMsg ?? '',
                            highlight: controller.name.errorMsg != null,
                            borderEnable: true,
                            borderColor: BPColor.grey2A,
                            controller: controller.nameTEController,
                            backgroundColor: BPColor.grey24,
                            textColor: BPColor.whiteText,
                            onChanged: (value) => controller.hasModified.value = value != controller.tempUser.value.username,
                          ),
                        ),
                        const SizedBox(height: 12),
                        // Birth date field
                        BPBaseInfoInputFiled(
                          key: controller.birthKey,
                          title: LocaleKeys.personal_basic_info_date_birth.tr,
                          titleStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColor.whiteText,
                          ),
                          backgroundColor: BPColor.grey24,
                          editable: false,
                          onPressed: () {
                            FocusManager.instance.primaryFocus?.unfocus();
                            BPDatePicker.show(
                              context: context,
                              maximumDate: DateTime.now().subtract(const Duration(days: 365 * 18)),
                              initialDate: controller.birthValueSubject.valueOrNull ?? DateTime(2000),
                              onDone: (date) {
                                controller.birthValueSubject.add(date);
                                controller.hasModified.value = controller.birthValueSubject.value != controller.tempUser.value.birthday;
                              },
                            );
                          },
                          rightIcon: const Icon(Icons.keyboard_arrow_down_rounded, color: BPColor.whiteText),
                          hintText: LocaleKeys.personal_basic_info_date_birth_PH.tr,
                          errorMessage: controller.birth.errorMsg ?? '',
                          highlight: controller.birth.errorMsg != null,
                          borderEnable: true,
                          borderColor: BPColor.grey2A,
                          controller: controller.birthTEController,
                          textColor: BPColor.whiteText,
                        ),
                        const SizedBox(height: 12),
                        // Gender field
                        Text(
                          LocaleKeys.personal_basic_info_gender.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColor.whiteText,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Obx(
                          () => Row(
                            key: controller.genderKey,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: BPTextButton(
                                  leftIcon: BPImageUtil.image('face_female.png', color: controller.gender.value != BPUserGender.female ? const Color(0xffBDC2C5) : Colors.white),
                                  title: '   ${LocaleKeys.personal_basic_info_female.tr}',
                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                  backgroundColor: controller.gender.value != BPUserGender.female ? BPColor.grey2A : BPColor.brand,
                                  fontSize: 14,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  fontWeight: FontWeight.w500,
                                  color: controller.gender.value != BPUserGender.female ? BPColor.greyText : Colors.white,
                                  onPressed: () {
                                    FocusManager.instance.primaryFocus?.unfocus();
                                    controller.genderChanged(setGender: BPUserGender.female);
                                  },
                                  borderSideColor: BPColor.grey2A,
                                  borderRadius: 8,
                                ),
                              ),
                              const SizedBox(width: 15),
                              Expanded(
                                child: BPTextButton(
                                  leftIcon: BPImageUtil.image('face_male.png', color: controller.gender.value != BPUserGender.male ? const Color(0xffBDC2C5) : Colors.white),
                                  title: '   ${LocaleKeys.personal_basic_info_male.tr}',
                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                  backgroundColor: controller.gender.value != BPUserGender.male ? BPColor.grey2A : BPColor.brand,
                                  fontSize: 14,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  fontWeight: FontWeight.w500,
                                  color: controller.gender.value != BPUserGender.male ? BPColor.greyText : Colors.white,
                                  onPressed: () {
                                    FocusManager.instance.primaryFocus?.unfocus();
                                    controller.genderChanged(setGender: BPUserGender.male);
                                  },
                                  borderSideColor: BPColor.grey2A,
                                  borderRadius: 8,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (controller.gender.value == BPUserGender.unknown) ...{
                          const SizedBox(height: 6),
                          Text(
                            controller.genderError.value,
                            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: BPColor.infoRed),
                          ),
                        },
                        const SizedBox(height: 24),
                        // Nationality field
                        BPBaseInfoInputFiled(
                          key: controller.nationalityKey,
                          title: LocaleKeys.personal_basic_info_nationality.tr,
                          titleStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColor.whiteText,
                          ),
                          backgroundColor: BPColor.grey24,
                          editable: false,
                          onPressed: () {
                            CustomCountryPicker.showCountryPickerBottomSheet(
                              onSelect: (country) {
                                controller.nationalityTEController.text = country.name;
                                controller.nationalityValueSubject.add(country.countryCode);
                                controller.hasModified.value = controller.accountModel?.nationalityCode != country.countryCode;
                                controller.nationality.errorMsg = null;
                                controller.update();
                              },
                            );
                          },
                          rightIcon: const Icon(
                            Icons.keyboard_arrow_down_rounded,
                            color: BPColor.whiteText,
                          ),
                          hintText: LocaleKeys.personal_basic_info_nationality_hint.tr,
                          errorMessage: controller.nationality.errorMsg ?? '',
                          highlight: controller.nationality.errorMsg != null,
                          borderEnable: true,
                          borderColor: BPColor.grey2A,
                          controller: controller.nationalityTEController,
                          textColor: BPColor.whiteText,
                        ),
                        const SizedBox(height: 24),
                        // Phone number field
                        Text(
                          LocaleKeys.personal_basic_info_phone_number.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColor.whiteText,
                          ),
                        ),
                        const SizedBox(height: 12),
                        BPBaseInfoInputFiled(
                          key: controller.phoneKey,
                          backgroundColor: BPColor.grey24,
                          editable: false,
                          onPressed: controller.onPhoneInputTap,
                          maxLength: controller.phoneNumber.maxLength,
                          hintText: LocaleKeys.personal_basic_info_phone_number_PH.tr,
                          errorMessage: controller.phoneNumber.errorMsg ?? '',
                          highlight: controller.phoneNumber.errorMsg != null,
                          borderEnable: true,
                          borderColor: BPColor.grey2A,
                          controller: controller.phoneNumberTEController,
                          keyboardType: TextInputType.phone,
                          textColor: BPColor.whiteText,
                          prefixIcon: Container(
                            constraints: const BoxConstraints(minWidth: 80),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  controller.phoneCodeTEController.text.isEmpty ? "+${Country.tryParse(countryIpResponse?.countryCode ?? "")?.phoneCode}" : controller.phoneCodeTEController.text,
                                  style: controller.phoneCodeTEController.text.isEmpty
                                      ? const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          color: BPColor.greyText,
                                        )
                                      : const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          color: BPColor.whiteText,
                                        ),
                                ),
                                const SizedBox(width: 8),
                                const Icon(Icons.keyboard_arrow_down_rounded, color: BPColor.whiteText),
                                const SizedBox(
                                  height: 30,
                                  child: VerticalDivider(),
                                ),
                              ],
                            ).paddingSymmetric(horizontal: 10),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          LocaleKeys.personal_basic_info_phone_input_tip.tr,
                          style: const TextStyle(
                            color: BPColor.greyText,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Interests field
                        Text(
                          LocaleKeys.edit_profile_interests_title.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColor.whiteText,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() => GestureDetector(
                              onTap: () async {
                                await controller.onTapInterests();
                              },
                              child: SectionContainer(
                                key: controller.interestsKey,
                                errorMessage: controller.interestsError.value,
                                child: (controller.tempUser.value.interests ?? controller.interests).isNotEmpty
                                    ? Wrap(
                                        spacing: 16,
                                        runSpacing: 12,
                                        children: List.generate(
                                          controller.tempUser.value.interests?.length ?? 0,
                                          (index) {
                                            var interest = controller.tempUser.value.interests![index];

                                            return Chip(
                                              side: BorderSide.none,
                                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40)),
                                              label: Text(interest.name ?? ''),
                                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                              backgroundColor: BPColor.gold,
                                              labelStyle: const TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.w500),
                                            );
                                          },
                                        ),
                                      )
                                    : Text(
                                        LocaleKeys.edit_profile_tap_to_edit_tip.tr,
                                        style: const TextStyle(color: BPColor.greyText, fontSize: 14, fontWeight: FontWeight.w400),
                                      ),
                              ),
                            )),
                        const SizedBox(height: 24),
                        // About me field
                        Text(
                          LocaleKeys.edit_profile_aboutme_title.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColor.whiteText,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() => GestureDetector(
                              onTap: () async {
                                await controller.onTapIntroduction();
                              },
                              child: SectionContainer(
                                key: controller.introductionKey,
                                errorMessage: controller.introductionError.value,
                                child: Text(
                                  (controller.tempUser.value.introduction ?? controller.introduction) == '' ? LocaleKeys.edit_profile_tap_to_edit_tip.tr : controller.tempUser.value.introduction ?? controller.introduction,
                                  style: TextStyle(
                                    color: (controller.tempUser.value.introduction ?? controller.introduction) == '' ? BPColor.greyText : BPColor.whiteText,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            )),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
