import '../../../data/BPAccountModels.dart';
import '../../../data/BPUserModels.dart';

/// Utility class for asset verification display methods
class AssetVerificationUtils {
  /// Get user display name with proper fallback logic
  /// Priority order: real name > fake name > username > empty
  static String getUserDisplayName(BPUserModel? userModel) {
    if (userModel == null) return '';

    // Priority order: real name > fake name > username > empty
    if (userModel.fullname != null && userModel.fullname!.isNotEmpty) {
      return userModel.fullname!;
    } else if (userModel.fakeName != null && userModel.fakeName!.isNotEmpty) {
      return userModel.fakeName!;
    } else if (userModel.username != null && userModel.username!.isNotEmpty) {
      return userModel.username!;
    }

    return '';
  }

  /// Get asset range text based on AssetResult
  static String getAssetRangeText(AssetResult? assetResult) {
    if (assetResult == null) return '100-500억';

    switch (assetResult) {
      case AssetResult.moreThan100K:
        return '100-500억';
      case AssetResult.moreThan500K:
        return '500억-1조';
      case AssetResult.moreThan1M:
        return '1-5조';
      case AssetResult.moreThan5M:
        return '5-10조';
      case AssetResult.moreThan10M:
        return '10-50조';
      case AssetResult.moreThan50M:
        return '50-100조';
      case AssetResult.moreThan100M:
        return '100조+';
    }
  }

  /// Get asset range text based on BPFinancialStatus (for compatibility)
  static String getAssetRangeTextFromFinancialStatus(BPFinancialStatus? financialStatus) {
    if (financialStatus == null) return '100-500억';

    switch (financialStatus) {
      case BPFinancialStatus.moreThan100K:
        return '100-500억';
      case BPFinancialStatus.moreThan500K:
        return '500억-1조';
      case BPFinancialStatus.moreThan1M:
        return '1-5조';
      case BPFinancialStatus.moreThan5M:
        return '5-10조';
      case BPFinancialStatus.moreThan10M:
        return '10-50조';
      case BPFinancialStatus.moreThan50M:
        return '50-100조';
      case BPFinancialStatus.moreThan100M:
        return '100조+';
      default:
        return '100-500억';
    }
  }

  /// Get formatted expiration date
  /// If expiryDate is provided, format it. Otherwise, default to 1 year from now.
  static String getValidUntilDate(DateTime? expiryDate) {
    if (expiryDate != null) {
      return '${expiryDate.year}.${expiryDate.month.toString().padLeft(2, '0')}.${expiryDate.day.toString().padLeft(2, '0')}';
    }

    // Fallback to default behavior (1 year from now)
    final now = DateTime.now();
    final validUntil = DateTime(now.year + 1, now.month, now.day);
    return '${validUntil.year}.${validUntil.month.toString().padLeft(2, '0')}.${validUntil.day.toString().padLeft(2, '0')}';
  }

  /// Get verification number from user data
  /// This can be user ID or any other identifier
  static String getVerificationNumber(BPUserModel? userModel) {
    if (userModel?.id != null) {
      return userModel!.id.toString();
    }
    return 'N/A';
  }
} 