import 'package:flutter/material.dart';
import 'package:starchex/app/shared/components/BPImageUtil.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../data/AssetVerificationModel.dart';

class AssetIconWidget extends StatelessWidget {
  final AssetType assetType;
  final double size;
  final bool isGoldBackground;
  final bool withBackground;

  const AssetIconWidget({
    super.key,
    required this.assetType,
    this.size = 48,
    this.isGoldBackground = false,
    this.withBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: withBackground
          ? BoxDecoration(
        color: isGoldBackground 
            ? BPColor.brand 
            : const Color(0xFF363636),
        borderRadius: BorderRadius.circular(size),
            )
          : null,
      child: Padding(
        padding: withBackground ? const EdgeInsets.all(12) : EdgeInsets.zero,
        child: BPImageUtil.scSvgImage(
          _getAssetTypeSvgPath(assetType),
        ),
      ),
    );
  }

  String _getAssetTypeSvgPath(AssetType type) {
    switch (type) {
      case AssetType.realEstate:
        return 'assets_verify_estate.svg';
      case AssetType.financialAssets:
        return 'assets_verify_financial.svg';
      case AssetType.cryptoCurrency:
        return 'assets_verify_crypto.svg';
      case AssetType.others:
        return 'assets_verify_others.svg';
    }
  }
}