import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/BPImageUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../../generated/locales.g.dart';
import '../controllers/asset_verification_controller.dart';

class IdUploadPage extends GetView<AssetVerificationController> {
  const IdUploadPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 描述文本
          MVText(
            LocaleKeys.asset_verification_upload_id_description.tr,
            fontSize: 16,
            color: BPColor.greyText,
          ),
          const SizedBox(height: 32),

          // ID卡片上传区域
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: BPColor.backgroundGrey,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: BPColor.grey66),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: BPColor.backgroundGrey,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: BPImageUtil.scSvgImage(
                        'assets_ID_with_bg.svg',
                      ),
                    ),
                    const SizedBox(width: 16),
                    MVText(
                      LocaleKeys.asset_verification_id_card.tr,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                MVText(
                  LocaleKeys.asset_verification_file_format_info.tr,
                  fontSize: 14,
                  color: BPColor.greyText,
                ),
                const SizedBox(height: 36),

                // 上传按钮或已上传状态
                Obx(() {
                  if (controller.uploadedFile.value != null || controller.existingIdCard.value != null) {
                    return _buildUploadedState();
                  } else {
                    return _buildUploadButton();
                  }
                }),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // ID提交指南
          MVText(
            LocaleKeys.asset_verification_id_submission_guidelines.tr,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          const SizedBox(height: 16),

          _buildGuidelineItem(
            LocaleKeys.asset_verification_guideline_clear_image.tr,
          ),
          _buildGuidelineItem(
            LocaleKeys.asset_verification_guideline_include_original.tr,
          ),
          _buildGuidelineItem(
            LocaleKeys.asset_verification_guideline_masking.tr,
          ),
          _buildGuidelineItem(
            LocaleKeys.asset_verification_guideline_updated_info.tr,
          ),

          const SizedBox(height: 80),
        ],
      ),
    );
  }

  Widget _buildUploadButton() {
    return GestureDetector(
      onTap: controller.uploadFile,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          BPImageUtil.scSvgImage('union_gold.svg'),
          const SizedBox(width: 8),
          Text(
            LocaleKeys.asset_verification_upload_file.tr,
            style: const TextStyle(
              fontSize: 12,
              color: BPColor.gold,
              decoration: TextDecoration.underline,
              decorationColor: BPColor.gold,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildUploadedState() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: BPColor.gold.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: BPColor.gold),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle,
            color: BPColor.gold,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: MVText(
              _getDisplayFileName(),
              fontSize: 14,
              color: Colors.white,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: controller.uploadFile,
            child: Text(
              _getChangeButtonText(),
              style: const TextStyle(
                fontSize: 12,
                color: BPColor.gold,
                decoration: TextDecoration.underline,
                decorationColor: BPColor.gold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getDisplayFileName() {
    if (controller.uploadedFile.value != null) {
      return controller.uploadedFileName.value;
    } else if (controller.existingIdCard.value != null) {
      return controller.existingIdCard.value!.name ?? 'Existing ID Card';
    }
    return '';
  }

  String _getChangeButtonText() {
    if (controller.existingIdCard.value != null && controller.uploadedFile.value == null) {
      return 'Update';
    }
    return 'Change';
  }

  Widget _buildGuidelineItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: const BoxDecoration(
              color: BPColor.greyText,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: MVText(
              text,
              fontSize: 14,
              color: BPColor.greyText,
            ),
          ),
        ],
      ),
    );
  }
}
