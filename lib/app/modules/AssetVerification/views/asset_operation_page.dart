import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:starchex/app/data/AssetVerificationModel.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/BPImageUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../../generated/locales.g.dart';
import '../controllers/asset_verification_controller.dart';
import '../widgets/asset_icon_widget.dart';

/// Custom painter for drawing dashed lines
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class AssetOperationPage extends GetView<AssetVerificationController> {
  const AssetOperationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Obx(() {
        final assetType = controller.currentAssetType.value;
        if (assetType == null) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 资产类型标题
            _buildAssetTypeHeader(assetType),
            const SizedBox(height: 24),

            // 资产表单列表
            _buildAssetFormsList(),

            const SizedBox(height: 24),

            // 添加新资产按钮
            _buildAddNewAssetButton(),

            const SizedBox(height: 24),

            // 资产相关的指导说明和免责声明
            _buildAssetGuidelines(assetType),

            const SizedBox(height: 80),
          ],
        );
      }),
    );
  }

  Widget _buildAssetTypeHeader(AssetType assetType) {
    return MVText(
      _getAssetTypeName(assetType),
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    );
  }

  Widget _buildAssetFormsList() {
    return Obx(() {
      return Column(
        children: controller.currentTypeAssetForms.asMap().entries.map((entry) {
          final index = entry.key;
          final form = entry.value;
          return _buildAssetFormCard(form, index);
        }).toList(),
      );
    });
  }

  Widget _buildAssetFormCard(AssetFormData form, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          // 表单头部 - 可折叠标题
          _buildFormHeader(form, index),

          // 表单内容 - 可折叠
          if (form.isExpanded) ...[
            _buildFormContent(form, index),
          ],
          
          // 虚线分隔符
          Container(
            margin: const EdgeInsets.only(top: 16),
            child: CustomPaint(
              size: const Size(double.infinity, 1),
              painter: DashedLinePainter(
                color: BPColor.greyText.withOpacity(0.3),
                strokeWidth: 1,
                dashWidth: 5,
                dashSpace: 3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormHeader(AssetFormData form, int index) {
    return GestureDetector(
      onTap: () => controller.toggleFormExpanded(index),
      // borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                // 现有数据标识图标
                if (form.isExistingData) ...[
                  Icon(
                    Icons.cloud_done,
                    color: BPColor.gold.withOpacity(0.8),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                ],

                // 标题文本
                MVText(
                  // form.name.isEmpty ? '${_getAssetTypeName(form.type)} ${index + 1}' : form.name,
                  '${_getAssetTypeName(form.type)} ${index + 1}',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: form.isExistingData ? BPColor.gold.withOpacity(0.8) : BPColor.gold,
                ), 
                const SizedBox(width: 16),
                // 折叠/展开图标
                Icon(
                  form.isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: form.isExistingData ? BPColor.gold.withOpacity(0.8) : BPColor.gold,
                  size: 20,
                ),
              ],
            ),
            if (!form.isExistingData && controller.currentTypeAssetForms.length > 1) ...[
              GestureDetector(
                onTap: () => controller.removeAssetForm(index),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  child: const Icon(
                    Icons.delete_outline,
                    color: BPColor.brand,
                    size: 20,
                  ),
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }

  Widget _buildFormContent(AssetFormData form, int index) {
    return Container(
      // padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 资产名称输入
          _buildAssetNameInput(form, index),
          const SizedBox(height: 20),

          // 文档证明上传
          _buildDocumentUpload(form, index),
          const SizedBox(height: 16),

          // 已选择的文件列表
          _buildSelectedFilesList(form, index),
          const SizedBox(height: 16),

          // 描述输入（可选）
          _buildDescriptionInput(form, index),
        ],
      ),
    );
  }

  Widget _buildAssetNameInput(AssetFormData form, int index) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              MVText(
                LocaleKeys.asset_verification_asset_name.tr,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              const SizedBox(width: 4),
              MVText(
                '*',
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
              if (form.isExistingData) ...[
                const SizedBox(width: 8),
                MVText(
                  '(${LocaleKeys.asset_verification_uploaded_file.tr})',
                  fontSize: 12,
                  color: BPColor.greyText,
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: form.isExistingData ? const Color(0xFF1A1A1A).withOpacity(0.6) : const Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(8),
              border: form.isExistingData ? Border.all(color: BPColor.gold.withOpacity(0.3), width: 1) : null,
            ),
            child: TextField(
              controller: form.nameController,
              enabled: !form.isExistingData, // 现有数据设为只读
              style: TextStyle(
                color: form.isExistingData ? Colors.white.withOpacity(0.7) : Colors.white,
              ),
              textInputAction: TextInputAction.done,
              onSubmitted: (_) {
                // 当用户按下完成按钮时，取消焦点
                FocusScope.of(context).unfocus();
              },
              decoration: InputDecoration(
                hintText: form.isExistingData ? null : _getAssetNamePlaceholder(form.type),
                hintStyle: TextStyle(color: BPColor.greyText),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
                suffixIcon: form.isExistingData
                    ? Icon(
                        Icons.lock_outline,
                        color: BPColor.greyText,
                        size: 20,
                      )
                    : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentUpload(AssetFormData form, int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            MVText(
              LocaleKeys.asset_verification_document_of_proof.tr,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            const SizedBox(width: 4),
            MVText(
              '*',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ],
        ),
        const SizedBox(height: 4),
        MVText(
          _getDocumentExamples(form.type),
          fontSize: 14,
          color: BPColor.greyText,
        ),
        const SizedBox(height: 12),
        
        // 如果是现有数据且有文件，显示简化的文件信息
        if (form.isExistingData && form.fileNames.isNotEmpty) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: BPColor.gold.withOpacity(0.3), width: 1),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.cloud_done,
                      color: BPColor.gold,
                      size: 48,
                    ),
                    const SizedBox(width: 16),
                    MVText(
                      LocaleKeys.asset_verification_uploaded_file.tr,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: BPColor.gold,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                MVText(
                  '${form.fileNames.length} ${form.fileNames.length == 1 ? "file" : "files"} uploaded',
                  fontSize: 14,
                  color: BPColor.greyText,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ] else ...[
          // 如果是新数据或没有文件，显示上传区域
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AssetIconWidget(
                      assetType: form.type,
                      size: 48,
                      isGoldBackground: false,
                    ),
                    const SizedBox(width: 16),
                    MVText(
                      _getAssetTypeName(form.type),
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                MVText(
                  LocaleKeys.asset_verification_file_format_info.tr,
                  fontSize: 14,
                  color: BPColor.greyText,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 36),

                // 上传按钮
                _buildUploadButton(index),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSelectedFilesList(AssetFormData form, int index) {
    if (form.fileNames.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MVText(
              form.isExistingData ? LocaleKeys.asset_verification_existing_files.tr : LocaleKeys.asset_verification_selected_files.tr,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            // 添加更多文件按钮（只对新数据显示）
            if (!form.isExistingData) ...[
              Builder(
                builder: (context) => GestureDetector(
                  onTap: () {
                    // 在文件选择前取消焦点，确保键盘收回
                    FocusScope.of(context).unfocus();
                    controller.selectFilesForForm(index, replaceExisting: false);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1A1A1A),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: BPColor.gold, width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.add,
                          color: BPColor.gold,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        MVText(
                          LocaleKeys.asset_verification_add_more_files.tr,
                          fontSize: 12,
                          color: BPColor.gold,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 12),
        ...form.fileNames.asMap().entries.map((entry) {
          final fileIndex = entry.key;
          final fileName = entry.value;
          return _buildSelectedFileItem(fileName, index, fileIndex, form.isExistingData);
        }),
      ],
    );
  }

  Widget _buildSelectedFileItem(String fileName, int formIndex, int fileIndex, bool isExistingFile) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isExistingFile ? const Color(0xFF1A1A1A).withOpacity(0.8) : const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(8),
        border: isExistingFile ? Border.all(color: BPColor.gold.withOpacity(0.3), width: 1) : null,
      ),
      child: Row(
        children: [
          Icon(
            isExistingFile ? Icons.cloud_done : Icons.insert_drive_file,
            color: isExistingFile ? BPColor.gold.withOpacity(0.8) : BPColor.gold,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MVText(
                  fileName,
                  fontSize: 14,
                  color: Colors.white,
                ),
                if (isExistingFile) ...[
                  const SizedBox(height: 2),
                  MVText(
                    LocaleKeys.asset_verification_uploaded_file.tr,
                    fontSize: 12,
                    color: BPColor.greyText,
                  ),
                ],
              ],
            ),
          ),
          // 只有新上传的文件才显示删除按钮
          if (!isExistingFile) ...[
            GestureDetector(
              onTap: () => controller.removeFileFromForm(formIndex, fileIndex),
              child: Container(
                padding: const EdgeInsets.all(4),
                child: BPImageUtil.scSvgImage('clear_btn.svg', width: 24, height: 24),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDescriptionInput(AssetFormData form, int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            MVText(
              LocaleKeys.asset_verification_description_optional.tr,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            if (form.isExistingData) ...[
              const SizedBox(width: 8),
              MVText(
                '(${LocaleKeys.asset_verification_uploaded_file.tr})',
                fontSize: 12,
                color: BPColor.greyText,
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: form.isExistingData ? const Color(0xFF1A1A1A).withOpacity(0.6) : const Color(0xFF1A1A1A),
            borderRadius: BorderRadius.circular(8),
            border: form.isExistingData ? Border.all(color: BPColor.gold.withOpacity(0.3), width: 1) : null,
          ),
          child: TextField(
            controller: form.descriptionController,
            enabled: !form.isExistingData, // 现有数据设为只读
            maxLines: 4,
            style: TextStyle(
              color: form.isExistingData ? Colors.white.withOpacity(0.7) : Colors.white,
            ),
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              hintText: form.isExistingData ? null : LocaleKeys.asset_verification_description_placeholder.tr,
              hintStyle: TextStyle(color: BPColor.greyText),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              suffixIcon: form.isExistingData
                  ? Padding(
                      padding: const EdgeInsets.only(top: 8, right: 8),
                      child: Icon(
                        Icons.lock_outline,
                        color: BPColor.greyText,
                        size: 20,
                      ),
                    )
                  : null,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadButton(int index) {
    return Builder(
      builder: (context) => GestureDetector(
        onTap: () {
          // 在文件选择前取消焦点，确保键盘收回
          FocusScope.of(context).unfocus();
          controller.selectFilesForForm(index, replaceExisting: true);
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            BPImageUtil.scSvgImage('union_gold.svg'),
            const SizedBox(width: 8),
            Text(
              LocaleKeys.asset_verification_upload_file.tr,
              style: const TextStyle(
                fontSize: 12,
                color: BPColor.gold,
                decoration: TextDecoration.underline,
                decorationColor: BPColor.gold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddNewAssetButton() {
    return Container(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: controller.addNewAssetForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1A1A1A),
          foregroundColor: BPColor.gold,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.add,
              color: BPColor.gold,
              size: 20,
            ),
            const SizedBox(width: 8),
            MVText(
              LocaleKeys.asset_verification_add_new_asset.tr,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: BPColor.gold,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssetGuidelines(AssetType assetType) {
    final uploadGuidelines = _getUploadGuidelines(assetType);
    final denialReasons = _getDenialReasons(assetType);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Upload Guidelines
        MVText(
          LocaleKeys.asset_verification_document_upload_guidelines_title.tr,
          fontSize: 14,
          color: BPColor.greyText,
        ),
        const SizedBox(height: 8),
        ...uploadGuidelines.map((guideline) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: BPColor.greyText,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: MVText(
                      guideline,
                      fontSize: 14,
                      color: BPColor.greyText,
                    ),
                  ),
                ],
              ),
            )),

        const SizedBox(height: 24),

        // Denial Reasons
        MVText(
          LocaleKeys.asset_verification_denial_reasons_title.tr,
          fontSize: 14,
          color: BPColor.greyText,
        ),
        const SizedBox(height: 8),
        ...denialReasons.map((reason) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: BPColor.greyText,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: MVText(
                      reason,
                      fontSize: 14,
                      color: BPColor.greyText,
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  String _getAssetNamePlaceholder(AssetType assetType) {
    switch (assetType) {
      case AssetType.realEstate:
        return LocaleKeys.asset_verification_real_estate_name_placeholder.tr;
      case AssetType.financialAssets:
        return LocaleKeys.asset_verification_financial_assets_name_placeholder.tr;
      case AssetType.cryptoCurrency:
        return LocaleKeys.asset_verification_crypto_currency_name_placeholder.tr;
      case AssetType.others:
        return LocaleKeys.asset_verification_other_assets_name_placeholder.tr;
    }
  }

  String _getDocumentExamples(AssetType assetType) {
    switch (assetType) {
      case AssetType.realEstate:
        return LocaleKeys.asset_verification_real_estate_document_desc.tr;
      case AssetType.financialAssets:
        return LocaleKeys.asset_verification_financial_assets_document_desc.tr;
      case AssetType.cryptoCurrency:
        return LocaleKeys.asset_verification_crypto_currency_document_desc.tr;
      case AssetType.others:
        return LocaleKeys.asset_verification_other_assets_document_desc.tr;
    }
  }

  String _getAssetTypeName(AssetType assetType) {
    switch (assetType) {
      case AssetType.realEstate:
        return LocaleKeys.asset_verification_real_estate.tr;
      case AssetType.financialAssets:
        return LocaleKeys.asset_verification_financial_assets.tr;
      case AssetType.cryptoCurrency:
        return LocaleKeys.asset_verification_crypto_currency.tr;
      case AssetType.others:
        return LocaleKeys.asset_verification_other_assets.tr;
    }
  }

  List<String> _getUploadGuidelines(AssetType assetType) {
    switch (assetType) {
      case AssetType.realEstate:
        return [
          LocaleKeys.asset_verification_real_estate_guideline_1.tr,
          LocaleKeys.asset_verification_real_estate_guideline_2.tr,
          LocaleKeys.asset_verification_real_estate_guideline_3.tr,
        ];
      case AssetType.financialAssets:
        return [
          LocaleKeys.asset_verification_financial_assets_guideline_1.tr,
          LocaleKeys.asset_verification_financial_assets_guideline_2.tr,
          LocaleKeys.asset_verification_financial_assets_guideline_3.tr,
        ];
      case AssetType.cryptoCurrency:
        return [
          LocaleKeys.asset_verification_crypto_currency_guideline_1.tr,
          LocaleKeys.asset_verification_crypto_currency_guideline_2.tr,
          LocaleKeys.asset_verification_crypto_currency_guideline_3.tr,
        ];
      case AssetType.others:
        return [
          LocaleKeys.asset_verification_other_assets_guideline_1.tr,
          LocaleKeys.asset_verification_other_assets_guideline_2.tr,
          LocaleKeys.asset_verification_other_assets_guideline_3.tr,
        ];
    }
  }

  List<String> _getDenialReasons(AssetType assetType) {
    List<String> commonReasons = [
      LocaleKeys.asset_verification_denial_reason_info_inconsistency.tr,
      LocaleKeys.asset_verification_denial_reason_title_inconsistency.tr,
      LocaleKeys.asset_verification_denial_reason_proof_ownership.tr,
    ];

    switch (assetType) {
      case AssetType.realEstate:
        return [
          LocaleKeys.asset_verification_real_estate_denial_unregistered.tr,
          LocaleKeys.asset_verification_real_estate_denial_disputes.tr,
          LocaleKeys.asset_verification_real_estate_denial_other_names.tr,
          LocaleKeys.asset_verification_real_estate_denial_forgery.tr,
          ...commonReasons,
        ];
      case AssetType.financialAssets:
        return [
          LocaleKeys.asset_verification_financial_denial_uncertain_maturity.tr,
          LocaleKeys.asset_verification_financial_denial_high_risk.tr,
          ...commonReasons,
        ];
      case AssetType.cryptoCurrency:
        return [
          LocaleKeys.asset_verification_crypto_denial_unregistered_exchange.tr,
          LocaleKeys.asset_verification_crypto_denial_low_liquidity.tr,
          LocaleKeys.asset_verification_crypto_denial_extreme_volatility.tr,
          ...commonReasons,
        ];
      case AssetType.others:
        return [
          LocaleKeys.asset_verification_other_denial_market_uncertainty.tr,
          LocaleKeys.asset_verification_other_denial_personal_agreement.tr,
          LocaleKeys.asset_verification_other_denial_unclear_ownership.tr,
          ...commonReasons,
        ];
    }
  }
}
