import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../../generated/locales.g.dart';
import '../controllers/asset_verification_controller.dart';
import '../widgets/asset_icon_widget.dart';
import '../../../data/AssetVerificationModel.dart';

class AssetSelectionPage extends GetView<AssetVerificationController> {
  const AssetSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 描述文本
          MVText(
            LocaleKeys.asset_verification_select_assets_description.tr,
            fontSize: 16,
            color: BPColor.greyText,
          ),
          const SizedBox(height: 24),

          // 可点击的资产类型网格
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.1,
            children: [
              _buildClickableAssetCard(
                assetType: AssetType.realEstate,
                icon: Icons.home_outlined,
                title: LocaleKeys.asset_verification_real_estate.tr,
                description: LocaleKeys.asset_verification_real_estate_desc.tr,
              ),
              _buildClickableAssetCard(
                assetType: AssetType.financialAssets,
                icon: Icons.trending_up,
                title: LocaleKeys.asset_verification_financial_assets.tr,
                description: LocaleKeys.asset_verification_financial_assets_desc.tr,
              ),
              _buildClickableAssetCard(
                assetType: AssetType.cryptoCurrency,
                icon: Icons.currency_bitcoin,
                title: LocaleKeys.asset_verification_crypto_currency.tr,
                description: LocaleKeys.asset_verification_crypto_currency_desc.tr,
              ),
              _buildClickableAssetCard(
                assetType: AssetType.others,
                icon: Icons.directions_car_outlined,
                title: LocaleKeys.asset_verification_other_assets.tr,
                description: LocaleKeys.asset_verification_other_assets_desc.tr,
              ),
            ],
          ),

          const SizedBox(height: 32),

          // 资产表单状态概览
          _buildAssetFormsOverview(),

          const SizedBox(height: 80),
        ],
      ),
    );
  }

  Widget _buildClickableAssetCard({
    required AssetType assetType,
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Obx(() {
      // 检查该资产类型是否有有效数据
      bool hasValidData = _getValidFormsCount(assetType) > 0;
      
      return GestureDetector(
        onTap: () => controller.onAssetTypeClicked(assetType),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: hasValidData ? BPColor.brand.withOpacity(0.2) : const Color(0xFF242424),
            borderRadius: BorderRadius.circular(18),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AssetIconWidget(
                assetType: assetType,
                size: 48,
                isGoldBackground: hasValidData,
              ),
              const SizedBox(height: 12),
              MVText(
                title,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: hasValidData ? BPColor.brand : Colors.white,
              ),
              const SizedBox(height: 4),
              MVText(
                description,
                fontSize: 12,
                color: BPColor.greyText,
                maxLines: 2,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildAssetFormsOverview() {
    return Obx(() {
      // 检查是否有任何资产类型有表单数据
      bool hasAnyAssets = _hasAnyValidAssetForms();

      if (!hasAnyAssets) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: BPColor.backgroundGrey,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: BPColor.grey66),
          ),
          child: Column(
            children: [
              const Icon(
                Icons.info_outline,
                color: BPColor.greyText,
                size: 48,
              ),
              const SizedBox(height: 16),
              MVText(
                LocaleKeys.asset_verification_tap_to_add_asset.tr,
                fontSize: 16,
                color: BPColor.greyText,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MVText(
            LocaleKeys.asset_verification_list_of_added_assets.tr,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          const SizedBox(height: 16),

          // 显示每种资产类型的状态
          ...AssetType.values
              .map((assetType) {
                return _buildAssetTypeStatus(assetType);
              })
              .where((widget) => widget != null)
              .cast<Widget>(),
        ],
      );
    });
  }

  Widget? _buildAssetTypeStatus(AssetType assetType) {
    // 获取该资产类型的有效表单数据
    final validForms = _getValidAssetForms(assetType);

    if (validForms.isEmpty) {
      return null; // 如果没有有效表单，不显示
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 资产类型标题
        // Container(
        //   margin: const EdgeInsets.only(bottom: 8),
        //   child: Row(
        //     children: [
        //       AssetIconWidget(
        //         assetType: assetType,
        //         size: 24,
        //         isGoldBackground: true,
        //         withBackground: false,
        //       ),
        //       const SizedBox(width: 8),
        //       MVText(
        //         _getAssetTypeName(assetType),
        //         fontSize: 16,
        //         fontWeight: FontWeight.bold,
        //         color: Colors.white,
        //       ),
        //     ],
        //   ),
        // ),

        // 每个具体的资产项
        ...validForms.map((form) => _buildAssetItem(form)),

        const SizedBox(height: 16),
      ],
    );
  }

  bool _hasAnyValidAssetForms() {
    return AssetType.values.any((assetType) => _getValidFormsCount(assetType) > 0);
  }

  int _getValidFormsCount(AssetType assetType) {
    // 使用控制器中的方法获取有效表单数量
    return controller.getValidFormsCount(assetType);
  }

  List<AssetFormData> _getValidAssetForms(AssetType assetType) {
    // 获取指定资产类型的所有有效表单数据
    final forms = controller.allAssetForms[assetType];
    if (forms == null) return [];
    return forms.where((form) => form.isValid).toList();
  }

  Widget _buildAssetItem(AssetFormData form) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
      decoration: BoxDecoration(
        color: BPColor.grey24,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // 资产类型图标
          AssetIconWidget(
            assetType: form.type,
            size: 24,
            isGoldBackground: true,
            withBackground: false,
          ),
          const SizedBox(width: 8),
          // 资产名称
          Expanded(
            child: MVText(
              form.name.isNotEmpty ? form.name : 'Unnamed Asset',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  String _getAssetTypeName(AssetType assetType) {
    switch (assetType) {
      case AssetType.realEstate:
        return LocaleKeys.asset_verification_real_estate.tr;
      case AssetType.financialAssets:
        return LocaleKeys.asset_verification_financial_assets.tr;
      case AssetType.cryptoCurrency:
        return LocaleKeys.asset_verification_crypto_currency.tr;
      case AssetType.others:
        return LocaleKeys.asset_verification_other_assets.tr;
    }
  }
}
