import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:starchex/app/shared/components/BPImageUtil.dart';
import '../../../shared/components/BPEasyLoading.dart';
import '../../../shared/networking/BPRestAPI.dart';
import '../../../shared/account/BPSessionManager.dart';
import '../widgets/asset_verification_utils.dart';
import 'dart:convert';

class QRCodeDisplayPage extends StatefulWidget {
  const QRCodeDisplayPage({super.key});

  @override
  State<QRCodeDisplayPage> createState() => _QRCodeDisplayPageState();
}

class _QRCodeDisplayPageState extends State<QRCodeDisplayPage> {
  String? qrCodeBase64;
  String? qrData;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _generateQRCode();
  }

  void _generateQRCode() async {
    try {
      print('Starting QR Code generation...');
      final response = await BPRestClient().getMyQRCode();
      
      print('=== QR Code API Response Debug ===');
      print('Response: $response');
      print('Response Type: ${response.runtimeType}');
      print('Response toString: ${response.toString()}');
      
      // 直接检查响应是否为空字符串
      if (response == null || response.toString().isEmpty) {
        print('Response is null or empty');
        _handleFallbackMode();
        return;
      }
      
      // 处理不同类型的响应
      Map<String, dynamic>? data;
      
      // 如果响应是字符串，尝试解析为JSON
      if (response is String) {
        print('Response is String, attempting to parse JSON...');
        try {
          final jsonData = jsonDecode(response);
          print('Parsed JSON: $jsonData');
          if (jsonData is Map<String, dynamic>) {
            data = jsonData;
          }
        } catch (e) {
          print('Failed to parse response as JSON: $e');
          // 如果解析失败，可能是空字符串或无效JSON
          _handleFallbackMode();
          return;
        }
      } else if (response is Map<String, dynamic>) {
        print('Response is already a Map');
        data = response;
      } else {
        print('Response is unexpected type: ${response.runtimeType}');
        _handleFallbackMode();
        return;
      }
      
      // 检查是否有有效数据
      if (data != null && data.containsKey('data') && data['data'] != null) {
        try {
          final responseData = data['data'];
          print('Response data: $responseData');
          print('Response data type: ${responseData.runtimeType}');
          
          if (responseData is Map<String, dynamic>) {
            final qrCodeBase64Temp = responseData['qrCodeBase64'] as String?;
            final qrDataTemp = responseData['qrData'] as String?;
            
            print('QR Data: $qrDataTemp');
            print('QR Code Base64 length: ${qrCodeBase64Temp?.length}');
            
            setState(() {
              qrCodeBase64 = qrCodeBase64Temp;
              qrData = qrDataTemp;
              isLoading = false;
            });
            print('QR Code data loaded successfully');
          } else {
            print('Response data is not a Map: ${responseData.runtimeType}');
            _handleFallbackMode();
          }
        } catch (e) {
          print('Error parsing response data: $e');
          _handleFallbackMode();
        }
      } else {
        print('No valid data found in response');
        print('Data keys: ${data?.keys}');
        _handleFallbackMode();
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('QR Code API Error: $e');
      print('Error stack trace: ${e.toString()}');
      BPEasyLoading.showError('Error generating QR code: ${e.toString()}');
    }
  }
  
  void _handleFallbackMode() {
    // 使用用户ID作为QR码数据进行测试
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    if (userModel?.id != null) {
      print('Using fallback QR code generation for user ID: ${userModel!.id}');
      setState(() {
        qrData = userModel.id.toString();
        // 临时生成一个简单的QR码占位符
        qrCodeBase64 = null; // 这会显示"不可用"的UI，但保存了用户ID
        isLoading = false;
      });
      BPEasyLoading.showError('QR code API returned invalid data. Using fallback mode.');
    } else {
      setState(() {
        isLoading = false;
      });
      BPEasyLoading.showError('QR code sharing is not enabled or data is unavailable');
    }
  }

  @override
  Widget build(BuildContext context) {
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    final userName = AssetVerificationUtils.getUserDisplayName(userModel);
    final financialStatus = userModel?.financialStatus;
    
    // Get the asset range text based on financial status
    final assetRangeText = AssetVerificationUtils.getAssetRangeTextFromFinancialStatus(financialStatus);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.close, color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement share functionality
              BPEasyLoading.showToast('Share functionality coming soon!');
            },
            icon: const Icon(Icons.share, color: Colors.white),
          ),
        ],
      ),
      body: isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Colors.white,
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    // QR Code Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2A2315),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: const Color(0xFFABABAB).withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          // Starchex logo
                          Row(
                            children: [
                              BPImageUtil.scSvgImage('cert_logo.svg'),
                            ],
                          ),
                          const SizedBox(height: 20),
                          
                          // User info section
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Name label
                                    Text(
                                      'Name',
                                      style: TextStyle(
                                        color: Colors.grey[400],
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      userName,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    
                                    // Verification number
                                    Text(
                                      'Verification number',
                                      style: TextStyle(
                                        color: Colors.grey[400],
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'No. ${qrData ?? AssetVerificationUtils.getVerificationNumber(userModel)}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              
                              // Avatar
                              Container(
                                width: 100,
                                height: 100,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: userModel?.fakeAvatar?.url != null
                                      ? Image.network(
                                          userModel!.fakeAvatar!.url!,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) {
                                            return _buildDefaultAvatar();
                                          },
                                        )
                                      : _buildDefaultAvatar(),
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 20),
                          
                          // Asset and Expiration row
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Asset',
                                      style: TextStyle(
                                        color: Colors.grey[400],
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      assetRangeText,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      'Expiration date',
                                      style: TextStyle(
                                        color: Colors.grey[400],
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      AssetVerificationUtils.getValidUntilDate(userModel?.assetCertification?.expirydate),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 30),
                          
                          // QR Code
                          if (qrCodeBase64 != null)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: _buildQRCodeImage(qrCodeBase64!),
                              ),
                            )
                          else
                            Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                color: Colors.grey[800],
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.grey[600]!,
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.qr_code_2,
                                    color: Colors.grey[400],
                                    size: 48,
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    'QR Code\nNot Available',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.grey[400],
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Please enable QR sharing\nin your settings',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.grey[500],
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  TextButton(
                                    onPressed: () {
                                      setState(() {
                                        isLoading = true;
                                      });
                                      _generateQRCode();
                                    },
                                    child: Text(
                                      'Retry',
                                      style: TextStyle(
                                        color: Colors.grey[300],
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xFF2C3E50),
        borderRadius: BorderRadius.circular(10),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 40,
      ),
    );
  }

  Widget _buildQRCodeImage(String qrCodeBase64) {
    try {
      // 处理Base64字符串，移除可能的data URL前缀
      String base64String = qrCodeBase64;
      if (base64String.contains(',')) {
        base64String = base64String.split(',').last;
      }
      
      // 解码Base64
      final bytes = base64Decode(base64String);
      
      return Image.memory(
        bytes,
        width: 200,
        height: 200,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          print('Error loading QR code image: $error');
          return Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text(
                'QR Code\nImage Error',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
          );
        },
      );
    } catch (e) {
      print('Error decoding QR code base64: $e');
      return Container(
        width: 200,
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: Text(
            'QR Code\nDecode Error',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }
  }


} 