import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../shared/components/BPEasyLoading.dart';
import '../../../shared/networking/BPRestAPI.dart';
import '../../../data/BPUserModels.dart';
import '../../../../generated/locales.g.dart';

class ReceivedCertificateController extends GetxController {
  final _receivedUsers = <ReceivedUserModel>[].obs;
  final _isLoading = false.obs;
  final _hasMore = true.obs;
  final _currentPage = 1.obs;
  final _pageSize = 20;
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  List<ReceivedUserModel> get receivedUsers => _receivedUsers;
  bool get isLoading => _isLoading.value;
  bool get hasMore => _hasMore.value;

  @override
  void onInit() {
    super.onInit();
    loadReceivedUsers();
  }

  @override
  void onClose() {
    refreshController.dispose();
    super.onClose();
  }

  Future<void> loadReceivedUsers({bool isRefresh = false}) async {
    if (_isLoading.value && !isRefresh) return;

    try {
      _isLoading.value = true;

      if (isRefresh) {
        _currentPage.value = 1;
        _hasMore.value = true;
      }

      final response = await BPRestClient().getReceivedUsers(
        page: _currentPage.value,
        pageSize: _pageSize,
        sort: 'scannedAt:desc',
      );

      if (isRefresh) {
        _receivedUsers.clear();
      }

      _receivedUsers.addAll(response.data);
      
      // Check if there are more pages
      final pagination = response.meta?.pagination;
      if (pagination != null) {
        _hasMore.value = pagination.hasNextPage;
        if (_hasMore.value) {
          _currentPage.value++;
        }
      } else {
        _hasMore.value = false;
      }

      // Complete refresh or loading
      if (isRefresh) {
        refreshController.refreshCompleted();
      } else {
        if (_hasMore.value) {
          refreshController.loadComplete();
        } else {
          refreshController.loadNoData();
        }
      }
    } catch (e) {
      BPEasyLoading.showError('Failed to load received certificates: ${e.toString()}');
      if (isRefresh) {
        refreshController.refreshFailed();
      } else {
        refreshController.loadFailed();
      }
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadMoreReceivedUsers() async {
    if (!_hasMore.value || _isLoading.value) return;
    await loadReceivedUsers();
  }

  void onRefresh() async {
    await loadReceivedUsers(isRefresh: true);
  }

  void onLoadMore() async {
    await loadMoreReceivedUsers();
  }
}

class ReceivedCertificatePage extends StatelessWidget {
  const ReceivedCertificatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReceivedCertificateController>(
      init: ReceivedCertificateController(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.black,
          body: Column(
            children: [
              // Header count
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Obx(() => MVText(
                      '${controller.receivedUsers.length}개',
                      fontSize: 14,
                      color: Colors.white70,
                    )),
                  ],
                ),
              ),
              
              // List
              Expanded(
                child: Obx(() {
                  if (controller.isLoading && controller.receivedUsers.isEmpty) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: BPColor.gold,
                      ),
                    );
                  }

                  if (controller.receivedUsers.isEmpty) {
                    return Center(
                      child: MVText(
                        '받은 인증서가 없습니다',
                        fontSize: 16,
                        color: Colors.white70,
                        textAlign: TextAlign.center,
                      ),
                    );
                  }

                  return SmartRefresher(
                    header: const WaterDropHeader(
                      complete: Text(''),
                    ),
                    controller: controller.refreshController,
                    onRefresh: controller.onRefresh,
                    onLoading: controller.onLoadMore,
                    enablePullUp: true,
                    enablePullDown: true,
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      itemCount: controller.receivedUsers.length,
                      itemBuilder: (context, index) {
                        final receivedUser = controller.receivedUsers[index];
                        return _buildReceivedUserCard(receivedUser);
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReceivedUserCard(ReceivedUserModel receivedUser) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            begin: Alignment(-0.8, -0.6), // Approximate 156.87 degrees
            end: Alignment(0.8, 0.6),
            colors: [
              Color(0xFF8C7D6D), // 0.58%
              Color(0xFFF1E3C0), // 35.47%
              Color(0xFF8A7654), // 67.97%
              Color(0xFF302B25), // 100%
            ],
            stops: [0.0058, 0.3547, 0.6797, 1.0],
          ),
        ),
        child: Container(
          margin: const EdgeInsets.all(1), // This creates the border width
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF222324),
            borderRadius: BorderRadius.circular(15), // Slightly smaller radius for inner container
          ),
          child: Column(
        children: [
          // First row: Avatar and username/level
          Row(
            children: [
              // Avatar
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF2C3E50),
                  border: Border.all(
                    color: const Color(0xFFD4AF37),
                    width: 2,
                  ),
                ),
                child: ClipOval(
                  child: _buildUserAvatar(receivedUser.user),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Username and level in a column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Username
                    MVText(
                      receivedUser.user?.username ?? 'Unknown User',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Star rating based on level
                    Row(
                      children: List.generate(
                        (receivedUser.user?.level ?? 0).clamp(0, 7),
                        (index) => const Icon(
                          Icons.star,
                          color: BPColor.gold,
                          size: 18,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Other info rows
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title row
              if (receivedUser.user?.title != null && receivedUser.user!.title!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      MVText(
                        '직업',
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                      const SizedBox(width: 16),
                      MVText(
                        receivedUser.user!.title!,
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),

              // Age row
              Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    MVText(
                      '연령대',
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 16),
                    MVText(
                      _formatAge(receivedUser.user?.birthday),
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
              
              // Scanned time row
              if (receivedUser.scannedAt != null)
                Row(
                  children: [
                    MVText(
                      '저장일시',
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 16),
                    MVText(
                      _formatScannedDateTime(receivedUser.scannedAt!),
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ],
                ),
            ],
          ),
        ],
      ),
        ),
      ),
    );
  }

  Widget _buildUserAvatar(BPUserModel? userModel) {
    String? avatarUrl;

    if (userModel != null) {
      // Priority order: fakeAvatar > avatar > fakeRandomAvatar
      if (userModel.fakeAvatar?.url != null && userModel.fakeAvatar!.url!.isNotEmpty) {
        avatarUrl = userModel.fakeAvatar!.url!;
      } else if (userModel.avatar?.url != null && userModel.avatar!.url!.isNotEmpty) {
        avatarUrl = userModel.avatar!.url!;
      } else if (userModel.fakeRandomAvatar != null && userModel.fakeRandomAvatar!.isNotEmpty) {
        avatarUrl = userModel.fakeRandomAvatar!;
      }
    }

    if (avatarUrl != null) {
      return Image.network(
        avatarUrl,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.grey[600],
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: BPColor.gold,
              ),
            ),
          );
        },
      );
    }

    return _buildDefaultAvatar();
  }

  Widget _buildDefaultAvatar() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: Color(0xFF2C3E50),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 24,
      ),
    );
  }

  String _formatAge(DateTime? birthday) {
    if (birthday == null) return '알 수 없음';

    final now = DateTime.now();
    int age = now.year - birthday.year;

    // Check if birthday has passed this year
    if (now.month < birthday.month || (now.month == birthday.month && now.day < birthday.day)) {
      age--;
    }

    // Format age into age groups
    if (age < 20) {
      return '10대';
    } else if (age < 25) {
      return '20대 초반';
    } else if (age < 30) {
      return '20대 후반';
    } else if (age < 35) {
      return '30대 초반';
    } else if (age < 40) {
      return '30대 후반';
    } else if (age < 45) {
      return '40대 초반';
    } else if (age < 50) {
      return '40대 후반';
    } else if (age < 55) {
      return '50대 초반';
    } else if (age < 60) {
      return '50대 후반';
    } else {
      return '60대 이상';
    }
  }

  String _formatScannedDateTime(DateTime scannedAt) {
    // Format: YYYY. M. d HH:mm
    return '${scannedAt.year}. ${scannedAt.month}. ${scannedAt.day} ${scannedAt.hour.toString().padLeft(2, '0')}:${scannedAt.minute.toString().padLeft(2, '0')}';
  }
} 