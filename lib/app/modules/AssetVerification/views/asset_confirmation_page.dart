import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../../generated/locales.g.dart';
import '../controllers/asset_verification_controller.dart';
import '../widgets/asset_icon_widget.dart';
import '../../../data/AssetVerificationModel.dart';

class AssetConfirmationPage extends GetView<AssetVerificationController> {
  const AssetConfirmationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description text
          MVText(
            LocaleKeys.asset_verification_confirmation_description.tr,
            fontSize: 16,
            color: BPColor.whiteText,
          ),
          const SizedBox(height: 32),

          // Assets summary
          _buildAssetsSummary(),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildAssetsSummary() {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: AssetType.values
            .where((assetType) => controller.getValidFormsCount(assetType) > 0)
            .map((assetType) => _buildAssetTypeSummary(assetType))
            .toList(),
      );
    });
  }

  Widget _buildAssetTypeSummary(AssetType assetType) {
    final count = controller.getValidFormsCount(assetType);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2315),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: const Color(0xFFABABAB).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Asset icon
          AssetIconWidget(
            assetType: assetType,
            size: 40,
            isGoldBackground: true,
          ),
          const SizedBox(width: 16),
          MVText(
            '${count.toString()} ${_getAssetTypeName(assetType)}',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: BPColor.brand,
          ),
        ],
      ),
    );
  }





  String _getAssetTypeName(AssetType assetType) {
    switch (assetType) {
      case AssetType.realEstate:
        return LocaleKeys.asset_verification_real_estate.tr;
      case AssetType.financialAssets:
        return LocaleKeys.asset_verification_financial_assets.tr;
      case AssetType.cryptoCurrency:
        return LocaleKeys.asset_verification_crypto_currency.tr;
      case AssetType.others:
        return LocaleKeys.asset_verification_other_assets.tr;
    }
  }
} 