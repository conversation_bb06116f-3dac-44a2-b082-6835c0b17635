import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../shared/components/BPTextButton.dart';
import '../../../routes/app_pages.dart';
import '../../../../generated/locales.g.dart';

class AssetUnderReviewPage extends StatelessWidget {
  const AssetUnderReviewPage({super.key});

  /// Navigate to the asset under review page
  static void show() {
    Get.toNamed(Routes.ASSET_UNDER_REVIEW);
  }

  /// Navigate to the asset under review page and clear navigation stack
  static void showAndClearStack() {
    Get.offAllNamed(Routes.ASSET_UNDER_REVIEW);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            // 返回到主页面而不是资产验证页面，避免循环跳转
            if (Get.currentRoute == Routes.ASSET_UNDER_REVIEW) {
              Get.back();
            }
          },
          icon: const Icon(Icons.arrow_back),
        ),
        title: MVText(
          LocaleKeys.asset_verification_title.tr,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Review icon or illustration
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: BPColor.gold.withOpacity(0.1),
                      border: Border.all(
                        color: BPColor.gold.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.schedule,
                      size: 60,
                      color: BPColor.gold,
                    ),
                  ),
                  const SizedBox(height: 40),
                  
                  // Title
                  MVText(
                    LocaleKeys.asset_verification_under_review_title.tr,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  
                  // Description
                  MVText(
                    LocaleKeys.asset_verification_under_review_description.tr,
                    fontSize: 16,
                    color: BPColor.greyText,
                    textAlign: TextAlign.center,
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  
                  // Additional info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: BPColor.grey(0x1A),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: BPColor.gold,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: MVText(
                                LocaleKeys.asset_verification_under_review_info_title.tr,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        MVText(
                          LocaleKeys.asset_verification_under_review_info_description.tr,
                          fontSize: 14,
                          color: BPColor.greyText,
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Status indicators
                  Column(
                    children: [
                      _buildStatusItem(
                        icon: Icons.check_circle,
                        iconColor: BPColor.gold,
                        title: LocaleKeys.asset_verification_under_review_status_submitted.tr,
                        subtitle: LocaleKeys.asset_verification_under_review_status_submitted_desc.tr,
                        isCompleted: true,
                      ),
                      const SizedBox(height: 12),
                      _buildStatusItem(
                        icon: Icons.schedule,
                        iconColor: BPColor.gold,
                        title: LocaleKeys.asset_verification_under_review_status_reviewing.tr,
                        subtitle: LocaleKeys.asset_verification_under_review_status_reviewing_desc.tr,
                        isCompleted: false,
                      ),
                      const SizedBox(height: 12),
                      _buildStatusItem(
                        icon: Icons.verified,
                        iconColor: BPColor.greyText,
                        title: LocaleKeys.asset_verification_under_review_status_complete.tr,
                        subtitle: LocaleKeys.asset_verification_under_review_status_complete_desc.tr,
                        isCompleted: false,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Bottom button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: BPTextButton(
                title: LocaleKeys.asset_verification_back_to_home.tr,
                onPressed: () {
                  // 返回到主页面
                  Get.back();
                },
                backgroundColor: BPColor.gold,
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                borderRadius: 25,
                minimumSize: const Size(double.infinity, 50),
                mainAxisAlignment: MainAxisAlignment.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required bool isCompleted,
  }) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: iconColor.withOpacity(0.1),
            border: Border.all(
              color: iconColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            size: 20,
            color: iconColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MVText(
                title,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isCompleted ? Colors.white : BPColor.greyText,
              ),
              MVText(
                subtitle,
                fontSize: 12,
                color: BPColor.greyText,
              ),
            ],
          ),
        ),
      ],
    );
  }
} 