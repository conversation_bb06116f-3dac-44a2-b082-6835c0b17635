import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:starchex/app/shared/components/BPImageUtil.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../shared/components/BPTextButton.dart';
import '../../../shared/components/BPEasyLoading.dart';
import '../../../shared/components/BPQRScanner.dart';
import '../../../routes/app_pages.dart';
import '../../../../generated/locales.g.dart';
import '../../../shared/account/BPSessionManager.dart';
import '../../../data/BPAccountModels.dart';
import '../../../data/BPUserModels.dart';
import '../../../data/BPCommonModels.dart';
import '../../../shared/networking/BPRestAPI.dart';
import 'qr_code_display_page.dart';
import 'received_certificate_page.dart';
import '../../ascm/Components/star_level_widget.dart';
import '../widgets/asset_verification_utils.dart';
import 'dart:convert'; // Added for json.decode

class AssetVerificationSuccessPage extends StatefulWidget {
  const AssetVerificationSuccessPage({super.key});

  /// Navigate to the asset verification success page
  static void show() {
    Get.toNamed(Routes.ASSET_VERIFICATION_SUCCESS);
  }

  /// Navigate to the asset verification success page and clear navigation stack
  static void showAndClearStack() {
    Get.offAllNamed(Routes.ASSET_VERIFICATION_SUCCESS);
  }

  @override
  State<AssetVerificationSuccessPage> createState() => _AssetVerificationSuccessPageState();
}

class _AssetVerificationSuccessPageState extends State<AssetVerificationSuccessPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    final userName = AssetVerificationUtils.getUserDisplayName(userModel);
    final assetCertification = userModel?.assetCertification;
    
    // Get the asset range text based on asset certification result
    final assetRangeText = AssetVerificationUtils.getAssetRangeText(assetCertification?.assetResult);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: const Icon(Icons.arrow_back),
        ),
        title: MVText(
          LocaleKeys.asset_verification_success_title.tr,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Tab bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: TabBar(
              controller: _tabController,
              indicator: _CustomTabIndicator(),
              indicatorSize: TabBarIndicatorSize.label,
              dividerColor: Colors.transparent,
              labelColor: Colors.white,
              unselectedLabelColor: BPColor.greyText,
              labelStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
              ),
              tabs: [
                Tab(text: LocaleKeys.asset_verification_success_my_certificate.tr),
                Tab(text: LocaleKeys.asset_verification_success_received_certificate.tr),
              ],
            ),
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // My Certificate Tab
                _buildMyCertificateTab(userModel, userName, assetRangeText, assetCertification),

                // Received Certificate Tab
                const ReceivedCertificatePage(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMyCertificateTab(BPUserModel? userModel, String userName, String assetRangeText, AssetCertificationModel? assetCertification) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Certificate card - Made clickable
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () {
                  _showCertificateDialog(context, userModel, userName, assetRangeText, assetCertification);
                },
                child: Center(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // Calculate responsive scaling
                      double screenWidth = constraints.maxWidth;
                      double baseWidth = 343;
                      double scaleFactor = (screenWidth * 0.9) / baseWidth;
                      scaleFactor = scaleFactor.clamp(0.7, 1.2); // Limit scaling between 70% and 120%

                      double containerWidth = baseWidth * scaleFactor;
                      double containerHeight = 522 * scaleFactor;

                      return Container(
                        width: containerWidth,
                        height: containerHeight,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: BPImageUtil.pngImageProvider('certification_background.png'),
                            fit: BoxFit.contain,
                          ),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: 32 * scaleFactor,
                            vertical: 40 * scaleFactor,
                          ),
                          child: Column(
                            children: [
                              // Certificate title
                              MVText(
                                LocaleKeys.asset_verification_success_certificate_title.tr,
                                fontSize: 20 * scaleFactor,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFFB8860B), // Dark golden rod
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: 32 * scaleFactor),

                              // Avatar with decorative wrapper
                              Stack(
                                alignment: Alignment.center,
                                children: [
                                  // Avatar wrapper background
                                  Container(
                                    width: 190 * scaleFactor,
                                    height: 125 * scaleFactor,
                                    decoration: BoxDecoration(
                                      image: DecorationImage(
                                        image: BPImageUtil.pngImageProvider('certification_avatar_wrapper.png'),
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                  ),
                                  // User avatar
                                  Container(
                                    width: 100 * scaleFactor,
                                    height: 100 * scaleFactor,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      // color: const Color(0xFF2C3E50), // Dark blue-gray
                                      border: Border.all(
                                        color: BPColor.gold, // Blue border
                                        width: 1 * scaleFactor,
                                      ),
                                    ),
                                    child: ClipOval(
                                      child: _buildUserAvatar(userModel),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 24 * scaleFactor),

                              // User name
                              MVText(
                                userName,
                                fontSize: 24 * scaleFactor,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF2C3E50),
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: 16 * scaleFactor),

                              // Star rating
                              Center(
                                child: StarLevelWidget(
                                  level: userModel?.level ?? 0, // 5 stars (4+1)
                                  starWidth: 28 * scaleFactor,
                                  starHeight: 28 * scaleFactor,
                                  spacing: 4 * scaleFactor,
                                ),
                              ),
                              SizedBox(height: 16 * scaleFactor),

                              // Asset range
                              MVText(
                                assetRangeText,
                                fontSize: 18 * scaleFactor,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFFB8860B),
                                textAlign: TextAlign.center,
                              ),
                              
                              // Spacer to push content to bottom
                              const Spacer(),
                              Row(
                                children: [
                                  MVText(
                                    LocaleKeys.asset_verification_success_expiry_date.tr,
                                    fontSize: 14 * scaleFactor,
                                    color: const Color(0xFF9F9F9F),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(width: 4 * scaleFactor),
                                  MVText(
                                    AssetVerificationUtils.getValidUntilDate(assetCertification?.expirydate),
                                    fontSize: 14 * scaleFactor,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                              MVText(
                                LocaleKeys.asset_verification_success_verification_text.tr,
                                fontSize: 12 * scaleFactor,
                                color: const Color(0xFF9F9F9F),
                                textAlign: TextAlign.left,
                                maxLines: 3,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),

          // Bottom buttons
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 50,
                  child: BPTextButton(
                    title: LocaleKeys.asset_verification_success_share.tr,
                    leftIcon: BPImageUtil.scSvgImage('qrcode.svg'),
                    onPressed: () {
                      // TODO: Implement share functionality
                      _shareAssetCertificate();
                    },
                    backgroundColor: BPColor.backgroundGrey,
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    borderRadius: 25,
                    minimumSize: const Size(double.infinity, 50),
                    mainAxisAlignment: MainAxisAlignment.center,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  height: 50,
                  // margin: const EdgeInsets.only(left: 8),
                  child: BPTextButton(
                    title: LocaleKeys.asset_verification_success_qr_scan.tr,
                    leftIcon: BPImageUtil.scSvgImage('qr_scan.svg'),
                    onPressed: () {
                      // TODO: Implement QR scan functionality
                      _showQRCode();
                    },
                    backgroundColor: BPColor.backgroundGrey,
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    borderRadius: 25,
                    minimumSize: const Size(double.infinity, 50),
                    mainAxisAlignment: MainAxisAlignment.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  /// Show certificate dialog popup
  void _showCertificateDialog(BuildContext context, BPUserModel? userModel, String userName, String assetRangeText, AssetCertificationModel? assetCertification) {
    Get.dialog(
      Dialog(
        backgroundColor: const Color(0xFF1A1A1A),
        insetPadding: const EdgeInsets.all(0), // Remove default padding
        child: Container(
          width: double.infinity, // Full width
          height: double.infinity, // Full height
          decoration: BoxDecoration(
            color: const Color(0xFF1A1A1A), // Dark background
            borderRadius: BorderRadius.circular(0), // Remove border radius for full screen
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Header with close and share buttons
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.close, color: Colors.white, size: 24),
                    ),
                    MVText(
                      LocaleKeys.asset_verification_success_my_certificate.tr,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    IconButton(
                      onPressed: () {
                        // TODO: Implement share functionality
                        BPEasyLoading.showToast(LocaleKeys.asset_verification_success_share_coming_soon.tr);
                      },
                      icon: const Icon(Icons.share, color: Colors.white, size: 24),
                    ),
                  ],
                ),
              ),

              // Scrollable content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      // Certificate number
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MVText(
                            _getCertificateNumber(assetCertification),
                            fontSize: 14,
                            color: Colors.white70,
                          ),
                          const SizedBox(height: 16),

                          // Certificate title
                          MVText(
                            LocaleKeys.asset_verification_success_certificate_title.tr,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),

                          // 5-Star Member badge
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.star,
                                color: BPColor.darkGold, // Gold color
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              MVText(
                                LocaleKeys.asset_verification_success_five_star_member.tr,
                                fontSize: 14,
                                color: BPColor.darkGold,
                                fontWeight: FontWeight.w500,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),

                      // Certificate details table
                      _buildCertificateDetailsTable(userModel, userName, assetRangeText, assetCertification),

                      const SizedBox(height: 32),

                      // Verification text
                      Text(
                        LocaleKeys.asset_verification_success_verification_message.tr,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 32),

                      // Signature section
                      _buildSignatureSection(),

                      const SizedBox(height: 32),

                      // Medal/Seal
                      Container(
                        width: 80,
                        height: 80,
                        child: Image.asset(
                          'assets/images/common/medal.png',
                          fit: BoxFit.contain,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Footer text
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          LocaleKeys.asset_verification_success_footer_text.tr,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white60,
                            height: 1.3,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get certificate number from AssetCertificationModel
  String _getCertificateNumber(AssetCertificationModel? assetCertification) {
    if (assetCertification?.id != null && assetCertification!.id > 0) {
      return 'Certificate No. ${assetCertification.id.toString().padLeft(8, '0')}';
    }
    return LocaleKeys.asset_verification_success_default_certificate_number.tr;
  }

  /// Build certificate details table
  Widget _buildCertificateDetailsTable(BPUserModel? userModel, String userName, String assetRangeText, AssetCertificationModel? assetCertification) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF222324),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          _buildDetailRow(LocaleKeys.asset_verification_success_certificate_number.tr, _getCertificateNumber(assetCertification)),
          _buildDetailRow(LocaleKeys.asset_verification_success_asset_range.tr, assetRangeText),
          _buildDetailRow(LocaleKeys.asset_verification_success_issue_date.tr, _getFormattedDate(assetCertification?.issuedate)),
          _buildDetailRow(LocaleKeys.asset_verification_success_expiry_date.tr, _getFormattedDate(assetCertification?.expirydate)),
          const SizedBox(height: 24),
          _buildDetailRow(LocaleKeys.asset_verification_success_name.tr, userName.isNotEmpty ? userName : LocaleKeys.asset_verification_success_default_name.tr),
          _buildDetailRow(LocaleKeys.asset_verification_success_occupation.tr, _getOccupation(userModel)),
          _buildDetailRow(LocaleKeys.asset_verification_success_age_group.tr, _getAgeGroup(userModel?.birthday)),
          _buildDetailRow(LocaleKeys.asset_verification_success_nationality.tr, _getNationality(userModel?.nationalityCode)),
          _buildDetailRow(LocaleKeys.asset_verification_success_gender.tr, _getGender(userModel?.gender)),
        ],
      ),
    );
  }

  /// Get occupation from user model
  String _getOccupation(BPUserModel? userModel) {
    // First check if user has a title (job title)
    if (userModel?.title != null && userModel!.title!.isNotEmpty) {
      return userModel.title!;
    }

    // Check if user has university information
    if (userModel?.university?.name != null && userModel!.university!.name!.isNotEmpty) {
      return '${userModel.university!.name!} 출신';
    }

    // Check user type (talent vs business)
    if (userModel?.type != null) {
      switch (userModel!.type!) {
        case BPUserRole.business:
          return '사업가';
        case BPUserRole.talent:
          return '전문직';
        default:
          break;
      }
    }

    // Check graduate status
    if (userModel?.status != null) {
      switch (userModel!.status!) {
        case BPGraduateStatus.graduate:
          return '대학 졸업';
        case BPGraduateStatus.undergraduate:
          return '대학생';
        case BPGraduateStatus.none:
          return '기타';
        default:
          break;
      }
    }

    // Fallback to default
    return LocaleKeys.asset_verification_success_default_occupation.tr;
  }

  /// Format date for display
  String _getFormattedDate(DateTime? date) {
    if (date == null) {
      return LocaleKeys.asset_verification_success_default_issue_date.tr;
    }
    return '${date.year}.${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')}';
  }

  /// Get age group from birthday
  String _getAgeGroup(DateTime? birthday) {
    if (birthday == null) {
      return LocaleKeys.asset_verification_success_default_age_group.tr;
    }

    final age = DateTime.now().difference(birthday).inDays ~/ 365;
    if (age < 20) {
      return '10대';
    } else if (age < 30) {
      return '20대';
    } else if (age < 40) {
      return '30대';
    } else if (age < 50) {
      return '40대';
    } else if (age < 60) {
      return '50대';
    } else if (age < 70) {
      return '60대';
    } else {
      return '70대+';
    }
  }

  /// Get nationality from nationality code
  String _getNationality(String? nationalityCode) {
    if (nationalityCode == null || nationalityCode.isEmpty) {
      return LocaleKeys.asset_verification_success_default_nationality.tr;
    }

    switch (nationalityCode.toUpperCase()) {
      case 'KR':
        return '대한민국';
      case 'US':
        return '미국';
      case 'JP':
        return '일본';
      case 'CN':
        return '중국';
      case 'GB':
        return '영국';
      case 'DE':
        return '독일';
      case 'FR':
        return '프랑스';
      case 'CA':
        return '캐나다';
      case 'AU':
        return '호주';
      case 'SG':
        return '싱가포르';
      case 'HK':
        return '홍콩';
      case 'TW':
        return '대만';
      case 'IN':
        return '인도';
      case 'TH':
        return '태국';
      case 'VN':
        return '베트남';
      case 'PH':
        return '필리핀';
      case 'MY':
        return '말레이시아';
      case 'ID':
        return '인도네시아';
      case 'BR':
        return '브라질';
      case 'MX':
        return '멕시코';
      case 'RU':
        return '러시아';
      case 'IT':
        return '이탈리아';
      case 'ES':
        return '스페인';
      case 'NL':
        return '네덜란드';
      case 'CH':
        return '스위스';
      case 'SE':
        return '스웨덴';
      case 'NO':
        return '노르웨이';
      case 'DK':
        return '덴마크';
      case 'FI':
        return '핀란드';
      case 'BE':
        return '벨기에';
      case 'AT':
        return '오스트리아';
      case 'NZ':
        return '뉴질랜드';
      case 'IL':
        return '이스라엘';
      case 'AE':
        return '아랍에미리트';
      case 'SA':
        return '사우디아라비아';
      case 'ZA':
        return '남아프리카공화국';
      case 'EG':
        return '이집트';
      case 'TR':
        return '터키';
      case 'GR':
        return '그리스';
      case 'PL':
        return '폴란드';
      case 'CZ':
        return '체코';
      case 'HU':
        return '헝가리';
      case 'PT':
        return '포르투갈';
      case 'IE':
        return '아일랜드';
      case 'LU':
        return '룩셈부르크';
      case 'MC':
        return '모나코';
      case 'LI':
        return '리히텐슈타인';
      case 'MT':
        return '몰타';
      case 'CY':
        return '키프로스';
      case 'IS':
        return '아이슬란드';
      default:
        return nationalityCode.toUpperCase();
    }
  }

  /// Get gender text
  String _getGender(BPUserGender? gender) {
    if (gender == null) {
      return LocaleKeys.asset_verification_success_default_gender.tr;
    }

    switch (gender) {
      case BPUserGender.male:
        return '남성';
      case BPUserGender.female:
        return '여성';
      default:
        return LocaleKeys.asset_verification_success_default_gender.tr;
    }
  }

  /// Build a single detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MVText(
            label,
            fontSize: 14,
            color: Colors.white70,
          ),
          MVText(
            value,
            fontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ],
      ),
    );
  }

  /// Build signature section
  Widget _buildSignatureSection() {
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    final reviewUsers = userModel?.assetCertification?.reviewUsers;

    if (reviewUsers == null || reviewUsers.isEmpty) {
      // Fallback to default signatures if no review users
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSignature(LocaleKeys.asset_verification_success_tax_accountant.tr, '김세무'),
              _buildSignature(LocaleKeys.asset_verification_success_lawyer.tr, '최변호'),
              _buildSignature(LocaleKeys.asset_verification_success_accountant.tr, '박회계'),
            ],
          ),
        ],
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF222324),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: reviewUsers.map((reviewUser) {
              return _buildSignature(
                reviewUser.firstname ?? LocaleKeys.asset_verification_success_tax_accountant.tr,
                reviewUser.lastname ?? 'Unknown',
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// Build individual signature
  Widget _buildSignature(String title, String name) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.grey[600],
          ),
          child: const ClipOval(
            child: Icon(
              Icons.person,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MVText(
              title,
              fontSize: 12,
              color: Colors.white70,
            ),
            MVText(
              name,
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w400,
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Signature line
        // Container(
        //   width: 60,
        //   height: 20,
        //   child: CustomPaint(
        //     painter: SignaturePainter(),
        //   ),
        // ),
      ],
    );
  }

  /// Build user avatar with proper fallback logic
  Widget _buildUserAvatar(BPUserModel? userModel) {
    String? avatarUrl;

    // Priority order: real avatar > fake avatar > fake random avatar
    if (userModel?.fakeAvatar?.url != null && userModel!.fakeAvatar!.url!.isNotEmpty) {
      avatarUrl = userModel.fakeAvatar!.url!;
    } else if (userModel?.avatar?.url != null && userModel!.avatar!.url!.isNotEmpty) {
      avatarUrl = userModel.avatar!.url!;
    }

    if (avatarUrl != null) {
      return Image.network(
        avatarUrl,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.grey[300],
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            ),
          );
        },
      );
    }

    return _buildDefaultAvatar();
  }

  Widget _buildDefaultAvatar() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: Color(0xFF2C3E50),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 40,
      ),
    );
  }



  void _shareAssetCertificate() {
    // 直接打开QR码展示页面
    Get.to(() => const QRCodeDisplayPage());
  }

  void _showQRCode() {
    // 直接启动QR扫码功能
    _scanQRCode();
  }

  void _handleScannedData(String data) {
    // 解析扫码数据
    final parsedData = BPQRScanner.parseQRData(data);
    final dataType = parsedData['type'] as String;
    
    // 根据数据类型进行不同的处理
    switch (dataType) {
      case 'url':
        _handleUrlData(parsedData['url'] as String);
        break;
      case 'email':
        _handleEmailData(parsedData['email'] as String);
        break;
      case 'phone':
        _handlePhoneData(parsedData['phone'] as String);
        break;
      case 'json':
        _handleJsonData(parsedData['raw'] as String);
        break;
      default:
        _handleTextData(parsedData['text'] as String);
        break;
    }
  }

  void _handleUrlData(String url) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: const Text(
          'Website Found',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'A website URL was detected:',
              style: TextStyle(color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                url,
                style: const TextStyle(
                  color: Colors.blue,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // 这里可以添加打开URL的逻辑
              BPEasyLoading.showToast('Opening website...');
            },
            child: const Text('Open'),
          ),
        ],
      ),
    );
  }

  void _handleEmailData(String email) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: const Text(
          'Email Found',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'An email address was detected:',
              style: TextStyle(color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Text(
              email,
              style: const TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _handlePhoneData(String phone) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: const Text(
          'Phone Number Found',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'A phone number was detected:',
              style: TextStyle(color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Text(
              phone,
              style: const TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _handleJsonData(String jsonData) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: const Text(
          'Structured Data Found',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'JSON data was detected:',
              style: TextStyle(color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    jsonData,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _handleTextData(String text) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        title: const Text(
          'QR Code Content',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scanned content:',
              style: TextStyle(color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    text,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // 复制到剪贴板
              BPEasyLoading.showToast('Content copied to clipboard');
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  

  void _scanQRCode() {
    // 启动QR扫码功能
    BPQRScanner.startScan(
      onScanResult: (String scannedData) {
        _handleScannedQRData(scannedData);
      },
      onError: (String error) {
        BPEasyLoading.showError('QR Scanner failed: $error');
      },
    );
  }

  void _handleScannedQRData(String data) {
    // 首先检查是否是用户ID（数字）
    if (RegExp(r'^\d+$').hasMatch(data)) {
      // 直接请求用户信息，不需要确认
      _fetchUserInfoFromQR(data);
      return;
    }

    // 检查是否是JSON格式的数据
    try {
      final jsonData = json.decode(data);
      if (jsonData is Map<String, dynamic>) {
        _handleJsonQRData(jsonData);
        return;
      }
    } catch (e) {
      // 不是JSON格式，继续其他处理
    }

    // 检查是否是包含用户ID的URL格式
    if (_isUserProfileUrl(data)) {
      final userId = _extractUserIdFromUrl(data);
      if (userId != null) {
        _fetchUserInfoFromQR(userId);
        return;
      }
    }

    // 如果不是用户相关数据，则按原来的逻辑处理其他类型的QR码
    _handleScannedData(data);
  }

  /// 检查是否是用户资料URL
  bool _isUserProfileUrl(String data) {
    // 检查常见的用户资料URL模式
    final userUrlPatterns = [
      RegExp(r'/user/(\d+)'),
      RegExp(r'/profile/(\d+)'),
      RegExp(r'user_id=(\d+)'),
      RegExp(r'userId=(\d+)'),
      RegExp(r'id=(\d+)'),
    ];

    return userUrlPatterns.any((pattern) => pattern.hasMatch(data));
  }

  /// 从URL中提取用户ID
  String? _extractUserIdFromUrl(String data) {
    final userUrlPatterns = [
      RegExp(r'/user/(\d+)'),
      RegExp(r'/profile/(\d+)'),
      RegExp(r'user_id=(\d+)'),
      RegExp(r'userId=(\d+)'),
      RegExp(r'id=(\d+)'),
    ];

    for (final pattern in userUrlPatterns) {
      final match = pattern.firstMatch(data);
      if (match != null && match.groupCount > 0) {
        return match.group(1);
      }
    }

    return null;
  }

  void _handleJsonQRData(Map<String, dynamic> jsonData) {
    // 处理JSON格式的QR码数据
    if (jsonData.containsKey('userId') || jsonData.containsKey('user_id')) {
      final userId = jsonData['userId']?.toString() ?? jsonData['user_id']?.toString();
      if (userId != null && userId.isNotEmpty) {
        // 直接请求用户信息
        _fetchUserInfoFromQR(userId);
        return;
      }
    }

    // 如果JSON中包含完整的用户信息，直接显示证书
    if (_isCompleteUserData(jsonData)) {
      _showCertificateDialogFromQR(jsonData);
      return;
    }

    // 检查是否包含用户相关的其他标识
    if (_containsUserIdentifier(jsonData)) {
      final userId = _extractUserIdFromJson(jsonData);
      if (userId != null) {
        _fetchUserInfoFromQR(userId);
        return;
      }
    }

    // 其他JSON数据的处理
    _handleJsonData(json.encode(jsonData));
  }

  /// 检查是否是完整的用户数据
  bool _isCompleteUserData(Map<String, dynamic> jsonData) {
    // 检查是否包含足够的用户信息来显示证书
    final hasId = jsonData.containsKey('id');
    final hasName = jsonData.containsKey('fullname') || jsonData.containsKey('fakeName') || jsonData.containsKey('name');
    final hasUserInfo = jsonData.containsKey('avatar') || jsonData.containsKey('fakeAvatar') || jsonData.containsKey('gender') || jsonData.containsKey('birthday');

    return hasId && hasName && hasUserInfo;
  }

  /// 检查是否包含用户标识符
  bool _containsUserIdentifier(Map<String, dynamic> jsonData) {
    final userKeys = ['id', 'userId', 'user_id', 'uid', 'memberId', 'member_id', 'profileId', 'profile_id', 'accountId', 'account_id'];

    return userKeys.any((key) => jsonData.containsKey(key) && jsonData[key] != null && jsonData[key].toString().isNotEmpty);
  }

  /// 从JSON中提取用户ID
  String? _extractUserIdFromJson(Map<String, dynamic> jsonData) {
    final userKeys = ['id', 'userId', 'user_id', 'uid', 'memberId', 'member_id', 'profileId', 'profile_id', 'accountId', 'account_id'];

    for (final key in userKeys) {
      if (jsonData.containsKey(key) && jsonData[key] != null) {
        final value = jsonData[key].toString();
        if (value.isNotEmpty && RegExp(r'^\d+$').hasMatch(value)) {
          return value;
        }
      }
    }

    return null;
  }

  void _fetchUserInfoFromQR(String userId) async {
    try {
      // 显示加载提示
      BPEasyLoading.show();

      final response = await BPRestClient().scanUser(userId: userId);

      BPEasyLoading.dismiss();

      if (response != null) {
        // 检查响应数据结构
        Map<String, dynamic> userData;

        if (response is Map<String, dynamic>) {
          if (response.containsKey('data') && response['data'] != null) {
            userData = response['data'] as Map<String, dynamic>;
          } else {
            userData = response;
          }
        } else {
          throw Exception('Invalid response format');
        }

        // 验证用户数据的完整性
        if (userData.isEmpty) {
          throw Exception('User data is empty');
        }

        // 直接显示证书对话框，不显示成功提示
        _showCertificateDialogFromQR(userData);
      } else {
        throw Exception('No response received from server');
      }
    } catch (e) {
      BPEasyLoading.dismiss();

      // 根据错误类型显示不同的错误信息
      String errorMessage;
      if (e.toString().contains('404')) {
        errorMessage = 'User not found. Please check the QR code and try again.';
      } else if (e.toString().contains('403')) {
        errorMessage = 'Access denied. You may not have permission to view this user.';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'Request timeout. Please try again.';
      } else {
        errorMessage = 'Failed to load user information: ${e.toString()}';
      }

      BPEasyLoading.showError(errorMessage);

      // 记录错误日志（如果需要）
      print('QR Scan Error: $e');
    }
  }

  void _showCertificateDialogFromQR(Map<String, dynamic> userData) {
    try {
      // 提取用户信息，支持多种数据格式
      final userId = _extractUserId(userData);
      final userName = _extractUserName(userData);
      final avatarUrl = _extractAvatarUrl(userData);
      final financialStatus = _extractFinancialStatus(userData);
      final assetResult = _extractAssetResult(userData);
      final birthday = _extractBirthday(userData);
      final gender = _extractGender(userData);
      final nationality = _extractNationality(userData);

      // 创建资产认证信息
      final tempAssetCertification = AssetCertificationModel(
        id: userId,
        issuedate: DateTime.now().subtract(const Duration(days: 30)),
        expirydate: DateTime.now().add(const Duration(days: 335)),
        assetResult: assetResult,
      );

      // 创建临时用户模型
      final tempUserModel = BPUserModel(
        id: userId,
        fullname: userName,
        fakeName: userName,
        avatar: avatarUrl != null ? BPURLModel(id: 0, url: avatarUrl) : null,
        fakeAvatar: avatarUrl != null ? BPURLModel(id: 0, url: avatarUrl) : null,
        financialStatus: financialStatus,
        assetCertification: tempAssetCertification,
        birthday: birthday,
        gender: gender,
        nationalityCode: nationality,
      );

      // 获取资产范围文本
      final assetRangeText = AssetVerificationUtils.getAssetRangeText(assetResult);

      // 显示证书对话框
      _showCertificateDialog(Get.context!, tempUserModel, userName, assetRangeText, tempAssetCertification);
    } catch (e) {
      BPEasyLoading.showError('Failed to display certificate: ${e.toString()}');
      print('Certificate Display Error: $e');
    }
  }

  // 辅助方法：提取用户ID
  int _extractUserId(Map<String, dynamic> userData) {
    return (userData['id'] as int?) ?? (int.tryParse(userData['userId']?.toString() ?? '')) ?? (int.tryParse(userData['user_id']?.toString() ?? '')) ?? 0;
  }

  // 辅助方法：提取用户名
  String _extractUserName(Map<String, dynamic> userData) {
    return (userData['fakeName'] as String?) ?? (userData['fullname'] as String?) ?? (userData['name'] as String?) ?? (userData['username'] as String?) ?? 'Unknown User';
  }

  // 辅助方法：提取头像URL
  String? _extractAvatarUrl(Map<String, dynamic> userData) {
    // 检查多种可能的头像字段
    if (userData['fakeAvatar'] != null) {
      if (userData['fakeAvatar'] is Map) {
        return userData['fakeAvatar']['url'] as String?;
      } else if (userData['fakeAvatar'] is String) {
        return userData['fakeAvatar'] as String?;
      }
    }

    if (userData['avatar'] != null) {
      if (userData['avatar'] is Map) {
        return userData['avatar']['url'] as String?;
      } else if (userData['avatar'] is String) {
        return userData['avatar'] as String?;
      }
    }

    return userData['fakeRandomAvatar'] as String?;
  }

  // 辅助方法：提取财务状态
  BPFinancialStatus _extractFinancialStatus(Map<String, dynamic> userData) {
    final statusStr = userData['financialStatus'] as String?;
    if (statusStr != null) {
      switch (statusStr) {
        case 'moreThan100K':
          return BPFinancialStatus.moreThan100K;
        case 'moreThan500K':
          return BPFinancialStatus.moreThan500K;
        case 'moreThan1M':
          return BPFinancialStatus.moreThan1M;
        case 'moreThan5M':
          return BPFinancialStatus.moreThan5M;
        case 'moreThan10M':
          return BPFinancialStatus.moreThan10M;
        case 'moreThan50M':
          return BPFinancialStatus.moreThan50M;
        case 'moreThan100M':
          return BPFinancialStatus.moreThan100M;
      }
    }
    return BPFinancialStatus.moreThan100K;
  }

  // 辅助方法：提取资产结果
  AssetResult _extractAssetResult(Map<String, dynamic> userData) {
    // 首先检查asset_certification中的assetResult
    if (userData['asset_certification'] != null) {
      final assetCert = userData['asset_certification'] as Map<String, dynamic>;
      final resultStr = assetCert['assetResult'] as String?;
      if (resultStr != null) {
        switch (resultStr) {
          case 'moreThan100K':
            return AssetResult.moreThan100K;
          case 'moreThan500K':
            return AssetResult.moreThan500K;
          case 'moreThan1M':
            return AssetResult.moreThan1M;
          case 'moreThan5M':
            return AssetResult.moreThan5M;
          case 'moreThan10M':
            return AssetResult.moreThan10M;
          case 'moreThan50M':
            return AssetResult.moreThan50M;
          case 'moreThan100M':
            return AssetResult.moreThan100M;
        }
      }
    }

    // 如果没有找到，使用financialStatus作为备选
    final financialStatus = _extractFinancialStatus(userData);
    switch (financialStatus) {
      case BPFinancialStatus.moreThan100K:
        return AssetResult.moreThan100K;
      case BPFinancialStatus.moreThan500K:
        return AssetResult.moreThan500K;
      case BPFinancialStatus.moreThan1M:
        return AssetResult.moreThan1M;
      case BPFinancialStatus.moreThan5M:
        return AssetResult.moreThan5M;
      case BPFinancialStatus.moreThan10M:
        return AssetResult.moreThan10M;
      case BPFinancialStatus.moreThan50M:
        return AssetResult.moreThan50M;
      case BPFinancialStatus.moreThan100M:
        return AssetResult.moreThan100M;
      default:
        return AssetResult.moreThan100K;
    }
  }

  // 辅助方法：提取生日
  DateTime? _extractBirthday(Map<String, dynamic> userData) {
    final birthdayStr = userData['birthday'] as String?;
    if (birthdayStr != null) {
      try {
        return DateTime.parse(birthdayStr);
      } catch (e) {
        print('Failed to parse birthday: $e');
      }
    }
    return null;
  }

  // 辅助方法：提取性别
  BPUserGender? _extractGender(Map<String, dynamic> userData) {
    final genderStr = userData['gender'] as String?;
    if (genderStr != null) {
      switch (genderStr.toUpperCase()) {
        case 'M':
          return BPUserGender.male;
        case 'F':
          return BPUserGender.female;
        case 'MALE':
          return BPUserGender.male;
        case 'FEMALE':
          return BPUserGender.female;
      }
    }
    return null;
  }

  // 辅助方法：提取国籍
  String? _extractNationality(Map<String, dynamic> userData) {
    return userData['nationalityCode'] as String? ?? userData['nationality'] as String? ?? userData['countryCode'] as String?;
  }
}

/// Custom tab indicator with exact 24x3 size
class _CustomTabIndicator extends Decoration {
  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomTabIndicatorPainter(onChanged);
  }
}

class _CustomTabIndicatorPainter extends BoxPainter {
  _CustomTabIndicatorPainter(VoidCallback? onChanged) : super(onChanged);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Size size = configuration.size!;

    // Calculate the center position for the 24x3 indicator
    final double indicatorWidth = 24.0;
    final double indicatorHeight = 3.0;

    final double left = offset.dx + (size.width - indicatorWidth) / 2;
    final double top = offset.dy + size.height - indicatorHeight;

    final Rect rect = Rect.fromLTWH(left, top, indicatorWidth, indicatorHeight);

    final Paint paint = Paint()
      ..color = BPColor.gold
      ..style = PaintingStyle.fill;

    canvas.drawRect(rect, paint);
  }
}

/// Custom painter for signature
class SignaturePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF2C3E50)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.3,
      size.width * 0.5,
      size.height * 0.6,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.8,
      size.width * 0.9,
      size.height * 0.4,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 