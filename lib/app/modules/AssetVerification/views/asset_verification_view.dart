import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/mv_text.dart';
import '../../../shared/components/BPTextButton.dart';
import '../../../../generated/locales.g.dart';
import '../controllers/asset_verification_controller.dart';
import '../../../data/AssetVerificationModel.dart';
import 'asset_types_display_page.dart';
import 'id_upload_page.dart';
import 'asset_selection_page.dart';
import 'asset_operation_page.dart';
import 'asset_confirmation_page.dart';

class AssetVerificationView extends GetView<AssetVerificationController> {
  const AssetVerificationView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: controller.onBackPressed,
          icon: const Icon(Icons.arrow_back),
        ),
        title: Obx(() {
          switch (controller.currentStep.value) {
            case AssetVerificationStep.assetList:
              return MVText(
                LocaleKeys.asset_verification_title.tr,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              );
            case AssetVerificationStep.uploadId:
              return MVText(
                LocaleKeys.asset_verification_upload_id_title.tr,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              );
            case AssetVerificationStep.selectAssets:
              return MVText(
                LocaleKeys.asset_verification_select_assets_title.tr,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              );
            case AssetVerificationStep.confirmation:
              return MVText(
                LocaleKeys.asset_verification_confirmation_title.tr,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              );
            case AssetVerificationStep.assetOperation:
              return Obx(() {
                final assetType = controller.currentAssetType.value;
                if (assetType == null) {
                  return MVText(
                    LocaleKeys.asset_verification_add_asset.tr,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  );
                }
                return MVText(
                  _getAddAssetTitle(assetType),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                );
              });
          }
        }),
        centerTitle: true,
        actions: [
          // 在assetOperation页面显示save按钮
          Obx(() {
            if (controller.currentStep.value == AssetVerificationStep.assetOperation) {
              return TextButton(
                onPressed: controller.canSaveCurrentAssets ? controller.saveCurrentAssets : null,
                child: MVText(
                  LocaleKeys.common_save.tr,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: controller.canSaveCurrentAssets ? BPColor.gold : BPColor.greyText,
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(() {
              switch (controller.currentStep.value) {
                case AssetVerificationStep.assetList:
                  return const AssetTypesDisplayPage();
                case AssetVerificationStep.uploadId:
                  return const IdUploadPage();
                case AssetVerificationStep.selectAssets:
                  return const AssetSelectionPage();
                case AssetVerificationStep.confirmation:
                  return const AssetConfirmationPage();
                case AssetVerificationStep.assetOperation:
                  return const AssetOperationPage();
              }
            }),
          ),
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Obx(() {
      // 在assetOperation页面时不显示底部按钮
      if (controller.currentStep.value == AssetVerificationStep.assetOperation) {
        return const SizedBox.shrink();
      }
      
      // 确认页面显示特殊的底部布局
      if (controller.currentStep.value == AssetVerificationStep.confirmation) {
        return _buildConfirmationBottomSection();
      }
      
      return Container(
        padding: const EdgeInsets.all(20),
        child: Obx(() {
          bool isEnabled;
          switch (controller.currentStep.value) {
            case AssetVerificationStep.assetList:
              isEnabled = true; // 第一步始终可以继续
              break;
            case AssetVerificationStep.uploadId:
              isEnabled = controller.canProceedFromUpload;
              break;
            case AssetVerificationStep.selectAssets:
              isEnabled = controller.canProceedFromAssets;
              break;
            case AssetVerificationStep.confirmation:
              isEnabled = controller.hasAgreed.value;
              break;
            case AssetVerificationStep.assetOperation:
              isEnabled = true; // 资产操作页面总是可以返回
              break;
          }

          return SizedBox(
            width: double.infinity,
            height: 50,
            child: BPTextButton(
              title: LocaleKeys.asset_verification_next.tr,
              onPressed: isEnabled ? controller.onNextPressed : null,
              backgroundColor: isEnabled ? BPColor.gold : BPColor.grey66,
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              borderRadius: 25,
              minimumSize: const Size(double.infinity, 50),
              mainAxisAlignment: MainAxisAlignment.center,
            ),
          );
        }),
      );
    });
  }

  Widget _buildConfirmationBottomSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Agreement checkbox
          Obx(() {
            return GestureDetector(
              onTap: controller.toggleAgreement,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Checkbox
                  Container(
                    width: 14,
                    height: 14,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: controller.hasAgreed.value
                          ? null
                          : Border.all(
                              color: BPColor.whiteText,
                              width: 1,
                            ),
                      color: controller.hasAgreed.value ? BPColor.brand : Colors.transparent,
                    ),
                    child: controller.hasAgreed.value
                        ? const Center(
                            child: Icon(
                              Icons.check,
                              color: BPColor.whiteText,
                              size: 12,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 8),
                  // Agreement text
                  MVText(
                    LocaleKeys.asset_verification_agreement_text.tr,
                    fontSize: 14,
                    color: BPColor.greyText,
                  ),
                ],
              ),
            );
          }),

          const SizedBox(height: 20),

          // Submit button
          Obx(() {
            final isEnabled = controller.hasAgreed.value;
            return SizedBox(
              width: double.infinity,
              height: 50,
              child: BPTextButton(
                title: LocaleKeys.asset_verification_submit.tr,
                onPressed: isEnabled ? controller.onNextPressed : null,
                backgroundColor: isEnabled ? BPColor.brand : BPColor.brand.withOpacity(0.5),
                color: isEnabled ? BPColor.whiteText : BPColor.greyText,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                borderRadius: 25,
                minimumSize: const Size(double.infinity, 50),
                mainAxisAlignment: MainAxisAlignment.center,
              ),
            );
          }),
        ],
      ),
    );
  }

  String _getAddAssetTitle(AssetType assetType) {
    switch (assetType) {
      case AssetType.realEstate:
        return LocaleKeys.asset_verification_add_real_estate.tr;
      case AssetType.financialAssets:
        return LocaleKeys.asset_verification_add_financial_asset.tr;
      case AssetType.cryptoCurrency:
        return LocaleKeys.asset_verification_add_crypto_asset.tr;
      case AssetType.others:
        return LocaleKeys.asset_verification_add_other_assets.tr;
    }
  }
}
