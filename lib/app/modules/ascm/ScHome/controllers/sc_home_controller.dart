import 'package:carousel_slider/carousel_slider.dart';
import 'package:get/get.dart';
import 'package:starchex/app/shared/networking/BPRestAPI.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import '../../../../data/scdata/mv_star_event_model.dart';
import '../../../../data/scdata/mv_star_model.dart';
import '../../../../data/BPCommonModels.dart';
import '../../../../data/BPCommunityModels.dart';
import '../../../../data/BPNotificationModels.dart';
import '../../../../routes/app_pages.dart';
import '../../Components/page_media/page_media_controller.dart';
import '../../EventJoinComfirm/views/event_join_comfirm_view.dart';
import '../../../Notification/controllers/notification_controller.dart';
import 'package:flutter/material.dart';

// 统一的photo数据结构，包含tab信息
class PhotoWithTabInfo {
  final dynamic photo;
  final int tabIndex;
  final StarModel? star;
  final String tabName;
  final int photoIndexInTab;

  PhotoWithTabInfo({
    required this.photo,
    required this.tabIndex,
    required this.star,
    required this.tabName,
    required this.photoIndexInTab,
  });
}

class ScHomeController extends GetxController with PageMediaControllerMixin {
  final _loadTask = MVTaskToken();
  bool get isLoadingList => _loadTask.isLoading;

  final carouselController = CarouselSliderController();

  // 为每个标签页的photos创建独立的PageController
  final Map<int, PageController> _photosPageControllers = {};

  PageController getPhotosPageController(int tabIndex) {
    try {
      if (!_photosPageControllers.containsKey(tabIndex)) {
        _photosPageControllers[tabIndex] = PageController();
      }
      final controller = _photosPageControllers[tabIndex];
      if (controller == null) {
        _photosPageControllers[tabIndex] = PageController();
        return _photosPageControllers[tabIndex]!;
      }
      return controller;
    } catch (e) {
      // 如果出现任何错误，创建并返回一个新的PageController
      final newController = PageController();
      try {
        _photosPageControllers[tabIndex] = newController;
      } catch (e) {
        // 如果连设置都失败，直接返回新controller
      }
      return newController;
    }
  }

  int selectedTabIndex = 0;

  // 跟踪每个标签页的当前photo索引
  final Map<int, int> _currentPhotoIndex = {0: 0, 1: 0, 2: 0};
  
  // 连续滑动的统一数据结构
  List<PhotoWithTabInfo> _allPhotos = [];
  int _currentUnifiedIndex = 0;
  final PageController unifiedPageController = PageController();

  int getCurrentPhotoIndex(int tabIndex) {
    try {
      return _currentPhotoIndex[tabIndex] ?? 0;
    } catch (e) {
      return 0;
    }
  }

  void setCurrentPhotoIndex(int tabIndex, int photoIndex) {
    try {
      _currentPhotoIndex[tabIndex] = photoIndex;
      update();
    } catch (e) {
      // 忽略设置错误，避免崩溃
    }
  }

  // 获取所有photos的统一列表
  List<PhotoWithTabInfo> get allPhotos => _allPhotos;
  
  // 获取当前统一索引
  int get currentUnifiedIndex => _currentUnifiedIndex;
  
  // 构建统一的photos列表
  void _buildUnifiedPhotosList() {
    _allPhotos.clear();
    
    // This Week photos
    final thisWeekPhotos = star?.event?.photos ?? [];
    if (thisWeekPhotos.isNotEmpty) {
      for (int i = 0; i < thisWeekPhotos.length; i++) {
        _allPhotos.add(PhotoWithTabInfo(
          photo: thisWeekPhotos[i],
          tabIndex: 0,
          star: star,
          tabName: 'This Week',
          photoIndexInTab: i,
        ));
      }
    } else {
      // This Week没有数据时添加占位符
      _allPhotos.add(PhotoWithTabInfo(
        photo: null,
        tabIndex: 0,
        star: star,
        tabName: 'This Week',
        photoIndexInTab: 0,
      ));
    }
    
    // Last Week photos
    final lastWeekPhotos = lastStar?.event?.photos ?? [];
    if (lastWeekPhotos.isNotEmpty) {
      for (int i = 0; i < lastWeekPhotos.length; i++) {
        _allPhotos.add(PhotoWithTabInfo(
          photo: lastWeekPhotos[i],
          tabIndex: 1,
          star: lastStar,
          tabName: 'Last Week',
          photoIndexInTab: i,
        ));
      }
    } else {
      // Last Week没有数据时添加占位符
      _allPhotos.add(PhotoWithTabInfo(
        photo: null,
        tabIndex: 1,
        star: lastStar,
        tabName: 'Last Week',
        photoIndexInTab: 0,
      ));
    }
    
    // Upcoming photos
    final upcomingPhotos = nextStar?.event?.photos ?? [];
    if (upcomingPhotos.isNotEmpty) {
      for (int i = 0; i < upcomingPhotos.length; i++) {
        _allPhotos.add(PhotoWithTabInfo(
          photo: upcomingPhotos[i],
          tabIndex: 2,
          star: nextStar,
          tabName: 'Upcoming',
          photoIndexInTab: i,
        ));
      }
    } else {
      // Upcoming没有数据时添加占位符
      _allPhotos.add(PhotoWithTabInfo(
        photo: null,
        tabIndex: 2,
        star: nextStar,
        tabName: 'Upcoming',
        photoIndexInTab: 0,
      ));
    }
  }
  
  // 根据统一索引计算应该显示的tab
  int _calculateTabIndexFromUnifiedIndex(int unifiedIndex) {
    if (unifiedIndex < 0 || unifiedIndex >= _allPhotos.length) return 0;
    return _allPhotos[unifiedIndex].tabIndex;
  }
  
  // 更新统一索引并同步tab状态
  void updateUnifiedIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _allPhotos.length) {
      _currentUnifiedIndex = newIndex;
      final newTabIndex = _calculateTabIndexFromUnifiedIndex(newIndex);
      
      if (selectedTabIndex != newTabIndex) {
        selectedTabIndex = newTabIndex;
      }
      
      update();
    }
  }
  
  // 获取指定tab的第一个photo的统一索引
  int _getTabStartIndex(int tabIndex) {
    for (int i = 0; i < _allPhotos.length; i++) {
      if (_allPhotos[i].tabIndex == tabIndex) {
        return i;
      }
    }
    return 0;
  }
  
  // 获取当前tab的photos信息
  List<PhotoWithTabInfo> getCurrentTabPhotos() {
    return _allPhotos.where((photo) => photo.tabIndex == selectedTabIndex).toList();
  }
  
  // 获取当前photo在当前tab中的索引
  int getCurrentPhotoIndexInTab() {
    final currentTabPhotos = getCurrentTabPhotos();
    if (currentTabPhotos.isEmpty) return 0;
    
    final currentPhoto = _allPhotos.isNotEmpty && _currentUnifiedIndex < _allPhotos.length 
        ? _allPhotos[_currentUnifiedIndex] 
        : null;
    
    if (currentPhoto == null) return 0;
    
    // 找到当前photo在当前tab中的相对位置
    for (int i = 0; i < currentTabPhotos.length; i++) {
      if (currentTabPhotos[i] == currentPhoto) {
        return i;
      }
    }
    
    return 0;
  }

  StarModel? get star => home?.current;
  StarModel? get nextStar => home?.next;
  StarModel? get lastStar => home?.last;
  List<StarEventModel>? get normalEvents {
    // 当数据少于3个时，添加模拟数据以展示横向滚动效果
    final realEvents = home?.normalEvents ?? [];
    // if (realEvents.length < 3) {
    //   final mockEvents = _generateMockEvents(3 - realEvents.length);
    //   return [...realEvents, ...mockEvents];
    // }
    return realEvents;
  }

  List<StarEventModel> _generateMockEvents(int count) {
    final mockEvents = <StarEventModel>[];
    for (int i = 0; i < count; i++) {
      final mockEvent = StarEventModel(
        id: -i - 1, // 负数ID表示模拟数据
        title: 'Mock Event ${i + 1}',
        address: 'Seoul, Korea',
        startDate: DateTime.now().add(Duration(days: i + 1)),
        maxMembers: 100,
        photos: [
          BPURLModel(
            id: -i - 1,
            url: 'https://picsum.photos/300/200?random=${i + 1}',
            mime: 'image/jpeg',
          ),
        ],
        extra: EventExtra(
          membersCount: (i + 1) * 15,
          status: EventJoinStatus.none,
        ),
      );
      mockEvents.add(mockEvent);
    }
    return mockEvents;
  }
  HomeModel? home;

  // Community posts for Best from Talk section
  List<BPCommunityPost>? _communityPosts;
  List<BPCommunityPost>? get communityPosts => _communityPosts;
  final _communityLoadTask = MVTaskToken();
  bool get isLoadingCommunityPosts => _communityLoadTask.isLoading;

  RefreshController refreshController = RefreshController(initialRefresh: false);
  String? get lastWeekEventTitle {
    return lastStar?.event?.title;
  }

  @override
  void onReady() {
    super.onReady();
    // Initialize NotificationController if not already available
    _initializeNotificationController();
    onRefresh();
  }

  void _initializeNotificationController() {
    try {
      // Try to get existing controller
      Get.find<NotificationController>();
    } catch (e) {
      // If not found, create it
      Get.put<NotificationController>(NotificationController());
    }
  }

  @override
  void onClose() {
    _loadTask.cancel();
    _communityLoadTask.cancel();
    unifiedPageController.dispose();

    // 清理所有photos的PageController
    for (final controller in _photosPageControllers.values) {
      controller.dispose();
    }
    _photosPageControllers.clear();
    
    super.onClose();
  }

  Future<void> _load({bool isReload = false}) async {
    if (_loadTask.isLoading) return;
    final idf = _loadTask.create();
    try {
      update();
      home = await BPRestClient().currentStar(cancelToken: _loadTask.task);

      _buildUnifiedPhotosList();
      _updateMediaForTab();
      autoPlayOnVisibility = true;
      
      // Load community posts
      await _loadCommunityPosts();
    } catch (e) {
      catchedError(e);
    } finally {
      if (_loadTask.completed(idf: idf)) {}
      update();
      refreshController.refreshCompleted();
    }
  }

  Future<void> _loadCommunityPosts() async {
    if (_communityLoadTask.isLoading) return;
    final idf = _communityLoadTask.create();
    try {
      print('Loading community posts...');
      // Use existing API with boardId = 0 to get popular posts
      // According to the requirement, boardId = 0 returns posts sorted by likeCount descending
      // and shows only last 7 days posts, limited to 3 items
      _communityPosts = await BPRestClient().postList(
        boardId: 0,
        ids: '', // Empty string for initial load
        cancelToken: _communityLoadTask.task,
      );
      print('Community posts loaded: ${_communityPosts?.length ?? 0} posts');
    } catch (e) {
      print('Error loading community posts: $e');
      catchedError(e);
    } finally {
      if (_communityLoadTask.completed(idf: idf)) {}
      update(); // Make sure to update the UI
    }
  }

  Future<void> joinEvent(StarEventModel event) async {
    if (event.extra?.status == EventJoinStatus.none) {
      EventJoinComfirmView.show(
        event: event,
        doneCallback: (event) {
          update();
        },
      );
    }
  }

  void onRefresh() {
    _load(isReload: true);
  }

  void toDetail() {
    if (star?.event?.id != null) {
      // _toGuestEvent(star!.event!);
      toViewEvent(star!.event!);
    }
  }

  void onViewAllComments() {
    if (star?.event?.id != null) {
      Get.toNamed(
        Routes.EVENT_ALL_COMMENTS,
        parameters: {'eventId': star?.event?.id.toString() ?? ''},
      );
    }
  }

  void onViewAllEvents() {
    Get.toNamed(Routes.EVENTS);
  }

  void _toGuestEvent(StarEventModel event) {
    if (event.type == StarEventType.specialGuest) {
      Get.toNamed(
        Routes.SPECIAL_GUEST_EVENT_DETAIL,
        arguments: {'event': event},
        parameters: {
          'eventId': event.id.toString(),
        },
      );
    }
  }

  void toViewEvent(StarEventModel event) {
    if (event.type == StarEventType.normal) {
      Get.toNamed(
        Routes.NORMAL_EVENT_DETAIL,
        arguments: {'event': event},
        parameters: {
          'eventId': event.id.toString(),
        },
      );
    } else {
      _toGuestEvent(event);
    }
  }

  void onViewLastStar() {
    if (lastStar != null) {
      Get.toNamed(
        Routes.PAST_SPECIAL_GUEST,
      );
    }
  }

  void toNotification() {
    Get.toNamed(Routes.NOTIFICATION);
  }

  void toCommunityPostDetail(BPCommunityPost post) {
    Get.toNamed(
      Routes.COMMUNITY_DETAIL,
      arguments: {'post': post},
      parameters: {
        'postId': post.id.toString(),
      },
    );
  }

  void changeTab(int index) {
    if (selectedTabIndex != index) {
      selectedTabIndex = index;

      // 重置当前标签页的photo索引
      _currentPhotoIndex[index] = 0;

      // 跳转到对应tab的第一张photo
      final targetIndex = _getTabStartIndex(index);
      if (unifiedPageController.hasClients && targetIndex != _currentUnifiedIndex) {
        unifiedPageController.animateToPage(
          targetIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }

      _updateMediaForTab();
      update();
    }
  }
  
  // 手动切换到指定tab（用于TabBar点击）
  void switchToTab(int tabIndex) {
    final targetIndex = _getTabStartIndex(tabIndex);
    if (unifiedPageController.hasClients) {
      unifiedPageController.animateToPage(
        targetIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _updateMediaForTab() {
    switch (selectedTabIndex) {
      case 0: // This Week
        media = home?.current?.event?.photos ?? [];
        break;
      case 1: // Last Week
        media = home?.last?.event?.photos ?? [];
        break;
      case 2: // Upcoming
        media = home?.next?.event?.photos ?? [];
        break;
    }
    if (media.isNotEmpty) {
      onPageChange(0);
    }
  }

  // Get the latest notification from NotificationController
  BPNotification? get latestNotification {
    try {
      final notificationController = Get.find<NotificationController>();
      if (notificationController.notifications.isNotEmpty) {
        return notificationController.notifications.first;
      }
    } catch (e) {
      // NotificationController not found or not initialized
      // Try to initialize it
      _initializeNotificationController();
    }
    return null;
  }

  // Check if there are any notifications
  bool get hasNotifications {
    try {
      final notificationController = Get.find<NotificationController>();
      return notificationController.notifications.isNotEmpty;
    } catch (e) {
      // NotificationController not found or not initialized
      // Try to initialize it
      _initializeNotificationController();
      return false;
    }
  }
}
