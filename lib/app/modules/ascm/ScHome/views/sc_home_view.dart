import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:starchex/app/data/scdata/mv_star_event_model.dart';
import 'package:starchex/app/data/BPCommunityModels.dart';
import 'package:starchex/app/shared/components/BPImageUtil.dart';
import 'package:starchex/app/shared/components/mv_text.dart';
import 'package:starchex/generated/locales.g.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../../routes/app_pages.dart';
import '../../Components/star_level_widget.dart';
import '../../../CommunityV2/CommunityFeed/Widgets/community_post_card.dart';
import '../controllers/sc_home_controller.dart';

class ScHomeView extends StatefulWidget {
  const ScHomeView({super.key});

  @override
  State<ScHomeView> createState() => _ScHomeViewState();
}

class _ScHomeViewState extends State<ScHomeView> with TickerProviderStateMixin {
  late TabController _tabController;
  ScHomeController? _controller;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: 0,
    );

    // 监听TabController变化 - 用于统一PageView
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging && _controller != null) {
        // 不再直接调用changeTab，让switchToTab处理统一PageView的跳转
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ScHomeController>(
      init: ScHomeController(),
      builder: (ctrl) {
        // 初始化controller引用（仅在第一次）
        if (_controller == null) {
          _controller = ctrl;
          // 同步初始状态
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_tabController.index != ctrl.selectedTabIndex) {
              _tabController.animateTo(ctrl.selectedTabIndex);
            }
          });
        } else {
          _controller = ctrl;
          // 同步TabController和controller状态
          if (_tabController.index != ctrl.selectedTabIndex) {
            _tabController.animateTo(ctrl.selectedTabIndex);
          }
        }
        return Scaffold(
          backgroundColor: BPColor.background,
          body: SmartRefresher(
            header: const WaterDropHeader(
              complete: Text(''),
            ),
            controller: ctrl.refreshController,
            onRefresh: ctrl.onRefresh,
            enablePullDown: true,
            child: SafeArea(
              bottom: false,
              child: CustomScrollView(
                slivers: [
                  // 顶部标题和标签页
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          // Starchex 标题
                          // const MVText(
                          //   'Starchex',
                          //   fontSize: 32,
                          //   fontWeight: FontWeight.w900,
                          //   color: BPColor.brand,
                          // ),
                          BPImageUtil.scSvgImage('starchex_gradient_logo.svg'),
                          const SizedBox(height: 16),
                          // 标签页 - 使用真正的TabBar，处理点击事件
                          TabBar(
                            controller: _tabController,
                            indicator: const _CustomTabIndicator(
                              color: BPColor.brand,
                              width: 24,
                              height: 3,
                            ),
                            dividerColor: Colors.transparent, // 取消底部白色横线
                            labelColor: Colors.white,
                            unselectedLabelColor: BPColor.grayText,
                            labelStyle: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            unselectedLabelStyle: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                            ),
                            onTap: (index) {
                              // 手动点击TabBar时，切换到对应tab的第一张photo
                              _controller?.switchToTab(index);
                            },
                            tabs: [
                              Tab(text: LocaleKeys.sc_str_home_this_week.tr),
                              Tab(text: LocaleKeys.sc_str_home_last_week.tr),
                              Tab(text: LocaleKeys.sc_str_home_upcoming.tr),
                            ],
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                  // 主内容区域 - 使用统一的PageView实现连续滑动
                  SliverToBoxAdapter(
                    child: _buildUnifiedPageViewContent(ctrl),
                  ),
                  // 通知区域
                  SliverToBoxAdapter(
                    child: _buildNotificationSection(),
                  ),
                  // Featured Events
                  SliverToBoxAdapter(
                    child: _buildFeaturedEvents(ctrl),
                  ),
                  // Best from Talk Section
                  SliverToBoxAdapter(
                    child: _buildBestFromTalkSection(ctrl),
                  ),
                  // 底部间距
                  const SliverToBoxAdapter(
                    child: SizedBox(height: 100),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUnifiedPageViewContent(ScHomeController controller) {
    final allPhotos = controller.allPhotos;

    if (allPhotos.isEmpty) {
      return Container(
        height: 500,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: BPColor.grey(0x1A),
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Center(
          child: MVText(
            'No content available',
            color: BPColor.grayText,
          ),
        ),
      );
    }

    return Column(
      children: [
        SizedBox(
          height: 500,
          child: PageView.builder(
            controller: controller.unifiedPageController,
            itemCount: allPhotos.length,
            onPageChanged: (index) {
              controller.updateUnifiedIndex(index);
            },
            itemBuilder: (context, index) {
              final photoInfo = allPhotos[index];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                child: _buildUnifiedContentCard(controller, photoInfo, index),
              );
            },
          ),
        ),
        const SizedBox(height: 16),
        // 显示当前photo的指示器
        _buildUnifiedPhotosIndicator(controller),
      ],
    );
  }

  Widget _buildUnifiedContentCard(ScHomeController controller, PhotoWithTabInfo photoInfo, int unifiedIndex) {
    final star = photoInfo.star;

    // 如果photo为null（占位符），显示tab的基本信息
    if (photoInfo.photo == null) {
      return GestureDetector(
        onTap: () => _handleMainContentTap(controller, photoInfo.tabIndex),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // 背景 - 使用灰色占位符
              Positioned.fill(
                child: Container(
                  color: BPColor.backgroundGrey,
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: BPColor.grayText,
                      size: 50,
                    ),
                  ),
                ),
              ),
              // 渐变遮罩
              _buildGradientOverlay(),
              // 内容信息 - 显示对应tab的信息
              _buildUnifiedContentInfo(star, controller, photoInfo.tabIndex),
            ],
          ),
        ),
      );
    }

    if (star == null) {
      return GestureDetector(
        onTap: () => _handleMainContentTap(controller, photoInfo.tabIndex),
        child: Container(
          decoration: BoxDecoration(
            color: BPColor.grey(0x1A),
            borderRadius: BorderRadius.circular(16),
          ),
          child: const Center(
            child: MVText(
              'No content available',
              color: BPColor.grayText,
            ),
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: () => _handleMainContentTap(controller, photoInfo.tabIndex),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // 背景媒体 - 显示当前photo
            Positioned.fill(
              child: _buildSinglePhotoForUnified(photoInfo.photo),
            ),
            // 渐变遮罩
            _buildGradientOverlay(),
            // 内容信息 - 显示对应tab的信息
            _buildUnifiedContentInfo(star, controller, photoInfo.tabIndex),
          ],
        ),
      ),
    );
  }

  Widget _buildUnifiedPhotosIndicator(ScHomeController controller) {
    final currentTabPhotos = controller.getCurrentTabPhotos();
    final currentPhotoIndexInTab = controller.getCurrentPhotoIndexInTab();

    // 如果当前tab没有photos或只有一张（占位符），不显示指示器
    if (currentTabPhotos.isEmpty || currentTabPhotos.length <= 1) {
      return const SizedBox.shrink();
    }

    // 如果当前tab只有占位符（photo为null），不显示指示器
    if (currentTabPhotos.length == 1 && currentTabPhotos.first.photo == null) {
      return const SizedBox.shrink();
    }

    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(currentTabPhotos.length, (index) {
          return Container(
            width: 24,
            height: 3,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(1.5),
              color: index == currentPhotoIndexInTab ? Colors.white : Colors.white.withOpacity(0.3),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildSinglePhotoForUnified(dynamic photo) {
    if (photo == null) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    String? imageUrl;
    String? mimeType;

    if (photo is Map<String, dynamic>) {
      imageUrl = photo['url'] as String?;
      mimeType = photo['mime'] as String?;
    } else {
      imageUrl = photo?.url;
      mimeType = photo?.mime;
    }

    if (imageUrl == null || imageUrl.isEmpty) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    // 检查是否为视频文件
    final isVideo = mimeType?.toLowerCase().contains('video') == true || imageUrl.toLowerCase().contains('.mp4') || imageUrl.toLowerCase().contains('.mov') || imageUrl.toLowerCase().contains('.avi');

    if (isVideo) {
      return _AutoPlayVideoWidget(
        videoUrl: imageUrl,
        key: ValueKey(imageUrl),
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white54,
            strokeWidth: 2,
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        return Container(
          color: BPColor.backgroundGrey,
          child: const Center(
            child: Icon(
              Icons.image_not_supported,
              color: BPColor.grayText,
              size: 50,
            ),
          ),
        );
      },
    );
  }

  Widget _buildUnifiedContentInfo(dynamic star, ScHomeController controller, int tabIndex) {
    final starUser = star?.user;
    final event = star?.event;

    // 根据tabIndex获取对应的tab名称
    String getTabDisplayName(int tabIndex) {
      switch (tabIndex) {
        case 0:
          return LocaleKeys.sc_str_home_this_week.tr;
        case 1:
          return LocaleKeys.sc_str_home_last_week.tr;
        case 2:
          return LocaleKeys.sc_str_home_upcoming.tr;
        default:
          return 'Unknown';
      }
    }

    return Positioned(
      left: 20,
      right: 20,
      bottom: 20,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户名或tab名称
          MVText(
            starUser?.fullname ?? getTabDisplayName(tabIndex),
            fontSize: 28,
            fontWeight: FontWeight.w900,
            color: Colors.white,
          ),
          const SizedBox(height: 4),
          // Weekly Star 标签
          MVText(
            star != null ? LocaleKeys.sc_str_home_weekly_star.tr : 'No content available',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: star != null ? BPColor.brand : BPColor.grayText,
          ),
          const SizedBox(height: 12),
          // 星级评价（仅在有数据时显示）
          if (star != null)
            StarLevelWidget(
              level: starUser?.level ?? 0,
              starWidth: 20,
              starHeight: 20,
              spacing: 3,
              showLevelPlusOne: false,
            ),
          if (star != null) const SizedBox(height: 12),
          // 事件标题
          MVText(
            event?.title ?? (star != null ? 'No event title' : 'Coming soon...'),
            fontSize: 18,
            fontWeight: FontWeight.w400,
            color: Colors.white,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          // 日期和地点（仅在有数据时显示）
          if (star != null)
            MVText(
              _formatEventInfo(event),
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.white70,
            ),
        ],
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: IgnorePointer(
        // 关键：忽略手势，让手势穿透到下层
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: [0.0, 0.6, 1.0],
              colors: [
                Colors.transparent,
                Colors.transparent,
                Colors.black87,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentInfoWithTap(dynamic star, ScHomeController controller, int tabIndex) {
    final starUser = star?.user;
    final event = star?.event;

    return Positioned(
      left: 20,
      right: 20,
      bottom: 20,
      child: GestureDetector(
        onTap: () => _handleMainContentTap(controller, tabIndex),
        onPanEnd: (details) {
          // 在文字区域的水平滑动手势传递给TabController
          const minVelocity = 300;
          if (details.velocity.pixelsPerSecond.dx.abs() > minVelocity) {
            if (details.velocity.pixelsPerSecond.dx > 0) {
              // 向右滑动 - 切换到上一个tab
              if (tabIndex > 0) _tabController.animateTo(tabIndex - 1);
            } else {
              // 向左滑动 - 切换到下一个tab
              if (tabIndex < 2) _tabController.animateTo(tabIndex + 1);
            }
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户名
            MVText(
              starUser?.fullname ?? 'Name',
              fontSize: 28,
              fontWeight: FontWeight.w900,
              color: Colors.white,
            ),
            const SizedBox(height: 4),
            // Weekly Star 标签
            MVText(
              LocaleKeys.sc_str_home_weekly_star.tr,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: BPColor.brand,
            ),
            const SizedBox(height: 12),
            // 星级评价
            StarLevelWidget(
              level: starUser?.level ?? 0,
              starWidth: 20,
              starHeight: 20,
              spacing: 3,
              showLevelPlusOne: false,
            ),
            const SizedBox(height: 12),
            // 事件标题
            MVText(
              event?.title ?? 'Title TitleTitleTitleTitleTitle',
              fontSize: 18,
              fontWeight: FontWeight.w400,
              color: Colors.white,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            // 日期和地点
            MVText(
              _formatEventInfo(event),
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.white70,
            ),
          ],
        ),
      ),
    );
  }

  dynamic _getStarByIndex(ScHomeController controller, int index) {
    switch (index) {
      case 0:
        return controller.star;
      case 1:
        return controller.lastStar;
      case 2:
        return controller.nextStar;
      default:
        return null;
    }
  }

  Widget _buildNotificationSection() {
    return GetBuilder<ScHomeController>(
      builder: (controller) {
        // Check if there are any notifications
        if (!controller.hasNotifications) {
          return const SizedBox.shrink(); // Hide the section if no notifications
        }

        final latestNotification = controller.latestNotification;
        if (latestNotification == null) {
          return const SizedBox.shrink(); // Hide if no notification data
        }

        return GestureDetector(
          onTap: () => controller.toNotification(),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Color(0xFFF3E6C4), // #F3E6C4
                  Color(0xFFD3B985), // #D3B985
                  Color(0xFFAA905D), // #AA905D
                ],
                stops: [0.0, 0.6, 1.0],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: const BoxDecoration(
                    color: BPColor.brand,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.notifications_outlined,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MVText(
                        latestNotification.message.title,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF695233),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (latestNotification.message.desc.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        MVText(
                          latestNotification.message.desc,
                          fontSize: 12,
                          color: const Color(0xFF695233).withOpacity(0.8),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  color: const Color(0xFF695233),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeaturedEvents(ScHomeController controller) {
    final normalEvents = controller.normalEvents ?? [];

    if (normalEvents.isEmpty) {
      return Container();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              MVText(
                LocaleKeys.sc_str_home_featured_events.tr,
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              GestureDetector(
                onTap: controller.onViewAllEvents,
                child: Container(
                  padding: const EdgeInsets.only(left: 16, top: 8, bottom: 8, right: 8),
                  child: MVText(
                    LocaleKeys.sc_str_common_view_all.tr,
                    fontSize: 14,
                    color: BPColor.brand,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 252,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            itemCount: normalEvents.length,
            physics: const BouncingScrollPhysics(),
            itemBuilder: (context, index) {
              final event = normalEvents[index];
              return GestureDetector(
                onTap: () => controller.toViewEvent(event),
                child: Container(
                  width: 162,
                  height: 252,
                  margin: EdgeInsets.only(
                    right: index == normalEvents.length - 1 ? 0 : 16,
                  ),
                  child: _buildEventCard(event),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEventCard(StarEventModel event) {
    return Container(
      decoration: BoxDecoration(
        color: BPColor.grey(0x1A),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 事件图片
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: BPColor.backgroundGrey,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: event.photos?.isNotEmpty == true
                    ? BPImageUtil.networkImage(
                        event.photos!.first.url ?? '',
                        fit: BoxFit.cover,
                      )
                    : Container(
                        color: BPColor.backgroundGrey,
                      ),
              ),
            ),
          ),
          // 事件信息
          Container(
            height: 80,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题使用走马灯效果 - 始终保持1行
                SizedBox(
                  height: 18,
                  child: _MarqueeText(
                    text: event.title ?? '',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      height: 1.0,
                    ),
                    maxLines: 1,
                  ),
                ),
                const SizedBox(height: 6),
                Flexible(
                  child: MVText(
                    _formatEventInfo(event),
                    fontSize: 11,
                    color: Colors.white70,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                Flexible(
                  child: MVText(
                    'Attending: ${event.extra?.membersCount ?? 0} of ${event.maxMembers}',
                    fontSize: 11,
                    color: Colors.white70,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleMainContentTap(ScHomeController controller, int index) {
    switch (index) {
      case 0:
        controller.toDetail();
        break;
      case 1:
        controller.onViewLastStar();
        break;
      case 2:
        // 处理即将到来的事件点击
        if (controller.nextStar?.event != null) {
          controller.toViewEvent(controller.nextStar!.event!);
        }
        break;
    }
  }

  String _formatEventInfo(StarEventModel? event) {
    if (event == null) return 'Jun 25 • Seoul Hannam';

    final dateStr = event.startDate != null ? DateFormat('MMM dd').format(event.startDate!) : 'TBD';
    final location = event.address ?? 'Seoul Hannam';

    return '$dateStr • $location';
  }

  Widget _buildBestFromTalkSection(ScHomeController controller) {
    final communityPosts = controller.communityPosts;
    final isLoading = controller.isLoadingCommunityPosts;
    
    print('Building Best from Talk section - isLoading: $isLoading, posts count: ${communityPosts?.length ?? 0}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 32),
        // Section title - always show
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Text(
                '🔥',
                style: TextStyle(fontSize: 20),
              ),
              SizedBox(width: 8),
              MVText(
                'Best from Talk',
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Content based on state
        if (isLoading) ...[
          // Loading state
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: BPColor.grey(0x1A),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: BPColor.brand,
                strokeWidth: 2,
              ),
            ),
          ),
        ] else if (communityPosts == null || communityPosts.isEmpty) ...[
          // Empty state
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: BPColor.grey(0x1A),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: MVText(
                'No popular posts available',
                fontSize: 14,
                color: BPColor.grayText,
              ),
            ),
          ),
        ] else ...[
          // Posts list - using existing CommunityPostCard
          ...communityPosts.take(3).map((post) => CommunityPostCard(
            current: post,
            likeAction: () => _handleCommunityPostLike(post, controller),
            profileAction: () => _handleCommunityProfileTap(post, controller),
            moreAction: () => _handleCommunityPostMore(post, controller),
            displayComments: false, // Don't show comments in home page
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            backgroundColor: BPColor.grey(0x1A),
          )),
        ],
      ],
    );
  }

  void _handleCommunityPostLike(BPCommunityPost post, ScHomeController controller) {
    // Handle like action for community post
    // You might want to call the community feed controller's like method here
    controller.toCommunityPostDetail(post);
  }

  void _handleCommunityProfileTap(BPCommunityPost post, ScHomeController controller) {
    // Handle profile tap for community post
    // Navigate to user profile if needed
    if (post.authorUser?.id != null) {
      Get.toNamed(
        Routes.COMMUNITY_PROFILE,
        parameters: {'userId': post.authorUser!.id.toString()},
      );
    }
  }

  void _handleCommunityPostMore(BPCommunityPost post, ScHomeController controller) {
    // Handle more action for community post
    controller.toCommunityPostDetail(post);
  }
}

// 走马灯文本组件
class _MarqueeText extends StatefulWidget {
  final String text;
  final TextStyle style;
  final int? maxLines;

  const _MarqueeText({
    required this.text,
    required this.style,
    this.maxLines,
  });

  @override
  State<_MarqueeText> createState() => _MarqueeTextState();
}

class _MarqueeTextState extends State<_MarqueeText> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _needsScrolling = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    // 延迟检查是否需要滚动
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkIfScrollingNeeded();
    });
  }

  void _checkIfScrollingNeeded() {
    // 简单检查文本长度，如果超过一定长度就启用滚动
    // 由于字体变小且卡片宽度为162，大约可以显示18-20个字符
    if (widget.text.length > 18) {
      setState(() {
        _needsScrolling = true;
      });
      _startScrolling();
    }
  }

  void _startScrolling() {
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 延迟1秒后重新开始
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _controller.reset();
            _controller.forward();
          }
        });
      }
    });

    // 延迟2秒后开始滚动
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted && _needsScrolling) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_needsScrolling) {
      return Text(
        widget.text,
        style: widget.style,
        maxLines: widget.maxLines,
        overflow: TextOverflow.visible,
      );
    }

    return ClipRect(
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(-_animation.value * 50, 0),
            child: Text(
              widget.text,
              style: widget.style,
              maxLines: widget.maxLines,
              overflow: TextOverflow.visible,
            ),
          );
        },
      ),
    );
  }
}

// 自动播放视频组件 - 移除播放按钮，简化交互
class _AutoPlayVideoWidget extends StatefulWidget {
  final String videoUrl;

  const _AutoPlayVideoWidget({
    super.key,
    required this.videoUrl,
  });

  @override
  State<_AutoPlayVideoWidget> createState() => _AutoPlayVideoWidgetState();
}

class _AutoPlayVideoWidgetState extends State<_AutoPlayVideoWidget> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  void _initializeVideo() async {
    try {
      _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      await _controller.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        // 设置静音、循环播放和自动播放
        _controller.setVolume(0.0);
        _controller.setLooping(true);
        _controller.play();
      }
    } catch (e) {
      // 视频初始化错误: $e
      if (mounted) {
        setState(() {
          _hasError = true;
        });
      }
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: BPColor.grayText,
                size: 50,
              ),
              SizedBox(height: 8),
              Text(
                '视频加载失败',
                style: TextStyle(
                  color: BPColor.grayText,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isInitialized) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white54,
            strokeWidth: 2,
          ),
        ),
      );
    }

    return VisibilityDetector(
      key: ValueKey(widget.videoUrl),
      onVisibilityChanged: (visibilityInfo) {
        if (_isInitialized && mounted && !_isDisposed) {
          try {
            if (visibilityInfo.visibleFraction < 0.5) {
              _controller.pause();
            } else {
              _controller.play();
            }
          } catch (e) {
            // 忽略dispose后的调用错误
          }
        }
      },
      child: FittedBox(
        fit: BoxFit.cover,
        child: SizedBox(
          width: _isDisposed ? 0 : _controller.value.size.width,
          height: _isDisposed ? 0 : _controller.value.size.height,
          child: _isDisposed ? const SizedBox.shrink() : VideoPlayer(_controller),
        ),
      ),
    );
  }
}

// 独立的StatefulWidget来处理photos的PageView，支持嵌套滑动
class _PhotosPageView extends StatefulWidget {
  final List<dynamic> photos;
  final int tabIndex;
  final VoidCallback? onReachEnd;
  final VoidCallback? onReachStart;

  const _PhotosPageView({
    required this.photos,
    required this.tabIndex,
    this.onReachEnd,
    this.onReachStart,
  });

  @override
  State<_PhotosPageView> createState() => _PhotosPageViewState();
}

class _PhotosPageViewState extends State<_PhotosPageView> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果只有一张图片，不需要PageView，直接显示
    if (widget.photos.length == 1) {
      return _buildSinglePhoto(widget.photos.first);
    }

    // 使用自定义的嵌套滑动处理
    return _NestedScrollablePageView(
      pageController: _pageController,
      itemCount: widget.photos.length,
      currentIndex: _currentIndex,
      onPageChanged: (photoIndex) {
        setState(() {
          _currentIndex = photoIndex;
        });

        // 安全地更新controller中的索引
        try {
          final controller = Get.find<ScHomeController>();
          controller.setCurrentPhotoIndex(widget.tabIndex, photoIndex);
        } catch (e) {
          // 忽略错误，避免崩溃
        }
      },
      onReachEnd: widget.onReachEnd,
      onReachStart: widget.onReachStart,
      itemBuilder: (context, photoIndex) {
        if (photoIndex >= 0 && photoIndex < widget.photos.length) {
          final photo = widget.photos[photoIndex];
          if (photo != null) {
            return _buildSinglePhoto(photo);
          }
        }
        return Container(
          color: BPColor.backgroundGrey,
          child: const Center(
            child: Icon(
              Icons.image_not_supported,
              color: BPColor.grayText,
              size: 50,
            ),
          ),
        );
      },
    );
  }

  Widget _buildSinglePhoto(dynamic photo) {
    if (photo == null) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    String? imageUrl;
    String? mimeType;

    if (photo is Map<String, dynamic>) {
      imageUrl = photo['url'] as String?;
      mimeType = photo['mime'] as String?;
    } else {
      imageUrl = photo?.url;
      mimeType = photo?.mime;
    }

    if (imageUrl == null || imageUrl.isEmpty) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    // 检查是否为视频文件
    final isVideo = mimeType?.toLowerCase().contains('video') == true || imageUrl.toLowerCase().contains('.mp4') || imageUrl.toLowerCase().contains('.mov') || imageUrl.toLowerCase().contains('.avi');

    if (isVideo) {
      return _AutoPlayVideoWidget(
        videoUrl: imageUrl,
        key: ValueKey(imageUrl),
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white54,
            strokeWidth: 2,
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        // 图片加载错误: $error, URL: $url
        return Container(
          color: BPColor.backgroundGrey,
          child: const Center(
            child: Icon(
              Icons.image_not_supported,
              color: BPColor.grayText,
              size: 50,
            ),
          ),
        );
      },
    );
  }
}

// 支持嵌套滑动边界检测的PageView组件
class _NestedScrollablePageView extends StatefulWidget {
  final PageController pageController;
  final int itemCount;
  final int currentIndex;
  final Function(int) onPageChanged;
  final Widget Function(BuildContext, int) itemBuilder;
  final VoidCallback? onReachEnd; // 到达最后一页时回调
  final VoidCallback? onReachStart; // 到达第一页时回调

  const _NestedScrollablePageView({
    required this.pageController,
    required this.itemCount,
    required this.currentIndex,
    required this.onPageChanged,
    required this.itemBuilder,
    this.onReachEnd,
    this.onReachStart,
  });

  @override
  State<_NestedScrollablePageView> createState() => _NestedScrollablePageViewState();
}

class _NestedScrollablePageViewState extends State<_NestedScrollablePageView> {
  bool _isAtEnd = false;
  bool _isAtStart = true;
  bool _hasTriggeredEndCallback = false;
  bool _hasTriggeredStartCallback = false;

  // 设置过滚动阈值，避免轻微回弹就触发
  static const double _overscrollThreshold = 10.0; // 降低阈值，使切换更敏感
  static const bool _debugOverscroll = false; // 调试模式，打印过滚动信息

  @override
  void initState() {
    super.initState();
    _updateBoundaryState();
  }

  @override
  void didUpdateWidget(_NestedScrollablePageView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _updateBoundaryState();
      // 重置触发标志
      _hasTriggeredEndCallback = false;
      _hasTriggeredStartCallback = false;
    }
  }

  void _updateBoundaryState() {
    _isAtStart = widget.currentIndex == 0;
    _isAtEnd = widget.currentIndex == widget.itemCount - 1;
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollEndNotification>(
      onNotification: (notification) {
        // 滑动结束时重置触发标志，允许下次触发
        _hasTriggeredEndCallback = false;
        _hasTriggeredStartCallback = false;
        return false;
      },
      child: NotificationListener<OverscrollNotification>(
        onNotification: (notification) {
          if (_debugOverscroll) {
            print('Overscroll: ${notification.overscroll}, isAtEnd: $_isAtEnd, isAtStart: $_isAtStart');
          }

          // 检测过滚动，需要达到阈值且在边界时才传递手势
          if (notification.overscroll > _overscrollThreshold && _isAtEnd && !_hasTriggeredEndCallback) {
            // 向右过滚动超过阈值且在最后一页，触发切换到下一个tab
            if (_debugOverscroll) {
              print('Triggering onReachEnd - overscroll: ${notification.overscroll}');
            }
            _hasTriggeredEndCallback = true;
            widget.onReachEnd?.call();
            return true;
          } else if (notification.overscroll < -_overscrollThreshold && _isAtStart && !_hasTriggeredStartCallback) {
            // 向左过滚动超过阈值且在第一页，触发切换到上一个tab
            if (_debugOverscroll) {
              print('Triggering onReachStart - overscroll: ${notification.overscroll}');
            }
            _hasTriggeredStartCallback = true;
            widget.onReachStart?.call();
            return true;
          }
          return false;
        },
        child: PageView.builder(
          controller: widget.pageController,
          itemCount: widget.itemCount,
          onPageChanged: (index) {
            widget.onPageChanged(index);
            setState(() {
              _isAtStart = index == 0;
              _isAtEnd = index == widget.itemCount - 1;
            });
          },
          itemBuilder: widget.itemBuilder,
        ),
      ),
    );
  }
}

// 自定义TabBar指示器，精确控制24x3尺寸
class _CustomTabIndicator extends Decoration {
  final Color color;
  final double width;
  final double height;

  const _CustomTabIndicator({
    required this.color,
    required this.width,
    required this.height,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomTabIndicatorPainter(
      color: color,
      width: width,
      height: height,
    );
  }
}

class _CustomTabIndicatorPainter extends BoxPainter {
  final Color color;
  final double width;
  final double height;

  _CustomTabIndicatorPainter({
    required this.color,
    required this.width,
    required this.height,
  });

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final double indicatorLeft = offset.dx + (configuration.size!.width - width) / 2;
    final double indicatorTop = offset.dy + configuration.size!.height - height;

    final RRect rRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(indicatorLeft, indicatorTop, width, height),
      const Radius.circular(1.5),
    );

    canvas.drawRRect(rRect, paint);
  }
}

// 单张图片组件，支持手势穿透到外层TabBarView
class _SinglePhotoWithGesturePassthrough extends StatelessWidget {
  final dynamic photo;
  final int tabIndex;
  final VoidCallback onSwipeLeft;
  final VoidCallback onSwipeRight;

  const _SinglePhotoWithGesturePassthrough({
    required this.photo,
    required this.tabIndex,
    required this.onSwipeLeft,
    required this.onSwipeRight,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanEnd: (details) {
        // 检测滑动方向和速度
        const minVelocity = 300;
        if (details.velocity.pixelsPerSecond.dx.abs() > minVelocity) {
          if (details.velocity.pixelsPerSecond.dx > 0) {
            // 向右滑动
            onSwipeRight();
          } else {
            // 向左滑动
            onSwipeLeft();
          }
        }
      },
      child: _buildSinglePhoto(photo),
    );
  }

  Widget _buildSinglePhoto(dynamic photo) {
    if (photo == null) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    String? imageUrl;
    String? mimeType;

    if (photo is Map<String, dynamic>) {
      imageUrl = photo['url'] as String?;
      mimeType = photo['mime'] as String?;
    } else {
      imageUrl = photo?.url;
      mimeType = photo?.mime;
    }

    if (imageUrl == null || imageUrl.isEmpty) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
      return Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: BPColor.grayText,
            size: 50,
          ),
        ),
      );
    }

    // 检查是否为视频文件
    final isVideo = mimeType?.toLowerCase().contains('video') == true || imageUrl.toLowerCase().contains('.mp4') || imageUrl.toLowerCase().contains('.mov') || imageUrl.toLowerCase().contains('.avi');

    if (isVideo) {
      return _AutoPlayVideoWidget(
        videoUrl: imageUrl,
        key: ValueKey(imageUrl),
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: BPColor.backgroundGrey,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white54,
            strokeWidth: 2,
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        return Container(
          color: BPColor.backgroundGrey,
          child: const Center(
            child: Icon(
              Icons.image_not_supported,
              color: BPColor.grayText,
              size: 50,
            ),
          ),
        );
      },
    );
  }
}
