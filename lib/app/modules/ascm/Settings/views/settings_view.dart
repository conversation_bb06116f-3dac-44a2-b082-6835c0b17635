import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:starchex/app/shared/components/BPGetView.dart';
import '../../../../routes/app_pages.dart';
import '../../../../shared/account/BPSessionManager.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../MyPage/widgets/ServerSettingsBottomSheet.dart' as ServerSettingsBottomSheet;
import '../controllers/settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingsController>(
      init: SettingsController(),
      tag: Random().nextDouble().toString(),
      builder: (controller) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
          child: Scaffold(
            backgroundColor: BPColor.background,
            appBar: createAppBar(
              title: 'Settings',
              backgroundColor: BPColor.background,
              foregroundColor: BPColor.whiteText,
            ),
            body: SafeArea(
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: Column(
                          children: [
                            const SizedBox(height: 20),
                            // 主要设置区块
                            Container(
                              decoration: BoxDecoration(
                                color: const Color(0xFF1E1E1E),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                children: [
                                  'About Starchex',
                                  'Feedback',
                                  'Terms & Conditions',
                                  'Privacy Policy',
                                  'Safety and Policy Center',
                                  'Settings',
                                ].asMap().entries.map((entry) {
                                  int index = entry.key;
                                  String item = entry.value;
                                  bool isLast = index == 5; // 最后一项

                                  return Column(
                                    children: [
                                      Material(
                                        color: Colors.transparent,
                                        child: InkWell(
                                          onTap: () {
                                            if (item == 'About Starchex') {
                                              Get.toNamed(Routes.DOC_VIEWER, parameters: {'file': 'mypage_about'});
                                            }
                                            if (item == 'Settings') {
                                              Get.toNamed(Routes.SETTINGS_INDEX);
                                            }
                                            if (item == 'Terms & Conditions') {
                                              Get.toNamed(Routes.DOC_VIEWER, parameters: {'file': 'terms'});
                                            }
                                            if (item == 'Privacy Policy') {
                                              Get.toNamed(Routes.DOC_VIEWER, parameters: {'file': 'privacy'});
                                            }
                                            if (item == 'Safety and Policy Center') {
                                              Get.toNamed(Routes.DOC_VIEWER, parameters: {'file': 'safety_and_policy_center'});
                                            }
                                            if (item == 'Feedback') {
                                              Get.toNamed(Routes.FEEDBACK);
                                            }
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                                            child: Row(
                                              children: [
                                                Text(
                                                  item,
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w400,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                                const Spacer(),
                                                const Icon(
                                                  Icons.arrow_forward_ios,
                                                  color: Color(0xFF666666),
                                                  size: 16,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      if (!isLast)
                                        Container(
                                          height: 1,
                                          width: double.infinity,
                                          color: const Color(0xFF2A2A2A),
                                        ),
                                    ],
                                  );
                                }).toList(),
                              ),
                            ),
                            const SizedBox(height: 20),
                            // Server Settings 单独区块
                            if (controller.isWhitelisted)
                              Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFF1E1E1E),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(12),
                                    onTap: () {
                                      ServerSettingsBottomSheet.showBottomSheet();
                                    },
                                    child: const Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                                      child: Row(
                                        children: [
                                          Text(
                                            'Server Settings',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.white,
                                            ),
                                          ),
                                          Spacer(),
                                          Icon(
                                            Icons.arrow_forward_ios,
                                            color: Color(0xFF666666),
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    height: 54,
                    width: double.infinity,
                    margin: const EdgeInsets.only(bottom: 40, left: 15, right: 15),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1E1E1E),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          BPSessionManager.instance.logout();
                        },
                        child: const Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.logout,
                                color: Color(0xFFFF3B30),
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Log out',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFFFF3B30),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
