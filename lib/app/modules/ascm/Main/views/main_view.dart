import 'package:flutter/material.dart';
import 'dart:ui';

import 'package:get/get.dart';
import 'package:starchex/app/modules/ascm/Settings/views/settings_view.dart';
import 'package:uuid/uuid.dart';

import '../../../../shared/components/BPColorUtil.dart';
import '../../../../shared/components/BPImageUtil.dart';
import '../../../../shared/account/BPSessionManager.dart';
import '../../../CommunityV2/CommunityFeed/views/community_feed_view.dart';

// TODO: Removed import for deleted PassedCards module
// import '../../../PassedCards/views/passed_cards_view.dart';
import '../../Benefits/views/benefits_view.dart';
import '../../SCMy/views/s_c_my_view.dart';
import '../../ScHome/views/sc_home_view.dart';
import '../controllers/main_controller.dart';

class MainView extends GetView<MainController> {
  const MainView({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MainController>(
        init: MainController(),
        tag: const Uuid().v4(),
        builder: (controller) {
          return Scaffold(
          extendBody: true,
            body: Obx(
              () => IndexedStack(
                index: controller.selectedTabIndex.value,
                children: const [
                  ScHomeView(),
                  // TODO: Removed PassedCardsView as module was deleted
                  // PassedCardsView(),
                  // ConversationView(),
                  BenefitsView(),
                  SCMyView(),
                  CommunityFeedView(),
                SettingsView(),
                ],
              ),
            ),
            // const SizedBox(height: 80),
          bottomNavigationBar: ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF2B2B2B).withOpacity(0.68),
                ),
                child: SafeArea(
                child: SizedBox(
                  height: 60,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: controller.tabs.asMap().entries.map((e) {
                      final i = e.key;
                      final tab = e.value;
                      return Obx(
                        () {
                          final isSelected = controller.selectedTabIndex.value == i;
                          
                        // 特殊处理头像tab
                        if (tab.isAvatar) {
                          return GestureDetector(
                            onTap: () {
                              controller.tabController.animateTo(i);
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 5),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: 28,
                                    height: 28,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: isSelected ? BPColor.gold : Colors.white70, // 选中时金色，未选中时与其他按钮一致
                                        width: 2,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(2), // 内边距等于边框宽度
                                      child: ClipOval(
                                        child: BPImageUtil.networkImage(
                                          BPSessionManager.accountModelConvenience?.fakeAvatar?.url,
                                          fit: BoxFit.cover,
                                          scDirForDefault: true,
                                          defaultNamed: 'avatar_icon.svg',
                                        ),
                                      ),
                                    ),
                                  ),
                                  Text(
                                    BPSessionManager.accountModelConvenience?.username ?? 'User',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: isSelected ? Colors.white : Colors.white70,
                                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        // 普通tab的处理
                          final imgName = isSelected ? tab.icon : tab.unselectedIcon;
                          return GestureDetector(
                            onTap: () {
                              controller.tabController.animateTo(i);
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: BPImageUtil.scSvgImage(imgName),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    tab.label ?? '',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: isSelected ? Colors.white : Colors.white70,
                                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
            ),
          ),
          );
      },
    );
  }
}
