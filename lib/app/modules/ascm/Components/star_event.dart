import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:starchex/app/data/scdata/mv_star_event_model.dart';
import 'package:starchex/app/shared/components/BPTimeUtil.dart';
import 'package:starchex/app/shared/components/mv_text_button.dart';
import 'package:starchex/generated/locales.g.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/BPImageUtil.dart';
import '../../../shared/components/BPMediaThumbnail.dart';

class InfoCell extends StatelessWidget {
  const InfoCell({super.key, required this.icon, required this.title, this.color, this.fontSize, this.mainAxisAlignment, this.width, this.height, this.iconColor});
  final String icon;
  final String title;
  final Color? color;
  final double? fontSize;
  final MainAxisAlignment? mainAxisAlignment;
  final double? width;
  final double? height;
  final Color? iconColor;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
      children: [
        BPImageUtil.scSvgImage(icon, width: width, height: height, color: iconColor ?? color ?? BPColor.background),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: fontSize ?? 13,
              fontWeight: FontWeight.w400,
              color: color ?? BPColor.background,
            ),
          ),
        ),
      ],
    );
  }
}

class StarEventCell extends StatelessWidget {
  const StarEventCell({super.key, required this.event, this.joinAction});
  final StarEventModel event;
  final Function()? joinAction;
  @override
  Widget build(BuildContext context) {
    final photo = event.images?.firstOrNull?.url;
    final video = event.videos?.firstOrNull?.url;
    return Container(
      padding: const EdgeInsets.all(0),
      color: BPColor.grey2A,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: SizedBox(
              width: double.infinity,
              child: BPMediaThumbnail(
                sourceUrl: video ?? photo ?? '',
                isVideo: video != null,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 12, right: 12, top: 15, bottom: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 50,
                  child: Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      event.title ?? '',
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          InfoCell(
                            icon: 'location_icon.svg',
                            title: event.address ?? '',
                            fontSize: 13,
                            color: BPColor.greyAA,
                          ),
                          const SizedBox(height: 5),
                          InfoCell(
                            icon: 'date_icon.svg',
                            title: event.startDate == null ? '' : event.startDate!.commonDateTimeString,
                            fontSize: 13,
                            color: BPColor.greyAA,
                          ),
                          const SizedBox(height: 5),
                          InfoCell(
                            icon: 'seat_icon.svg',
                            title: '${LocaleKeys.sc_str_common_members.tr} ${event.extra?.membersCount ?? 0}/${event.maxMembers}',
                            fontSize: 13,
                            color: BPColor.greyAA,
                          ),
                        ],
                      ),
                    ),
                    if (joinAction != null)
                      MVTextButton(
                        title: event.joinStatusText,
                        textColor: (event.extra?.status == EventJoinStatus.applied || event.extra?.status == EventJoinStatus.rejected) ? BPColor.greyText : Colors.white,
                        backgroundColor: (event.extra?.status == EventJoinStatus.applied || event.extra?.status == EventJoinStatus.rejected) ? BPColor.backgroundGrey : BPColor.gold,
                        mainAxisSize: MainAxisSize.min,
                        fontSize: 18,
                        minimumSize: const Size(100, 36),
                        fontWeight: FontWeight.w500,
                        borderRadius: 20,
                        onPressed: joinAction,
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class StarEventUpcomingCell extends StatelessWidget {
  const StarEventUpcomingCell({super.key, required this.event});
  final StarEventModel event;
  @override
  Widget build(BuildContext context) {
    final photo = event.images?.firstOrNull?.url;
    final video = event.videos?.firstOrNull?.url;
    return Container(
      padding: const EdgeInsets.all(0),
      color: BPColor.backgroundGrey,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AspectRatio(
            aspectRatio: 1,
            child: BPMediaThumbnail(
              sourceUrl: video ?? photo ?? '',
              isVideo: video != null,
              fit: BoxFit.cover,
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MVTextButton(
                    title: event.joinStatusText,
                    textColor: BPColor.whiteText,
                    backgroundColor: BPColor.gold,
                    mainAxisSize: MainAxisSize.min,
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    borderRadius: 4,
                  ),
                  const SizedBox(height: 3),
                  SizedBox(
                    height: 42,
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        (event.title ?? ''),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: BPColor.whiteText,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      BPImageUtil.scSvgImage('countdown_icon.svg'),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          event.startDate == null ? '' : event.startDate!.commonDateTimeString,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: BPColor.gold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      BPImageUtil.scSvgImage('location_icon.svg', color: BPColor.greyText),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          event.address ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: BPColor.greyText,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

enum EventCardType { upcoming, past }

class MyEventCard extends StatelessWidget {
  const MyEventCard({super.key, required this.event, this.type = EventCardType.upcoming});
  final StarEventModel event;
  final EventCardType type;

  String _getTimeUntilStart(DateTime startDate) {
    final now = DateTime.now();
    final difference = startDate.difference(now);

    if (difference.inDays > 0) {
      return 'Starts in ${difference.inDays} day${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'Starts in ${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'Starts in ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
    } else if (difference.inSeconds > 0) {
      return 'Starting soon';
    } else {
      return 'Started';
    }
  }

  String _getPastEventDate(DateTime startDate) {
    return '${startDate.year}.${startDate.month}.${startDate.day}';
  }

  @override
  Widget build(BuildContext context) {
    final photo = event.images?.firstOrNull?.url;
    final video = event.videos?.firstOrNull?.url;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      height: 120, // Fixed height to match image
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A), // Dark grey background
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Left side image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              bottomLeft: Radius.circular(12),
            ),
            child: SizedBox(
              width: 120,
              height: 120, // This should now fill the container height exactly
              child: BPMediaThumbnail(
                sourceUrl: video ?? photo ?? '',
                isVideo: video != null,
                fit: BoxFit.cover,
              ),
            ),
          ),
          // Right side content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Status badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                    decoration: BoxDecoration(
                      color: type == EventCardType.past ? BPColor.grayE6 : BPColor.gold,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      type == EventCardType.past ? 'Past' : 'Joined',
                      style: TextStyle(
                        color: type == EventCardType.past ? Colors.black : const Color(0xFF7D5C2B),
                        fontSize: 10,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Event title
                  Text(
                    event.title ?? '',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      height: 1.1,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  // Time display with icon
                  Row(
                    children: [
                      BPImageUtil.scSvgImage(type == EventCardType.past ? 'date_icon.svg' : 'countdown_icon.svg', width: 16, height: 16, color: type == EventCardType.past ? BPColor.greyText : BPColor.gold),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          event.startDate == null ? '' : (type == EventCardType.past ? _getPastEventDate(event.startDate!) : _getTimeUntilStart(event.startDate!)),
                          style: TextStyle(
                            color: type == EventCardType.past ? BPColor.greyText : BPColor.gold,
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // Location with icon
                  Row(
                    children: [
                      BPImageUtil.scSvgImage('location_icon.svg', width: 16, height: 16, color: BPColor.greyText),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          event.address ?? '',
                          style: const TextStyle(
                            color: BPColor.greyText,
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
