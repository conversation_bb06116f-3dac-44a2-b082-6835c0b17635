import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:starchex/app/data/BPUserModels.dart';
import 'package:starchex/app/data/BPAccountModels.dart';
import 'package:starchex/app/data/scdata/mv_star_event_model.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:starchex/app/routes/app_pages.dart';
import 'package:starchex/app/shared/account/BPSessionManager.dart';
import 'package:starchex/app/shared/components/BPEasyLoading.dart';
import 'package:uuid/uuid.dart';

import '../../../../../generated/locales.g.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../../shared/components/BPImageUtil.dart';
import '../../../../shared/components/mv_text.dart';
import '../../Components/circle_avatar.dart';
import '../../Components/star_event.dart';
import '../../Components/star_level_widget.dart';
import '../controllers/s_c_my_controller.dart';

class SCMyView extends GetView<SCMyController> {
  const SCMyView({super.key});

  /// Handle Asset Verification button click
  /// First fetch latest user info, then navigate based on financial status
  Future<void> _handleAssetVerificationClick() async {
    try {
      // Show loading indicator
      BPEasyLoading.show();

      // Fetch latest user information
      await BPSessionManager.instance.fetchMe();

      // Get updated user model
      final userModel = BPSessionManager.instance.accountSession?.accountModel;

      // Navigate based on financial status
      if (userModel?.financialStatus == BPFinancialStatus.submitted) {
        // If status is submitted, go to under review page
        Get.toNamed(Routes.ASSET_UNDER_REVIEW);
      } else if (userModel?.financialStatus != null && userModel?.financialStatus != BPFinancialStatus.doNotAuthorize && userModel?.financialStatus != BPFinancialStatus.submitted) {
        // If user has already passed asset verification, go to success page
        Get.toNamed(Routes.ASSET_VERIFICATION_SUCCESS);
      } else {
        // Otherwise, go to normal asset verification flow
        Get.toNamed(Routes.ASSET_VERIFICATION);
      }
    } catch (e) {
      // If fetch fails, show error and navigate to normal flow
      BPEasyLoading.showError(LocaleKeys.common_net_error.tr);
      Get.toNamed(Routes.ASSET_VERIFICATION);
    } finally {
      // Always dismiss loading indicator
      BPEasyLoading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SCMyController>(
      init: SCMyController(),
      tag: const Uuid().v4(),
      builder: (controller) {
        final user = controller.user;
        return Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            title: Align(
              alignment: Alignment.centerLeft,
              child: MVText(
                'Hello, ${user?.username ?? ''}',
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.w700,
              ),
            ),
            centerTitle: true,
            backgroundColor: Colors.transparent,
          ),
          body: SafeArea(
            bottom: false,
            child: NestedScrollView(
              controller: controller.mainScrollController,
              headerSliverBuilder: (
                BuildContext context,
                bool innerBoxIsScrolled,
              ) {
                return [
                  SliverAppBar(
                    expandedHeight: controller.headerHeight,
                    collapsedHeight: 0,
                    toolbarHeight: 0,
                    pinned: true,
                    floating: false,
                    backgroundColor: Colors.black,
                    foregroundColor: BPColor.backgroundGrey,
                    elevation: 0,
                    // title: Text(controller.isScrollTop.value ? user?.fakeName ?? '' : ''),
                    flexibleSpace: FlexibleSpaceBar(
                      background: Container(
                        padding: const EdgeInsets.only(left: 20, right: 20, top: 20),
                        child: profileHeader(
                          user: user,
                          clickEditProfile: controller.goToEditProfile,
                          clickSettings: controller.goToSettings,
                        ),
                      ),
                    ),
                    bottom: FixedTabBar(
                      tabController: controller.tabController,
                      tabTitles: controller.tabTitles,
                    ),
                  ),
                ];
              },
              body: mainTabs(controller),
            ),
          ),
        );
      },
    );
  }

  Container mainTabs(SCMyController controller) {
    Widget postListWidget(List<StarEventModel> items, {bool isPast = false}) {
      return ListView.separated(
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        padding: const EdgeInsets.only(bottom: 100, top: 10),
        itemCount: items.length,
        itemBuilder: (BuildContext context, int index) {
          final item = items[index];
          return InkWell(
            onTap: () {
              // Handle card tap action here
            },
            child: MyEventCard(
              event: item,
              type: isPast ? EventCardType.past : EventCardType.upcoming,
            ),
          );
        },
      );
    }

    return Container(
      color: BPColor.backgroundGrey,
      child: TabBarView(
        controller: controller.tabController,
        children: controller.tabTitles.asMap().entries.map(
          (e) {
            if (e.key == 0) {
              return SmartRefresher(
                header: const MaterialClassicHeader(
                  backgroundColor: BPColor.brand,
                  color: Colors.white,
                ),
                controller: controller.refreshUpcommingEventsController,
                onRefresh: () {
                  controller.loadUpcommingEvents(reload: true);
                },
                enablePullUp: false,
                enablePullDown: true,
                onLoading: controller.loadUpcommingEvents,
                child: (controller.upcommingEvents.isEmpty ? emptyWidget() : postListWidget(controller.upcommingEvents, isPast: false)),
              );
            } else if (e.key == 1) {
              return SmartRefresher(
                header: const MaterialClassicHeader(
                  backgroundColor: BPColor.brand,
                  color: Colors.white,
                ),
                controller: controller.refreshPastEventsController,
                onRefresh: () {
                  controller.loadPastEvents(reload: true);
                },
                enablePullUp: false,
                enablePullDown: true,
                onLoading: controller.loadPastEvents,
                child: (controller.pastEvents.isEmpty ? emptyWidget() : postListWidget(controller.pastEvents, isPast: true)),
              );
            } else {
              return Container();
            }
          },
        ).toList(),
      ),
    );
  }

  Widget profileHeader({
    BPUserModel? user,
    void Function()? clickEditProfile,
    void Function()? clickSettings,
  }) {
    Widget headerBTN({String? icon, String? title, void Function()? action}) {
      return Expanded(
        child: Material(
          color: BPColor.backgroundGrey,
          borderRadius: BorderRadius.circular(40),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            splashColor: BPColor.brand.withOpacity(0.2),
            highlightColor: BPColor.brand.withOpacity(0.1),
            onTap: action,
            child: Container(
              height: 40,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (icon != null) ...[
                    BPImageUtil.scSvgImage(icon),
                    const SizedBox(width: 4),
                  ],
                  Text(
                    title ?? '',
                    style: const TextStyle(
                      color: BPColor.whiteText,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    final fakeAvatarUrl = user?.fakeAvatar?.url;
    return Column(
      children: [
        Column(
          children: [
            GestureDetector(
              onTap: () {
                Get.toNamed(Routes.AVATAR_GENERATION);
              },
              child: Stack(
                children: [
                  SCCircleAvatar(
                    url: fakeAvatarUrl,
                    size: 100,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 26,
                      height: 26,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: BPColor.brand,
                      ),
                      child: Center(
                        child: BPImageUtil.scSvgImage('camera.svg', width: 16, height: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 15),
            Text(
              user?.username ?? '',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 8),
            StarLevelWidget(
              level: user?.level ?? 0,
              starWidth: 16,
              starHeight: 16,
              spacing: 2,
            ),
          ],
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            headerBTN(
                icon: 'verify_icon.svg',
                title: 'Asset Verification',
                action: _handleAssetVerificationClick),
            const SizedBox(width: 16),
            headerBTN(icon: 'my_edit.svg', title: 'Edit Profile', action: clickEditProfile),
          ],
        ),
      ],
    );
  }
}

class FixedTabBar extends StatelessWidget implements PreferredSizeWidget {
  final TabController tabController;
  final List<String> tabTitles;

  const FixedTabBar({super.key, required this.tabController, required this.tabTitles});

  @override
  Size get preferredSize => const Size.fromHeight(76);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: BPColor.backgroundGrey,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
      ),
      padding: const EdgeInsets.only(left: 0, right: 0, top: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            color: Colors.transparent,
            height: 45,
            child: TabBar(
              indicatorColor: BPColor.brand,
              indicatorWeight: 4.0,
              indicatorSize: TabBarIndicatorSize.label,
              padding: const EdgeInsets.only(left: 20),
              dividerHeight: 0,
              tabAlignment: TabAlignment.fill,
              labelPadding: const EdgeInsets.symmetric(horizontal: 10),
              labelColor: BPColor.whiteText,
              // unselectedLabelColor: const Color(0xFF8c8c8c),
              labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              unselectedLabelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              controller: tabController,
              // isScrollable: true,
              tabs: tabTitles.asMap().entries.map((item) {
                return Tab(
                  child: Text(item.value),
                );
              }).toList(),
            ),
          ),
          // Divider(color: Colors.white.withOpacity(0.1)), // Removed white line
        ],
      ),
    );
  }
}

Widget emptyWidget({bool isComment = false}) {
  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      BPImageUtil.svgImage('community_post_empty.svg'),
      const SizedBox(height: 30),
      const Text(
        'No results.',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: BPColor.whiteText,
        ),
      ),
    ],
  );
}
