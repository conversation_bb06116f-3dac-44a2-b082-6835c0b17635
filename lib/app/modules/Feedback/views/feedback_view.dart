import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import '../../../shared/components/BPBaseInfoInputFiled.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/BPGetView.dart';
import '../../../shared/components/BPMediaSection.dart';
import '../../../shared/components/BPSimpleSelectMenu.dart';
import '../../../shared/components/BPTextButton.dart';
import '../../../../generated/locales.g.dart';

import '../controllers/feedback_controller.dart';

class FeedbackView extends GetView<FeedbackController> {
  const FeedbackView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: BPColor.background,
      appBar: createAppBar(
        title: LocaleKeys.my_page_feedback.tr,
        backgroundColor: BPColor.background,
        foregroundColor: BPColor.whiteText,
      ),
      body: GetBuilder<FeedbackController>(
        builder: (controller) => SafeArea(
          child: Container(
            padding: const EdgeInsets.only(left: 24, right: 24, top: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: FormBuilder(
                      key: controller.formKey,
                      autovalidateMode: controller.autoValidate,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // CustomInputField(
                          //   name: 'email',
                          //   isPrimary: false,
                          //   label: LocaleKeys.feedback_email_label.tr,
                          //   hintText: LocaleKeys.feedback_email_placeholder.tr,
                          // ),
                          // const SizedBox(height: 16),
                          Label(title: LocaleKeys.feedback_email_label.tr),
                          const SizedBox(height: 12),
                          BPBaseInfoInputFiled(
                            borderRadius: 8,
                            backgroundColor: BPColor.backgroundGrey,
                            focusNode: controller.emailFocusNode,
                            keyboardType: TextInputType.emailAddress,
                            autofocus: true,
                            maxLength: controller.emailInput.maxLength,
                            hintText: LocaleKeys.feedback_email_placeholder.tr,
                            errorMessage: controller.emailInput.errorMsg,
                            borderEnable: controller.emailInput.highlight,
                            highlight: controller.emailInput.highlight,
                            onChanged: (val) {
                              controller.emailInput.value = val;
                              controller.validateEmail();
                            },
                            controller: controller.emailInput.textEditingController!,
                          ),
                          const SizedBox(height: 24),
                          Label(title: LocaleKeys.feedback_inquiry_label.tr),
                          const SizedBox(height: 12),
                          BPSimpleSelectMenu(
                            menuItemData: controller.feedbackCategories.map((e) => BPMenuItemData(text: e.name)).toList(),
                            initialIndex: 0,
                            onChanged: (index) {
                              controller.pickedCatIndex = index;
                            },
                            placeholderText: LocaleKeys.feedback_inquiry_placeholder.tr,
                            requested: true,
                            primaryColor: BPColor.gold,
                            backgroundColor: BPColor.grey24,
                            textColor: BPColor.whiteText,
                            borderColor: BPColor.grey24,
                            iconColor: BPColor.whiteText,
                          ),
                          const SizedBox(height: 24),
                          BPBaseInfoInputFiled(
                            borderRadius: 8,
                            backgroundColor: BPColor.backgroundGrey,
                            focusNode: controller.contentFocusNode,
                            maxLength: 1000,
                            maxLines: 10,
                            keyboardType: TextInputType.multiline,
                            hintText: LocaleKeys.feedback_content_placeholder.tr,
                            errorMessage: controller.contentInput.errorMsg,
                            highlight: controller.contentInput.highlight,
                            borderEnable: controller.contentInput.highlight,
                            onChanged: (val) {
                              controller.contentInput.value = val;
                              controller.validateContent();
                            },
                            controller: controller.contentInput.textEditingController!,
                          ),
                          const SizedBox(height: 24),
                          BPMediaSection(controller: controller.fileSection, titleStyle: const TextStyle(fontSize: 16, color: BPColor.whiteText)),
                          const SizedBox(height: 30),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 44,
                  child: BPTextButton(
                    title: LocaleKeys.common_ok.tr,
                    backgroundColor: controller.isValid ? BPColor.gold : BPColor.grey66,
                    color: Colors.white,
                    borderRadius: 24,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    onPressed: () async {
                      controller.submit();
                    },
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Label extends StatelessWidget {
  final String title;
  const Label({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: const TextStyle(
        color: BPColor.whiteText,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}
