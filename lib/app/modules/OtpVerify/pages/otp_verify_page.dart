import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../../routes/app_pages.dart';
import '../../../shared/components/BPColorUtil.dart';
import '../../../shared/components/BPGetView.dart';
import '../../../shared/widgets/custom_country_picker.dart';
import '../../../shared/widgets/primary_button.dart';
import '../controllers/otp_verify_page_controller.dart';

class OtpVerifyPage extends GetWidget<OtpVerifyPageController> {
  static Future<dynamic> goToOtpVerifyPage({
    String? countryCode,
    String? phoneNumber,
    // Is update phone is use for the user if they want to change their phone number, not verify their phone if they don't have their phone already
    bool isUpdatePhone = false,
  }) async {
    return await Get.toNamed(
      Routes.OTP,
      arguments: {
        "countryCode": countryCode,
        "phoneNumber": phoneNumber,
        "isUpdatePhone": isUpdatePhone,
      },
    );
  }

  const OtpVerifyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OtpVerifyPageController>(
      builder: (_) {
        return Scaffold(
          backgroundColor: BPColor.background,
          appBar: createAppBar(
            title: LocaleKeys.otp_verify_page_title.tr,
            backgroundColor: BPColor.background,
            foregroundColor: BPColor.whiteText,
          ),
          body: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  LocaleKeys.personal_basic_info_phone_number.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: BPColor.whiteText,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 44,
                        child: TextFormField(
                          controller: controller.phoneNumberController,
                          keyboardType: TextInputType.phone,
                          style: const TextStyle(color: BPColor.whiteText),
                          onChanged: (value) {
                            controller.update();
                          },
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          cursorColor: BPColor.brand,
                          decoration: InputDecoration(
                            hintText: LocaleKeys.personal_basic_info_phone_number_PH.tr,
                            hintStyle: const TextStyle(
                              fontWeight: FontWeight.w400,
                              color: BPColor.greyText,
                              fontSize: 14,
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              vertical: 0,
                              horizontal: 10,
                            ),
                            filled: true,
                            fillColor: BPColor.grey24,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(color: BPColor.grey2A),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(color: BPColor.grey2A),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(color: BPColor.brand),
                            ),
                            prefixIcon: GestureDetector(
                              onTap: () {
                                CustomCountryPicker.showCountryPickerBottomSheet(
                                  showCountryPhoneCode: true,
                                  onSelect: controller.onCountrySelected,
                                );
                              },
                              child: Container(
                                constraints: const BoxConstraints(minWidth: 80),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      controller.phoneCodeFormatted.isEmpty ? "e.g, +1" : controller.phoneCodeFormatted,
                                      style: controller.phoneCodeFormatted.isEmpty
                                          ? const TextStyle(
                                              fontWeight: FontWeight.w400,
                                              color: BPColor.greyText,
                                            )
                                          : const TextStyle(
                                              fontWeight: FontWeight.w400,
                                              color: BPColor.whiteText,
                                            ),
                                    ),
                                    const SizedBox(width: 8),
                                    const Icon(Icons.keyboard_arrow_down_rounded, color: BPColor.whiteText),
                                    const SizedBox(
                                      height: 30,
                                      child: VerticalDivider(color: BPColor.grey2A),
                                    ),
                                  ],
                                ).paddingSymmetric(horizontal: 10),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Builder(
                      builder: (context) {
                        return FilledButton(
                          onPressed: controller.isSentButtonEnable ? controller.sendSms : null,
                          // onPressed: controller.sendSms,
                          style: ButtonStyle(
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            minimumSize: const WidgetStatePropertyAll(Size(44, 44)),
                            shape: WidgetStatePropertyAll(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            backgroundColor: WidgetStateProperty.resolveWith((states) {
                              if (states.contains(WidgetState.disabled)) {
                                return BPColor.grey2A;
                              }

                              return BPColor.brand;
                            }),
                            foregroundColor: MaterialStateProperty.resolveWith((states) {
                              if (states.contains(WidgetState.disabled)) {
                                return BPColor.greyText;
                              }

                              return Colors.white;
                            }),
                          ),
                          child: Text(LocaleKeys.common_verify.tr),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                if (controller.otpResponseModel != null && controller.isSent)
                  TextFormField(
                    controller: controller.codeController,
                    keyboardType: TextInputType.number,
                    style: const TextStyle(color: BPColor.whiteText),
                    onChanged: (value) {
                      controller.update();
                    },
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(6),
                    ],
                    cursorColor: BPColor.brand,
                    decoration: InputDecoration(
                      hintText: LocaleKeys.personal_basic_info_verification_code_PH.tr,
                      helperText: controller.codeHelperText,
                      helperStyle: const TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        color: BPColor.greyText,
                      ),
                      hintStyle: const TextStyle(
                        fontWeight: FontWeight.w400,
                        color: BPColor.greyText,
                        fontSize: 14,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        vertical: 0,
                        horizontal: 16,
                      ),
                      filled: true,
                      fillColor: BPColor.grey24,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: BPColor.grey2A),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: BPColor.grey2A),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: BPColor.brand),
                      ),
                    ),
                  ),
                const Expanded(child: SizedBox(height: 24)),
                if (controller.otpResponseModel != null)
                  Builder(
                    builder: (context) {
                      bool expired = _.isExpired;

                      return RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          text: expired
                              ? "${LocaleKeys.otp_verify_page_didnt_get_the_code.tr} "
                              : LocaleKeys.otp_verify_page_resend_after.trParams({
                                  "time": _.remainingTime,
                                }),
                          style: const TextStyle(
                            color: BPColor.greyText,
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                          ),
                          children: [
                            if (expired)
                              TextSpan(
                                text: LocaleKeys.otp_verify_page_resend.tr,
                                style: const TextStyle(
                                  color: BPColor.whiteText,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                ),
                                recognizer: TapGestureRecognizer()..onTap = () => controller.onResend(),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                const SizedBox(height: 24),
                PrimaryButton(
                  onPressed: _.codeController.text.length == 6 ? _.onSubmit : null,
                  child: Text(
                    LocaleKeys.common_confirm.tr,
                  ),
                ),
              ],
            ).paddingAll(24),
          ),
        );
      },
    );
  }
}
