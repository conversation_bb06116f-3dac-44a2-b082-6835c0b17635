import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../shared/components/BPBaseInfoInputFiled.dart';
import '../../../../shared/components/BPGetView.dart';
import '../../../../../generated/locales.g.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../BottomActionButton.dart';
import '../controllers/hobbies_controller.dart';

class HobbiesView extends GetView<HobbiesController> {
  const HobbiesView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HobbiesController>(
      builder: (controller) => Scaffold(
        backgroundColor: BPColor.background,
        appBar: createAppBar(
          title: LocaleKeys.submit_photos_page_title.tr,
          backgroundColor: BPColor.background,
          foregroundColor: BPColor.whiteText,
        ),
        body: SafeArea(
          child: CustomScrollView(
            slivers: [
              // SliverAppBar(
              //   leading: IconButton(
              //     icon: const Icon(Icons.arrow_back_ios_new_rounded, color: Colors.black),
              //     onPressed: () {
              //       Get.back();
              //     },
              //   ),
              //   centerTitle: true,
              //   scrolledUnderElevation: 0,
              //   title: SizedBox(
              //     height: 44,
              //     child: Text(LocaleKeys.submit_photos_page_title.tr,
              //         style: const TextStyle(
              //           fontWeight: FontWeight.w600,
              //           color: BPColor.black,
              //           height: 1,
              //         )),
              //   ),
              //   backgroundColor: const Color(0xFFF2F2FE),
              //   floating: false,
              //   pinned: true,
              //   expandedHeight: controller.maxHeaderHeight,
              //   flexibleSpace: FlexibleSpaceBar(
              //     background: Container(
              //         padding: const EdgeInsets.symmetric(horizontal: 24),
              //         child: Column(
              //           children: [
              //             const SizedBox(
              //               height: 50,
              //             ),
              //             Text(LocaleKeys.interesting_title.tr, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700, color: Colors.black)),
              //             const SizedBox(
              //               height: 16,
              //             ),
              //             Text(LocaleKeys.interesting_subtitle.tr, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: BPColor.lightBrandText)),
              //           ],
              //         )),
              //   ),
              //   bottom: PreferredSize(
              //     preferredSize: Size(double.infinity, controller.calFixedBottomHeight),
              //     child: Padding(
              //       padding: const EdgeInsets.only(left: 24, right: 24, bottom: 24),
              //       child: Column(mainAxisAlignment: MainAxisAlignment.start, children: [
              //         Container(
              //             constraints: BoxConstraints(minHeight: controller.selectedItems.isNotEmpty ? 110 : 0),
              //             width: double.infinity,
              //             padding: const EdgeInsets.only(bottom: 6),
              //             decoration: const BoxDecoration(color: Color(0xFFF2F2FE)),
              //             child: Wrap(
              //               spacing: 16,
              //               runSpacing: 16,
              //               alignment: WrapAlignment.start,
              //               children: [
              //                 ...controller.selectedItems
              //                     .map((e) => Chip(
              //                           side: BorderSide.none,
              //                           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40)),
              //                           materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              //                           labelStyle: const TextStyle(
              //                             fontSize: 14,
              //                             color: Colors.white,
              //                             fontWeight: FontWeight.w500,
              //                           ),
              //                           backgroundColor: BPColor.brand,
              //                           deleteIconColor: Colors.white,
              //                           label: Text(e.name ?? ''),
              //                           deleteIcon: const Icon(Icons.close_rounded, size: 14),
              //                           onDeleted: () {
              //                             controller.removeSelectedItem(e);
              //                           },
              //                         ))
              //                     .toList(),
              //               ],
              //             )),
              //         Focus(
              //           onFocusChange: (value) {
              //             controller.isFocus.value = value;
              //           },
              //           child: BPBaseInfoInputFiled(
              //               title: LocaleKeys.interesting_input_tip.tr,
              //               controller: controller.interestInputController,
              //               onSubmitted: (value) {
              //                 controller.createLocaleInterest(value);
              //               }),
              //         )
              //       ]),
              //     ),
              //   ),
              // ),

              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate(
                    [
                      Text(
                        LocaleKeys.interesting_title.tr,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: BPColor.whiteText,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        LocaleKeys.interesting_subtitle.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: BPColor.greyText,
                        ),
                      ),
                      Wrap(
                        spacing: 16,
                        runSpacing: 16,
                        alignment: WrapAlignment.start,
                        children: [
                          ...controller.selectedItems.map(
                            (e) => Chip(
                              side: BorderSide.none,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40)),
                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              labelStyle: const TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                              backgroundColor: BPColor.brand,
                              deleteIconColor: Colors.white,
                              label: Text(e.name ?? ''),
                              deleteIcon: const Icon(Icons.close_rounded, size: 14),
                              onDeleted: () {
                                controller.removeSelectedItem(e);
                              },
                            ),
                          ),
                        ],
                      ).paddingSymmetric(vertical: controller.selectedItems.isNotEmpty ? 16 : 0),
                      if (controller.selectedItems.isEmpty) const SizedBox(height: 16),
                      Focus(
                        onFocusChange: (value) {
                          controller.isFocus.value = value;
                        },
                        child: BPBaseInfoInputFiled(
                          borderColor: BPColor.grey2A,
                          borderEnable: true,
                          backgroundColor: BPColor.grey24,
                          textColor: BPColor.whiteText,
                          titleStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColor.whiteText,
                          ),
                          title: LocaleKeys.interesting_input_tip.tr,
                          controller: controller.interestInputController,
                          onSubmitted: (value) {
                            controller.createLocaleInterest(value);
                          },
                          maxLength: 30,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverList.builder(
                itemCount: controller.interests.length,
                itemBuilder: (context, index) {
                  final item = controller.interests[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: Text(
                            item.name,
                            textAlign: TextAlign.left,
                            style: const TextStyle(color: BPColor.whiteText, fontSize: 16, fontWeight: FontWeight.w500),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          // alignment: WrapAlignment.start,
                          children: [
                            ...item.interests.map(
                              (e) => GestureDetector(
                                onTap: () {
                                  controller.toggle(e);
                                },
                                child: Chip(
                                  side: BorderSide.none,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40)),
                                  label: Text(e.name ?? ''),
                                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                  backgroundColor: controller.selectedItems.contains(e) ? BPColor.brand : BPColor.grey2A,
                                  labelStyle: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: controller.selectedItems.contains(e) ? FontWeight.w700 : FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (index == controller.interests.length - 1) const SizedBox(height: 100),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: Obx(() => controller.isFocus.value ? Container() : BottomActionButton(backgroundColor: controller.isValid ? BPColor.brand : BPColor.grey2A, title: controller.fromEdit ? LocaleKeys.common_done.tr : LocaleKeys.common_next.tr, onPressed: controller.submit)),
      ),
    );
  }
}
