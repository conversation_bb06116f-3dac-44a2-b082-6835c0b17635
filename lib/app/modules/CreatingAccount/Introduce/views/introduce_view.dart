import 'package:flutter/material.dart';

import 'package:get/get.dart';
import '../../../../shared/components/BPGetView.dart';
import '../../../../../generated/locales.g.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../../shared/components/BPImageUtil.dart';
import '../../BottomActionButton.dart';
import '../controllers/introduce_controller.dart';

class IntroduceView extends GetView<IntroduceController> {
  const IntroduceView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<IntroduceController>(
      builder: (controller) => Scaffold(
        backgroundColor: BPColor.background,
        appBar: createAppBar(
          title: LocaleKeys.submit_photos_page_title.tr,
          backgroundColor: BPColor.background,
          foregroundColor: BPColor.whiteText,
        ),
        body: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.only(left: 25, right: 25),
                  child: SingleChildScrollView(
                    keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        Text(LocaleKeys.introduce_title.tr, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w700, color: BPColor.whiteText)),
                        const SizedBox(
                          height: 20,
                        ),
                        Text(LocaleKeys.introduce_subtitle.tr, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: BPColor.greyText)),
                        const SizedBox(height: 30),
                        Container(
                          height: 350,
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                            color: BPColor.grey24,
                            border: Border.all(color: BPColor.grey2A, width: 1),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(15),
                            child: TextField(
                              controller: controller.introduceTEController,
                              keyboardType: TextInputType.multiline,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: BPColor.whiteText,
                              ),
                              // textInputAction: TextInputAction.done,
                              maxLength: 1000,
                              maxLines: null,
                              // onTap: toAddNoteCallback,
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                counterText: '',
                                hintText: LocaleKeys.introduce_input_PH.tr,
                                hintStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: BPColor.greyText,
                                ),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20.0),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(padding: const EdgeInsets.only(top: 6), child: SizedBox(width: 4, height: 4, child: BPImageUtil.image('redPoint.png', color: BPColor.grayText))),
                            const SizedBox(width: 7),
                            Expanded(
                              child: Text(
                                LocaleKeys.introduce_note1.tr,
                                textAlign: TextAlign.left,
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: BPColor.grayText,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 5),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(padding: const EdgeInsets.only(top: 6), child: SizedBox(width: 4, height: 4, child: BPImageUtil.image('redPoint.png', color: BPColor.grayText))),
                            const SizedBox(width: 7),
                            Expanded(
                              child: Text(
                                LocaleKeys.introduce_note2.tr,
                                textAlign: TextAlign.left,
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: BPColor.grayText,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              BottomActionButton(backgroundColor: controller.isValid ? BPColor.brand : BPColor.grey2A, title: controller.fromEdit ? LocaleKeys.common_done.tr : LocaleKeys.common_preview.tr, onPressed: controller.submit),
            ],
          ),
        ),
      ),
    );
  }
}
