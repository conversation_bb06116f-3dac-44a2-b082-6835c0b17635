import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'comment_input.dart';
import '../../CommunityFeed/Widgets/community_post_card.dart';
import '../../CommunityProfile/views/community_comment.dart';
import '../../../../shared/account/BPSessionManager.dart';
import '../../../../shared/components/BPBottomSheet.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../../shared/components/BPImageUtil.dart';
import '../../../../shared/components/BPTextButton.dart';
import '../../../../shared/components/touch_close_keyboard.dart';
import '../../../../../generated/locales.g.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:uuid/uuid.dart';

import '../controllers/community_detail_controller.dart';
import '../widgets/MentionItem.dart';

class CommunityDetailView extends GetView<CommunityDetailController> {
  const CommunityDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder<CommunityDetailController>(
      init: CommunityDetailController(),
      tag: const Uuid().v4(),
      builder: (controller) {
        final post = controller.post;
        final userId = BPSessionManager.accountModelConvenience?.id ?? 0;
        return Scaffold(
          backgroundColor: BPColor.background,
          appBar: AppBar(
            shadowColor: const Color(0x00000000),
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.arrow_back_rounded,
                size: 30,
                color: Colors.white,
              ),
            ),
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.white,
            titleSpacing: 0,
            surfaceTintColor: Colors.transparent,
            actions: [
              InkWell(
                onTap: () {
                  final mine = (BPSessionManager.accountModelConvenience?.id ?? 0) == post?.authorUser?.id;
                  BPBottomSheet.show(
                    items: mine ? [LocaleKeys.common_edit.tr, LocaleKeys.community_delete.tr] : [LocaleKeys.common_report.tr],
                    onCompletion: (index) {
                      if (mine) {
                        if (index == 0) {
                          controller.editPost();
                        } else {
                          controller.deletePost();
                        }
                      } else {
                        controller.reportPost();
                      }
                    },
                  );
                },
                child: const SizedBox(width: 50, height: 48, child: Icon(Icons.more_horiz_rounded, size: 30, color: BPColor.grayText)),
              ),
            ],
          ),
          body: post == null || (post.removed ?? false)
              ? Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  width: double.infinity,
                  height: 400,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [BPImageUtil.svgImage('community_post_removed.svg'), const SizedBox(height: 24), Text(LocaleKeys.community_post_empty.tr, style: const TextStyle(color: BPColor.grayText))],
                  ),
                )
              : TouchCloseSoftKeyboard(
                  onTouch: () {
                    controller.isShowMentionList.value = false;
                  },
                  child: Column(
                    children: [
                      Expanded(
                        child: SmartRefresher(
                          header: const MaterialClassicHeader(
                            backgroundColor: BPColor.brand,
                            color: Colors.white,
                          ),
                          controller: controller.refreshCommentsController,
                          enablePullUp: controller.canLoadMoreComments,
                          scrollController: controller.scrollController,
                          enablePullDown: false,
                          onLoading: controller.loadMoreComments,
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                  child: CommunityPostUserBar(user: post.authorUser, dateTime: post.createdAt ?? DateTime.now(), isMe: post.authorUser?.id == userId),
                                ),
                                CommunityPostCard(
                                  current: post,
                                  backgroundColor: Colors.transparent,
                                  displayAuthor: false,
                                  displayToolbar: false,
                                  displayMeIfNeed: false,
                                  displayComments: false,
                                  padding: const EdgeInsets.only(left: 15, right: 15, bottom: 15),
                                  profileAction: () {},
                                  likeAction: () {},
                                  moreAction: () {},
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  color: Colors.transparent,
                                  padding: const EdgeInsets.symmetric(horizontal: 20),
                                  child: Column(
                                    children: [
                                      // const SizedBox(height: 10),
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              controller.handlePostLike();
                                            },
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                SizedBox(
                                                  width: 25,
                                                  height: 25,
                                                  child: (post.data?.liked ?? false) ? BPImageUtil.scSvgImage('thumbs_hl.svg') : BPImageUtil.scSvgImage('thumbs.svg'),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  (post.data?.likeCount ?? 0).toString(),
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                    color: BPColor.whiteText,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          BPTextButton(title: LocaleKeys.community_post_see_likes.tr, color: BPColor.whiteText, onPressed: controller.seeAllLikes),
                                        ],
                                      ),
                                      const SizedBox(height: 25),
                                      Row(
                                        children: [
                                          Text(
                                            LocaleKeys.community_post_all_comments.tr + ' ${(controller.post?.data?.commentCount ?? 0)}'.toString(),
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: BPColor.whiteText,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                      if (controller.comments.isEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(top: 20, bottom: 30),
                                          child: Text(
                                            LocaleKeys.community_comments_list_empty.tr,
                                            textAlign: TextAlign.center,
                                            style: const TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.w400),
                                          ),
                                        ),
                                      if (controller.comments.isNotEmpty) ...[
                                        ...controller.comments.map((item) {
                                          return Padding(
                                            padding: const EdgeInsets.only(left: 0, right: 0, top: 20, bottom: 0),
                                            child: CommunityComment(
                                              current: item,
                                              isSimpleStyle: true,
                                              likeAction: () {
                                                controller.handleCommentLike(comment: item);
                                              },
                                              moreAction: () {
                                                final mine = (BPSessionManager.accountModelConvenience?.id ?? 0) == item.authorUser?.id;
                                                BPBottomSheet.show(
                                                  items: [mine ? LocaleKeys.community_delete.tr : LocaleKeys.common_report.tr],
                                                  onCompletion: (index) {
                                                    if (mine) {
                                                      controller.deleteComment(item.id);
                                                    } else {
                                                      controller.reportComment(item.id);
                                                    }
                                                  },
                                                );
                                              },
                                              profileAction: () {},
                                            ),
                                          );
                                        }),
                                        const SizedBox(height: 10),
                                        if (!controller.canLoadMoreComments)
                                          Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 20),
                                            child: Text(
                                              LocaleKeys.community_comment_end_text.tr,
                                              style: const TextStyle(fontSize: 16, color: BPColor.grayText, fontWeight: FontWeight.w400),
                                            ),
                                          ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Obx(
                        () => controller.isShowMentionList.value ? const Divider(height: 1) : const SizedBox(),
                      ),
                      Column(
                        children: [
                          Obx(
                            () => controller.isShowMentionList.value
                                ? Container(
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      border: Border(
                                        bottom: BorderSide(
                                          width: 1,
                                          color: BPColor.lightBG,
                                        ),
                                      ),
                                    ),
                                    padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
                                    width: Get.width,
                                    height: 112,
                                    child: controller.mentionList.isEmpty && controller.mentionSearchText.value.isNotEmpty
                                        ? MentionsEmpty(searchText: controller.mentionSearchText.value)
                                        : ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            itemCount: controller.isFetchMentionSuggestions.value ? 5 : controller.mentionList.length,
                                            itemBuilder: (BuildContext context, int index) {
                                              return controller.isFetchMentionSuggestions.value
                                                  ? Skeletonizer.zone(
                                                      child: Container(
                                                        margin: const EdgeInsets.only(right: 10),
                                                        width: 72,
                                                        height: 68,
                                                        child: const Column(
                                                          mainAxisSize: MainAxisSize.min,
                                                          children: [
                                                            Bone.circle(size: 46),
                                                            SizedBox(height: 4),
                                                            Bone.text(
                                                              width: 55,
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    )
                                                  : Container(
                                                      margin: const EdgeInsets.only(right: 10),
                                                      width: 72,
                                                      height: 80,
                                                      child: MentionItem(
                                                        isChecked: controller.selectedMentionList.contains(controller.mentionList[index]),
                                                        fakeName: controller.mentionList[index].fakeName ?? '',
                                                        fakeAvatarUrl: controller.mentionList[index].fakeAvatar?.url,
                                                        onTap: () {
                                                          controller.onTapMentionItem(controller.mentionList[index]);
                                                        },
                                                      ),
                                                    );
                                            },
                                          ),
                                  )
                                : const SizedBox(),
                          ),
                          Container(
                            color: const Color(0xff333333),
                            padding: const EdgeInsets.only(top: 10, bottom: 100, left: 12, right: 12),
                            child: BPCommentInput(
                              allAtList: controller.selectedMentionList,
                              hintText: LocaleKeys.community_comment_placeholder.tr,
                              maxLength: 10000,
                              infoTEController: controller.tEController,
                              onClickSend: controller.sendComment,
                              onEditingComplete: () {},
                              onSubmitted: (text) {},
                              sendBTNEnable: true,
                              readOnly: false,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}

class MentionsEmpty extends StatelessWidget {
  final String searchText;
  const MentionsEmpty({
    super.key,
    required this.searchText,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        style: const TextStyle(height: 1.5, fontSize: 14, color: BPColor.tipLightGray),
        children: [
          TextSpan(text: LocaleKeys.community_comment_mention_empty_1.tr),
          TextSpan(text: searchText, style: const TextStyle(fontWeight: FontWeight.w600, color: BPColor.black)),
          TextSpan(text: '\n${LocaleKeys.community_comment_mention_empty_2.tr}'),
        ],
      ),
    );
  }
}
