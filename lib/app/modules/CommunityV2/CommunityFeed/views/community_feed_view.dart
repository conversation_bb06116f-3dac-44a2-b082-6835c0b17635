import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:starchex/app/shared/components/mv_text.dart';
import 'package:starchex/app/shared/components/mv_text_button.dart';
import '../../../../data/BPCommunityModels.dart';
import '../../Components/CommunityAvatar.dart';
import '../Widgets/community_ads_card.dart';
import '../Widgets/community_post_card.dart';
import '../../CommunitySignUp/views/community_sign_up_view.dart';
import '../../../../routes/app_pages.dart';
import '../../../../shared/account/BPSessionManager.dart';
import '../../../../shared/components/BPBottomSheet.dart';
import '../../../../shared/components/BPColorUtil.dart';
import '../../../../shared/components/BPImageUtil.dart';
import '../../../../../generated/locales.g.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';

import '../controllers/community_feed_controller.dart';

class CommunityFeedView extends GetView<CommunityFeedController> {
  const CommunityFeedView({super.key});
  @override
  Widget build(BuildContext context) {
    return GetBuilder<CommunityFeedController>(
      init: CommunityFeedController(),
      tag: const Uuid().v4(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: BPColor.background,
          appBar: AppBar(
            leadingWidth: 200,
            shadowColor: const Color(0x00000000),
            backgroundColor: Colors.transparent,
            leading: Padding(
              padding: const EdgeInsets.only(left: 20, top: 15),
              child: Text(
                LocaleKeys.community_title.tr,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
            ),
            actions: [
              if (controller.fakeName.isNotEmpty)
                CommunityAvatar(
                  fakeName: controller.fakeName,
                  fakeAvatarUrl: controller.fakeAvatarUrl,
                  width: 48,
                  height: 48,
                  heroTagSuffix: 'feed_header',
                  onTap: () {
                    controller.toMyCommunity();
                  },
                ),
              const SizedBox(width: 20),
            ],
          ),
          body: controller.hasRegistered
              ? mainPosts(controller)
              : SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: CommunitySignUpView(isEmbed: true, completion: controller.communityRegistered),
                  ),
                ),
          floatingActionButton: controller.hasRegistered
              ? Padding(
                  padding: const EdgeInsets.only(bottom: 100),
                  child: ElevatedButton(
                    onPressed: controller.toCreatePost,
                    style: ElevatedButton.styleFrom(
                      shape: const CircleBorder(),
                      padding: const EdgeInsets.all(0),
                      backgroundColor: BPColor.gold,
                    ),
                    child: const Icon(Icons.add, color: BPColor.whiteText),
                  ),
                )
              : const SizedBox(),
        );
      },
    );
  }

  Container mainPosts(CommunityFeedController controller) {
    return Container(
      padding: const EdgeInsets.only(left: 0, right: 0, top: 20, bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (controller.tabController != null) ...[
            Container(
              color: Colors.transparent,
              height: 45,
              child: TabBar(
                indicatorColor: Colors.transparent,
                padding: const EdgeInsets.all(0),
                indicatorWeight: 0.01,
                dividerHeight: 0,
                tabAlignment: TabAlignment.start,
                indicatorSize: TabBarIndicatorSize.label,
                labelColor: Colors.black,
                labelPadding: const EdgeInsets.symmetric(horizontal: 6),
                unselectedLabelColor: const Color(0xFF9EA3AE),
                labelStyle: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
                unselectedLabelStyle: const TextStyle(fontSize: 15, fontWeight: FontWeight.w400),
                controller: controller.tabController,
                isScrollable: true,
                tabs: controller.tabTitles.asMap().entries.map(
                  (item) {
                    return Tab(
                      child: Obx(
                        () {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 5),
                            child: MVTextButton(
                              title: item.value,
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              borderRadius: 20,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                              textColor: controller.selectedTabIndex.value == item.key ? BPColor.background : BPColor.grayText,
                              backgroundColor: controller.selectedTabIndex.value == item.key ? BPColor.whiteText : Colors.white.withOpacity(0.15),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ).toList(),
              ),
            ),
            const SizedBox(height: 15),
            Expanded(
              child: TabBarView(
                controller: controller.tabController,
                children: controller.boardsAccessList.map(
                  (e) {
                    Widget emptyWidget() {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [BPImageUtil.svgImage('community_post_empty.svg'), const SizedBox(height: 24), Text(LocaleKeys.community_post_list_empty.tr, textAlign: TextAlign.center, style: const TextStyle(color: BPColor.grayText))],
                      );
                    }

                    Widget postListWidget(List<BPCommunityPost> items) {
                      return ListView.separated(
                        separatorBuilder: (context, index) => const SizedBox(height: 12),
                        itemCount: items.length + 1, // Add 1 for the bottom SizedBox
                        itemBuilder: (BuildContext context, int index) {
                          // Add SizedBox at the bottom
                          if (index == items.length) {
                            return const SizedBox(height: 160);
                          }
                          
                          final item = items[index];

                          // If the post is an ads
                          if (item.board?.id == 139 && false) {
                            return InkWell(
                              onTap: () async {
                                controller.recordAdsClick(id: item.id);

                                Uri url = Uri.parse(item.adUrl ?? '');
                                if (!await launchUrl(url)) {
                                  throw Exception('Could not launch $url');
                                }
                              },
                              child: CommunityAdsCard(
                                post: item,
                                profileAction: () => controller.toAuthor(item.authorUser?.id ?? 0),
                                likeAction: () => controller.handlePostLike(post: item),
                              ),
                            );
                          } else {
                            return InkWell(
                              child: CommunityPostCard(
                                current: item,
                                profileAction: () {
                                  controller.toAuthor(item.authorUser?.id ?? 0);
                                },
                                likeAction: () {
                                  controller.handlePostLike(post: item);
                                },
                                moreAction: () {
                                  final mine = (BPSessionManager.accountModelConvenience?.id ?? 0) == item.authorUser?.id;
                                  BPBottomSheet.show(
                                    items: mine ? [LocaleKeys.common_edit.tr, LocaleKeys.community_delete.tr] : [LocaleKeys.common_report.tr],
                                    onCompletion: (index) {
                                      if (mine) {
                                        if (index == 0) {
                                          controller.editPost(item);
                                        } else {
                                          controller.deletePost(item.id);
                                        }
                                      } else {
                                        controller.reportPost(item.id);
                                      }
                                    },
                                  );
                                },
                              ),
                              onTap: () {
                                Get.toNamed(Routes.COMMUNITY_DETAIL, arguments: item, parameters: {"postID": item.id.toString()});
                              },
                            );
                          }
                        },
                      );
                    }

                    return (e.id != controller.currentBorderId)
                        ? emptyWidget()
                        : SmartRefresher(
                            header: const MaterialClassicHeader(
                              backgroundColor: BPColor.gold,
                              color: Colors.white,
                            ),
                            controller: controller.refreshController,
                            onRefresh: controller.onRefresh,
                            enablePullUp: true,
                            enablePullDown: true,
                            onLoading: controller.getPostList,
                            child: (controller.posts.isEmpty && !controller.isLoadingPosts ? emptyWidget() : postListWidget(controller.posts)),
                          );
                  },
                ).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
