// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars
// ignore: avoid_classes_with_only_static_members
class AppTranslation {
  static Map<String, Map<String, String>> translations = {
    'en': Locales.en,
    'ko': Locales.ko,
  };
}

class LocaleKeys {
  LocaleKeys._();
  static const sc_str_common_view = 'sc_str_common_view';
  static const sc_str_common_view_all = 'sc_str_common_view_all';
  static const sc_str_common_members = 'sc_str_common_members';
  static const sc_str_common_events = 'sc_str_common_events';
  static const sc_str_common_comments = 'sc_str_common_comments';
  static const sc_str_common_comment_placeholder =
      'sc_str_common_comment_placeholder';
  static const sc_str_common_post = 'sc_str_common_post';
  static const sc_str_common_upload = 'sc_str_common_upload';
  static const sc_str_common_continue = 'sc_str_common_continue';
  static const sc_str_common_publish = 'sc_str_common_publish';
  static const sc_str_common_image = 'sc_str_common_image';
  static const sc_str_event_intro_recommended =
      'sc_str_event_intro_recommended';
  static const sc_str_home_this_week = 'sc_str_home_this_week';
  static const sc_str_home_last_week = 'sc_str_home_last_week';
  static const sc_str_home_upcoming = 'sc_str_home_upcoming';
  static const sc_str_home_weekly_star = 'sc_str_home_weekly_star';
  static const sc_str_home_featured_events = 'sc_str_home_featured_events';
  static const sc_str_home_notification_content =
      'sc_str_home_notification_content';
  static const sc_str_home_last_week_event_title =
      'sc_str_home_last_week_event_title';
  static const sc_str_home_next_week_star_title =
      'sc_str_home_next_week_star_title';
  static const sc_str_home_next_week_star_subtitle =
      'sc_str_home_next_week_star_subtitle';
  static const sc_str_home_look_forward = 'sc_str_home_look_forward';
  static const sc_str_home_member_suffix = 'sc_str_home_member_suffix';
  static const sc_str_home_interview_article = 'sc_str_home_interview_article';
  static const sc_str_comment_Write_comment = 'sc_str_comment_Write_comment';
  static const sc_str_comment_your_thought = 'sc_str_comment_your_thought';
  static const sc_str_comment_all_comments = 'sc_str_comment_all_comments';
  static const sc_str_comment_add_comment = 'sc_str_comment_add_comment';
  static const sc_str_comment_interview_comment_placeholder =
      'sc_str_comment_interview_comment_placeholder';
  static const sc_str_event_capacity_notice = 'sc_str_event_capacity_notice';
  static const sc_str_event_event_introduction =
      'sc_str_event_event_introduction';
  static const sc_str_event_join_application = 'sc_str_event_join_application';
  static const sc_str_event_application_completed =
      'sc_str_event_application_completed';
  static const sc_str_event_congratulations_selected =
      'sc_str_event_congratulations_selected';
  static const sc_str_event_not_selected_message =
      'sc_str_event_not_selected_message';
  static const sc_str_event_ended = 'sc_str_event_ended';
  static const sc_str_event_confirmed_participants =
      'sc_str_event_confirmed_participants';
  static const sc_str_event_past_interviews = 'sc_str_event_past_interviews';
  static const sc_str_event_interview = 'sc_str_event_interview';
  static const sc_str_event_address = 'sc_str_event_address';
  static const sc_str_event_private_club_address =
      'sc_str_event_private_club_address';
  static const sc_str_community_register_tip = 'sc_str_community_register_tip';
  static const sc_str_community_name_input_PH =
      'sc_str_community_name_input_PH';
  static const sc_str_community_name_already_taken =
      'sc_str_community_name_already_taken';
  static const sc_str_community_name_select_tip =
      'sc_str_community_name_select_tip';
  static const sc_str_community_enter_circle = 'sc_str_community_enter_circle';
  static const sc_str_community_fake_name_existed_error =
      'sc_str_community_fake_name_existed_error';
  static const sc_str_community_craft_your_post =
      'sc_str_community_craft_your_post';
  static const sc_str_community_select_a_circle =
      'sc_str_community_select_a_circle';
  static const sc_str_community_share_your_insight =
      'sc_str_community_share_your_insight';
  static const sc_str_community_media_upload_tip =
      'sc_str_community_media_upload_tip';
  static const sc_str_ai_avatar_generating = 'sc_str_ai_avatar_generating';
  static const sc_str_ai_avatar_wait_time = 'sc_str_ai_avatar_wait_time';
  static const sc_str_ai_avatar_generated = 'sc_str_ai_avatar_generated';
  static const sc_str_ai_avatar_select_again = 'sc_str_ai_avatar_select_again';
  static const sc_str_ai_avatar_crop_avatar = 'sc_str_ai_avatar_crop_avatar';
  static const sc_str_ai_avatar_image_load_failed =
      'sc_str_ai_avatar_image_load_failed';
  static const sc_str_ai_avatar_image_data_error =
      'sc_str_ai_avatar_image_data_error';
  static const sc_str_ai_avatar_network_image_failed =
      'sc_str_ai_avatar_network_image_failed';
  static const sc_str_ai_avatar_no_image = 'sc_str_ai_avatar_no_image';
  static const sc_str_ai_avatar_render_error = 'sc_str_ai_avatar_render_error';
  static const sc_str_ai_avatar_completed = 'sc_str_ai_avatar_completed';
  static const sc_str_ai_avatar_generation_success =
      'sc_str_ai_avatar_generation_success';
  static const sc_str_ai_avatar_generation_failed =
      'sc_str_ai_avatar_generation_failed';
  static const sc_str_ai_avatar_generation_cancelled =
      'sc_str_ai_avatar_generation_cancelled';
  static const sc_str_ai_avatar_cancel_generation =
      'sc_str_ai_avatar_cancel_generation';
  static const sc_str_ai_avatar_cancel_generation_confirm =
      'sc_str_ai_avatar_cancel_generation_confirm';
  static const sc_str_ai_avatar_regenerate = 'sc_str_ai_avatar_regenerate';
  static const sc_str_ai_avatar_use_this_avatar =
      'sc_str_ai_avatar_use_this_avatar';
  static const sc_str_ai_avatar_set_success = 'sc_str_ai_avatar_set_success';
  static const sc_str_ai_avatar_generating_in_progress =
      'sc_str_ai_avatar_generating_in_progress';
  static const sc_str_ai_avatar_estimated_time_remaining =
      'sc_str_ai_avatar_estimated_time_remaining';
  static const sc_str_ai_avatar_task_restored =
      'sc_str_ai_avatar_task_restored';
  static const sc_str_ai_avatar_task_completed =
      'sc_str_ai_avatar_task_completed';
  static const not_verified_yet_warning_message =
      'not_verified_yet_warning_message';
  static const rejected = 'rejected';
  static const not_verified = 'not_verified';
  static const university = 'university';
  static const skip_invitation_message = 'skip_invitation_message';
  static const input_validation_required_error_message =
      'input_validation_required_error_message';
  static const badge_title = 'badge_title';
  static const badge_university_badges = 'badge_university_badges';
  static const badge_wealth_badges = 'badge_wealth_badges';
  static const badge_badge_policy = 'badge_badge_policy';
  static const badge_graduate = 'badge_graduate';
  static const badge_graduate_tip = 'badge_graduate_tip';
  static const badge_top_200 = 'badge_top_200';
  static const badge_top_200_tip = 'badge_top_200_tip';
  static const badge_more_than_500k = 'badge_more_than_500k';
  static const badge_more_than_500k_tip = 'badge_more_than_500k_tip';
  static const badge_more_than_1m = 'badge_more_than_1m';
  static const badge_more_than_1m_tip = 'badge_more_than_1m_tip';
  static const badge_more_than_10m = 'badge_more_than_10m';
  static const badge_more_than_10m_tip = 'badge_more_than_10m_tip';
  static const asset_verification_title = 'asset_verification_title';
  static const asset_verification_description =
      'asset_verification_description';
  static const asset_verification_real_estate =
      'asset_verification_real_estate';
  static const asset_verification_financial_assets =
      'asset_verification_financial_assets';
  static const asset_verification_crypto_currency =
      'asset_verification_crypto_currency';
  static const asset_verification_art_works_cars =
      'asset_verification_art_works_cars';
  static const asset_verification_guidance_title =
      'asset_verification_guidance_title';
  static const asset_verification_guidance_1 = 'asset_verification_guidance_1';
  static const asset_verification_guidance_2 = 'asset_verification_guidance_2';
  static const asset_verification_guidance_3 = 'asset_verification_guidance_3';
  static const asset_verification_next = 'asset_verification_next';
  static const asset_verification_upload_id_title =
      'asset_verification_upload_id_title';
  static const asset_verification_upload_id_description =
      'asset_verification_upload_id_description';
  static const asset_verification_id_card = 'asset_verification_id_card';
  static const asset_verification_file_format_info =
      'asset_verification_file_format_info';
  static const asset_verification_upload_file =
      'asset_verification_upload_file';
  static const asset_verification_id_submission_guidelines =
      'asset_verification_id_submission_guidelines';
  static const asset_verification_guideline_clear_image =
      'asset_verification_guideline_clear_image';
  static const asset_verification_guideline_include_original =
      'asset_verification_guideline_include_original';
  static const asset_verification_guideline_masking =
      'asset_verification_guideline_masking';
  static const asset_verification_guideline_updated_info =
      'asset_verification_guideline_updated_info';
  static const asset_verification_select_assets_title =
      'asset_verification_select_assets_title';
  static const asset_verification_select_assets_description =
      'asset_verification_select_assets_description';
  static const asset_verification_real_estate_desc =
      'asset_verification_real_estate_desc';
  static const asset_verification_financial_assets_desc =
      'asset_verification_financial_assets_desc';
  static const asset_verification_crypto_currency_desc =
      'asset_verification_crypto_currency_desc';
  static const asset_verification_other_assets =
      'asset_verification_other_assets';
  static const asset_verification_other_assets_desc =
      'asset_verification_other_assets_desc';
  static const asset_verification_list_of_added_assets =
      'asset_verification_list_of_added_assets';
  static const asset_verification_tap_to_add_asset =
      'asset_verification_tap_to_add_asset';
  static const asset_verification_remove_asset =
      'asset_verification_remove_asset';
  static const asset_verification_asset_name = 'asset_verification_asset_name';
  static const asset_verification_document_of_proof =
      'asset_verification_document_of_proof';
  static const asset_verification_selected_files =
      'asset_verification_selected_files';
  static const asset_verification_description_optional =
      'asset_verification_description_optional';
  static const asset_verification_description_placeholder =
      'asset_verification_description_placeholder';
  static const asset_verification_document_upload_guidelines =
      'asset_verification_document_upload_guidelines';
  static const asset_verification_real_estate_name_placeholder =
      'asset_verification_real_estate_name_placeholder';
  static const asset_verification_financial_assets_name_placeholder =
      'asset_verification_financial_assets_name_placeholder';
  static const asset_verification_crypto_currency_name_placeholder =
      'asset_verification_crypto_currency_name_placeholder';
  static const asset_verification_other_assets_name_placeholder =
      'asset_verification_other_assets_name_placeholder';
  static const asset_verification_real_estate_document_desc =
      'asset_verification_real_estate_document_desc';
  static const asset_verification_financial_assets_document_desc =
      'asset_verification_financial_assets_document_desc';
  static const asset_verification_crypto_currency_document_desc =
      'asset_verification_crypto_currency_document_desc';
  static const asset_verification_other_assets_document_desc =
      'asset_verification_other_assets_document_desc';
  static const asset_verification_real_estate_guideline_1 =
      'asset_verification_real_estate_guideline_1';
  static const asset_verification_real_estate_guideline_2 =
      'asset_verification_real_estate_guideline_2';
  static const asset_verification_real_estate_guideline_3 =
      'asset_verification_real_estate_guideline_3';
  static const asset_verification_financial_assets_guideline_1 =
      'asset_verification_financial_assets_guideline_1';
  static const asset_verification_financial_assets_guideline_2 =
      'asset_verification_financial_assets_guideline_2';
  static const asset_verification_financial_assets_guideline_3 =
      'asset_verification_financial_assets_guideline_3';
  static const asset_verification_crypto_currency_guideline_1 =
      'asset_verification_crypto_currency_guideline_1';
  static const asset_verification_crypto_currency_guideline_2 =
      'asset_verification_crypto_currency_guideline_2';
  static const asset_verification_crypto_currency_guideline_3 =
      'asset_verification_crypto_currency_guideline_3';
  static const asset_verification_other_assets_guideline_1 =
      'asset_verification_other_assets_guideline_1';
  static const asset_verification_other_assets_guideline_2 =
      'asset_verification_other_assets_guideline_2';
  static const asset_verification_other_assets_guideline_3 =
      'asset_verification_other_assets_guideline_3';
  static const asset_verification_common_guideline_ownership =
      'asset_verification_common_guideline_ownership';
  static const asset_verification_document_upload_guidelines_title =
      'asset_verification_document_upload_guidelines_title';
  static const asset_verification_denial_reasons_title =
      'asset_verification_denial_reasons_title';
  static const asset_verification_denial_reason_info_inconsistency =
      'asset_verification_denial_reason_info_inconsistency';
  static const asset_verification_denial_reason_title_inconsistency =
      'asset_verification_denial_reason_title_inconsistency';
  static const asset_verification_denial_reason_proof_ownership =
      'asset_verification_denial_reason_proof_ownership';
  static const asset_verification_real_estate_denial_unregistered =
      'asset_verification_real_estate_denial_unregistered';
  static const asset_verification_real_estate_denial_disputes =
      'asset_verification_real_estate_denial_disputes';
  static const asset_verification_real_estate_denial_other_names =
      'asset_verification_real_estate_denial_other_names';
  static const asset_verification_real_estate_denial_forgery =
      'asset_verification_real_estate_denial_forgery';
  static const asset_verification_financial_denial_uncertain_maturity =
      'asset_verification_financial_denial_uncertain_maturity';
  static const asset_verification_financial_denial_high_risk =
      'asset_verification_financial_denial_high_risk';
  static const asset_verification_crypto_denial_unregistered_exchange =
      'asset_verification_crypto_denial_unregistered_exchange';
  static const asset_verification_crypto_denial_low_liquidity =
      'asset_verification_crypto_denial_low_liquidity';
  static const asset_verification_crypto_denial_extreme_volatility =
      'asset_verification_crypto_denial_extreme_volatility';
  static const asset_verification_other_denial_market_uncertainty =
      'asset_verification_other_denial_market_uncertainty';
  static const asset_verification_other_denial_personal_agreement =
      'asset_verification_other_denial_personal_agreement';
  static const asset_verification_other_denial_unclear_ownership =
      'asset_verification_other_denial_unclear_ownership';
  static const asset_verification_error_no_file_selected =
      'asset_verification_error_no_file_selected';
  static const asset_verification_error_no_file_selected_desc =
      'asset_verification_error_no_file_selected_desc';
  static const asset_verification_success_assets_submitted =
      'asset_verification_success_assets_submitted';
  static const asset_verification_success_assets_submitted_desc =
      'asset_verification_success_assets_submitted_desc';
  static const asset_verification_error_no_assets_added =
      'asset_verification_error_no_assets_added';
  static const asset_verification_error_no_assets_added_desc =
      'asset_verification_error_no_assets_added_desc';
  static const asset_verification_success_file_selected =
      'asset_verification_success_file_selected';
  static const asset_verification_success_file_selected_desc =
      'asset_verification_success_file_selected_desc';
  static const asset_verification_error_file_selection =
      'asset_verification_error_file_selection';
  static const asset_verification_error_file_selection_desc =
      'asset_verification_error_file_selection_desc';
  static const asset_verification_success_files_selected =
      'asset_verification_success_files_selected';
  static const asset_verification_success_files_selected_desc =
      'asset_verification_success_files_selected_desc';
  static const asset_verification_success_files_added_desc =
      'asset_verification_success_files_added_desc';
  static const asset_verification_info_files_already_exist =
      'asset_verification_info_files_already_exist';
  static const asset_verification_info_files_already_exist_desc =
      'asset_verification_info_files_already_exist_desc';
  static const asset_verification_error_files_selection_desc =
      'asset_verification_error_files_selection_desc';
  static const asset_verification_error_asset_name_required =
      'asset_verification_error_asset_name_required';
  static const asset_verification_error_file_required =
      'asset_verification_error_file_required';
  static const asset_verification_success_asset_added =
      'asset_verification_success_asset_added';
  static const asset_verification_success_asset_added_desc =
      'asset_verification_success_asset_added_desc';
  static const asset_verification_success_asset_removed =
      'asset_verification_success_asset_removed';
  static const asset_verification_success_asset_removed_desc =
      'asset_verification_success_asset_removed_desc';
  static const asset_verification_asset_type_real_estate =
      'asset_verification_asset_type_real_estate';
  static const asset_verification_asset_type_financial_assets =
      'asset_verification_asset_type_financial_assets';
  static const asset_verification_asset_type_crypto_currency =
      'asset_verification_asset_type_crypto_currency';
  static const asset_verification_asset_type_other_assets =
      'asset_verification_asset_type_other_assets';
  static const asset_verification_default_asset_name_real_estate =
      'asset_verification_default_asset_name_real_estate';
  static const asset_verification_default_asset_name_financial =
      'asset_verification_default_asset_name_financial';
  static const asset_verification_default_asset_name_crypto =
      'asset_verification_default_asset_name_crypto';
  static const asset_verification_default_asset_name_other =
      'asset_verification_default_asset_name_other';
  static const asset_verification_add_asset = 'asset_verification_add_asset';
  static const asset_verification_add_new_asset =
      'asset_verification_add_new_asset';
  static const asset_verification_new_asset = 'asset_verification_new_asset';
  static const asset_verification_form_complete =
      'asset_verification_form_complete';
  static const asset_verification_form_incomplete =
      'asset_verification_form_incomplete';
  static const asset_verification_error_no_valid_assets =
      'asset_verification_error_no_valid_assets';
  static const asset_verification_error_no_valid_assets_desc =
      'asset_verification_error_no_valid_assets_desc';
  static const asset_verification_success_assets_saved =
      'asset_verification_success_assets_saved';
  static const asset_verification_success_assets_saved_desc =
      'asset_verification_success_assets_saved_desc';
  static const asset_verification_uploading_assets =
      'asset_verification_uploading_assets';
  static const asset_verification_uploading_file =
      'asset_verification_uploading_file';
  static const asset_verification_submitting_assets =
      'asset_verification_submitting_assets';
  static const asset_verification_confirming_assets =
      'asset_verification_confirming_assets';
  static const asset_verification_add_real_estate =
      'asset_verification_add_real_estate';
  static const asset_verification_add_financial_asset =
      'asset_verification_add_financial_asset';
  static const asset_verification_add_crypto_asset =
      'asset_verification_add_crypto_asset';
  static const asset_verification_add_other_assets =
      'asset_verification_add_other_assets';
  static const asset_verification_add_more_files =
      'asset_verification_add_more_files';
  static const asset_verification_existing_files =
      'asset_verification_existing_files';
  static const asset_verification_uploaded_file =
      'asset_verification_uploaded_file';
  static const asset_verification_error_cannot_delete_existing_file =
      'asset_verification_error_cannot_delete_existing_file';
  static const asset_verification_error_cannot_delete_existing_file_desc =
      'asset_verification_error_cannot_delete_existing_file_desc';
  static const asset_verification_error_cannot_delete_existing_asset =
      'asset_verification_error_cannot_delete_existing_asset';
  static const asset_verification_error_cannot_delete_existing_asset_desc =
      'asset_verification_error_cannot_delete_existing_asset_desc';
  static const asset_verification_confirmation_title =
      'asset_verification_confirmation_title';
  static const asset_verification_confirmation_description =
      'asset_verification_confirmation_description';
  static const asset_verification_agreement_text =
      'asset_verification_agreement_text';
  static const asset_verification_submit = 'asset_verification_submit';
  static const asset_verification_error_agreement_required =
      'asset_verification_error_agreement_required';
  static const asset_verification_error_agreement_required_desc =
      'asset_verification_error_agreement_required_desc';
  static const asset_verification_under_review_title =
      'asset_verification_under_review_title';
  static const asset_verification_under_review_description =
      'asset_verification_under_review_description';
  static const asset_verification_under_review_info_title =
      'asset_verification_under_review_info_title';
  static const asset_verification_under_review_info_description =
      'asset_verification_under_review_info_description';
  static const asset_verification_under_review_status_submitted =
      'asset_verification_under_review_status_submitted';
  static const asset_verification_under_review_status_submitted_desc =
      'asset_verification_under_review_status_submitted_desc';
  static const asset_verification_under_review_status_reviewing =
      'asset_verification_under_review_status_reviewing';
  static const asset_verification_under_review_status_reviewing_desc =
      'asset_verification_under_review_status_reviewing_desc';
  static const asset_verification_under_review_status_complete =
      'asset_verification_under_review_status_complete';
  static const asset_verification_under_review_status_complete_desc =
      'asset_verification_under_review_status_complete_desc';
  static const asset_verification_back_to_home =
      'asset_verification_back_to_home';
  static const asset_verification_success_title =
      'asset_verification_success_title';
  static const asset_verification_success_my_certificate =
      'asset_verification_success_my_certificate';
  static const asset_verification_success_received_certificate =
      'asset_verification_success_received_certificate';
  static const asset_verification_success_certificate_title =
      'asset_verification_success_certificate_title';
  static const asset_verification_success_valid_until =
      'asset_verification_success_valid_until';
  static const asset_verification_success_verification_text =
      'asset_verification_success_verification_text';
  static const asset_verification_success_share =
      'asset_verification_success_share';
  static const asset_verification_success_qr_scan =
      'asset_verification_success_qr_scan';
  static const asset_verification_success_share_title =
      'asset_verification_success_share_title';
  static const asset_verification_success_share_desc =
      'asset_verification_success_share_desc';
  static const asset_verification_success_qr_title =
      'asset_verification_success_qr_title';
  static const asset_verification_success_qr_desc =
      'asset_verification_success_qr_desc';
  static const asset_verification_success_certificate_number =
      'asset_verification_success_certificate_number';
  static const asset_verification_success_asset_range =
      'asset_verification_success_asset_range';
  static const asset_verification_success_issue_date =
      'asset_verification_success_issue_date';
  static const asset_verification_success_expiry_date =
      'asset_verification_success_expiry_date';
  static const asset_verification_success_name =
      'asset_verification_success_name';
  static const asset_verification_success_occupation =
      'asset_verification_success_occupation';
  static const asset_verification_success_age_group =
      'asset_verification_success_age_group';
  static const asset_verification_success_nationality =
      'asset_verification_success_nationality';
  static const asset_verification_success_gender =
      'asset_verification_success_gender';
  static const asset_verification_success_five_star_member =
      'asset_verification_success_five_star_member';
  static const asset_verification_success_verification_message =
      'asset_verification_success_verification_message';
  static const asset_verification_success_tax_accountant =
      'asset_verification_success_tax_accountant';
  static const asset_verification_success_lawyer =
      'asset_verification_success_lawyer';
  static const asset_verification_success_accountant =
      'asset_verification_success_accountant';
  static const asset_verification_success_footer_text =
      'asset_verification_success_footer_text';
  static const asset_verification_success_share_coming_soon =
      'asset_verification_success_share_coming_soon';
  static const asset_verification_success_default_name =
      'asset_verification_success_default_name';
  static const asset_verification_success_default_occupation =
      'asset_verification_success_default_occupation';
  static const asset_verification_success_default_age_group =
      'asset_verification_success_default_age_group';
  static const asset_verification_success_default_nationality =
      'asset_verification_success_default_nationality';
  static const asset_verification_success_default_gender =
      'asset_verification_success_default_gender';
  static const asset_verification_success_default_asset_range =
      'asset_verification_success_default_asset_range';
  static const asset_verification_success_default_issue_date =
      'asset_verification_success_default_issue_date';
  static const asset_verification_success_default_expiry_date =
      'asset_verification_success_default_expiry_date';
  static const asset_verification_success_default_certificate_number =
      'asset_verification_success_default_certificate_number';
  static const asset_verification_received_certificate_empty =
      'asset_verification_received_certificate_empty';
  static const asset_verification_received_certificate_count =
      'asset_verification_received_certificate_count';
  static const asset_verification_qr_scanner_title =
      'asset_verification_qr_scanner_title';
  static const asset_verification_qr_scanner_instruction =
      'asset_verification_qr_scanner_instruction';
  static const asset_verification_qr_scanner_continue =
      'asset_verification_qr_scanner_continue';
  static const asset_verification_qr_scanner_use_data =
      'asset_verification_qr_scanner_use_data';
  static const asset_verification_qr_scanner_position_hint =
      'asset_verification_qr_scanner_position_hint';
  static const asset_verification_qr_scanner_scanned_data =
      'asset_verification_qr_scanner_scanned_data';
  static const asset_verification_qr_scanner_success =
      'asset_verification_qr_scanner_success';
  static const asset_verification_qr_scanner_failed =
      'asset_verification_qr_scanner_failed';
  static const asset_verification_qr_scanner_permission_denied =
      'asset_verification_qr_scanner_permission_denied';
  static const asset_verification_qr_scanner_start_failed =
      'asset_verification_qr_scanner_start_failed';
  static const common_finance_status = 'common_finance_status';
  static const common_search = 'common_search';
  static const common_reject = 'common_reject';
  static const common_rejected_reason = 'common_rejected_reason';
  static const common_reject_popup_title = 'common_reject_popup_title';
  static const common_reject_popup_subtitle_first =
      'common_reject_popup_subtitle_first';
  static const common_reject_popup_subtitle_last =
      'common_reject_popup_subtitle_last';
  static const common_reject_popup_content = 'common_reject_popup_content';
  static const common_reject_popup_input_hint =
      'common_reject_popup_input_hint';
  static const common_continue = 'common_continue';
  static const common_done = 'common_done';
  static const common_cancel = 'common_cancel';
  static const common_save = 'common_save';
  static const common_back = 'common_back';
  static const common_refresh = 'common_refresh';
  static const common_close = 'common_close';
  static const common_next = 'common_next';
  static const common_monday = 'common_monday';
  static const common_tuesday = 'common_tuesday';
  static const common_wednesday = 'common_wednesday';
  static const common_thursday = 'common_thursday';
  static const common_friday = 'common_friday';
  static const common_saturday = 'common_saturday';
  static const common_sunday = 'common_sunday';
  static const common_send = 'common_send';
  static const common_resend = 'common_resend';
  static const common_verify = 'common_verify';
  static const common_reverify = 'common_reverify';
  static const common_edit = 'common_edit';
  static const common_add = 'common_add';
  static const common_preview = 'common_preview';
  static const common_confirm = 'common_confirm';
  static const common_ok = 'common_ok';
  static const common_successful = 'common_successful';
  static const common_heart = 'common_heart';
  static const common_balance = 'common_balance';
  static const common_double_heart = 'common_double_heart';
  static const common_double_heart_up = 'common_double_heart_up';
  static const common_item_required_msg = 'common_item_required_msg';
  static const common_from_photos_album = 'common_from_photos_album';
  static const common_from_photos_documents = 'common_from_photos_documents';
  static const common_file_too_large_content = 'common_file_too_large_content';
  static const common_file_do_not_support_type =
      'common_file_do_not_support_type';
  static const common_details = 'common_details';
  static const common_total = 'common_total';
  static const common_buy = 'common_buy';
  static const common_price = 'common_price';
  static const common_report = 'common_report';
  static const common_block = 'common_block';
  static const common_block_popup_title = 'common_block_popup_title';
  static const common_block_popup_content = 'common_block_popup_content';
  static const common_chat = 'common_chat';
  static const common_matched = 'common_matched';
  static const common_copy = 'common_copy';
  static const common_copy_finish_tip = 'common_copy_finish_tip';
  static const common_skip = 'common_skip';
  static const common_today_cards = 'common_today_cards';
  static const common_got_hearts = 'common_got_hearts';
  static const common_sent_hearts = 'common_sent_hearts';
  static const common_given_cards = 'common_given_cards';
  static const common_passed_cards = 'common_passed_cards';
  static const common_evaluation_title = 'common_evaluation_title';
  static const common_locked = 'common_locked';
  static const common_register_back_title = 'common_register_back_title';
  static const common_register_back_content = 'common_register_back_content';
  static const common_net_error = 'common_net_error';
  static const common_bottom_btn_today = 'common_bottom_btn_today';
  static const common_bottom_btn_passed = 'common_bottom_btn_passed';
  static const common_bottom_btn_community = 'common_bottom_btn_community';
  static const common_bottom_btn_chat = 'common_bottom_btn_chat';
  static const common_bottom_btn_me = 'common_bottom_btn_me';
  static const common_accept = 'common_accept';
  static const common_try_again = 'common_try_again';
  static const common_remove = 'common_remove';
  static const common_remove_popup_title = 'common_remove_popup_title';
  static const common_account_kickoff_tip = 'common_account_kickoff_tip';
  static const permission_photo_galley_alert_title =
      'permission_photo_galley_alert_title';
  static const permission_photo_galley_alert_content =
      'permission_photo_galley_alert_content';
  static const permission_camera_alert_title = 'permission_camera_alert_title';
  static const permission_camera_alert_content =
      'permission_camera_alert_content';
  static const permission_mic_alert_title = 'permission_mic_alert_title';
  static const permission_mic_alert_content = 'permission_mic_alert_content';
  static const time_just_now = 'time_just_now';
  static const time_ago = 'time_ago';
  static const time_minutes_ago = 'time_minutes_ago';
  static const time_hours_ago = 'time_hours_ago';
  static const time_days_ago = 'time_days_ago';
  static const time_months_ago = 'time_months_ago';
  static const time_years_ago = 'time_years_ago';
  static const splash_title = 'splash_title';
  static const splash_start = 'splash_start';
  static const splash_learn = 'splash_learn';
  static const sign_in_title = 'sign_in_title';
  static const sign_in_zalo = 'sign_in_zalo';
  static const sign_in_google = 'sign_in_google';
  static const sign_in_facebook = 'sign_in_facebook';
  static const sign_in_apple = 'sign_in_apple';
  static const agreement_title = 'agreement_title';
  static const agreement_accept_all_title = 'agreement_accept_all_title';
  static const agreement_age_title = 'agreement_age_title';
  static const agreement_terms_title = 'agreement_terms_title';
  static const agreement_personal_info_title = 'agreement_personal_info_title';
  static const agreement_promotional_title = 'agreement_promotional_title';
  static const agreement_safety_and_policy_center =
      'agreement_safety_and_policy_center';
  static const agreement_see_detail = 'agreement_see_detail';
  static const personal_basic_info_nationality =
      'personal_basic_info_nationality';
  static const personal_basic_info_nationality_hint =
      'personal_basic_info_nationality_hint';
  static const personal_basic_info_please_enter_your_nationality =
      'personal_basic_info_please_enter_your_nationality';
  static const personal_basic_info_sign_up = 'personal_basic_info_sign_up';
  static const personal_basic_info_title = 'personal_basic_info_title';
  static const personal_basic_info_subtitle = 'personal_basic_info_subtitle';
  static const personal_basic_info_nick_name = 'personal_basic_info_nick_name';
  static const personal_basic_info_nick_name_PH =
      'personal_basic_info_nick_name_PH';
  static const personal_basic_info_date_birth =
      'personal_basic_info_date_birth';
  static const personal_basic_info_date_birth_PH =
      'personal_basic_info_date_birth_PH';
  static const personal_basic_info_region = 'personal_basic_info_region';
  static const personal_basic_info_region_PH = 'personal_basic_info_region_PH';
  static const personal_basic_info_gender = 'personal_basic_info_gender';
  static const personal_basic_info_female = 'personal_basic_info_female';
  static const personal_basic_info_male = 'personal_basic_info_male';
  static const personal_basic_info_phone_number =
      'personal_basic_info_phone_number';
  static const personal_basic_info_phone_number_PH =
      'personal_basic_info_phone_number_PH';
  static const personal_basic_info_verification_code_PH =
      'personal_basic_info_verification_code_PH';
  static const personal_basic_info_phone_number_invalid_msg =
      'personal_basic_info_phone_number_invalid_msg';
  static const personal_basic_info_phone_number_need_verify =
      'personal_basic_info_phone_number_need_verify';
  static const personal_basic_info_verification_code_sent_msg =
      'personal_basic_info_verification_code_sent_msg';
  static const personal_basic_info_verification_code_valid_msg =
      'personal_basic_info_verification_code_valid_msg';
  static const personal_basic_info_verification_code_invalid_msg =
      'personal_basic_info_verification_code_invalid_msg';
  static const personal_basic_info_nick_name_exist_tip =
      'personal_basic_info_nick_name_exist_tip';
  static const personal_basic_info_email_PH = 'personal_basic_info_email_PH';
  static const personal_basic_info_location_tip =
      'personal_basic_info_location_tip';
  static const personal_basic_info_phone_input_tip =
      'personal_basic_info_phone_input_tip';
  static const personal_basic_info_email_send_tip =
      'personal_basic_info_email_send_tip';
  static const personal_basic_info_saving = 'personal_basic_info_saving';
  static const university_info_page_title = 'university_info_page_title';
  static const university_info_title = 'university_info_title';
  static const university_info_subtitle = 'university_info_subtitle';
  static const university_info_financial_verify_title =
      'university_info_financial_verify_title';
  static const university_info_financial_verify_subtitle =
      'university_info_financial_verify_subtitle';
  static const university_info_name = 'university_info_name';
  static const university_info_name_PH = 'university_info_name_PH';
  static const university_info_university = 'university_info_university';
  static const university_info_university_PH = 'university_info_university_PH';
  static const university_info_verify_your_wealthly =
      'university_info_verify_your_wealthly';
  static const university_info_verify_your_wealthly_hint =
      'university_info_verify_your_wealthly_hint';
  static const university_info_financial_status_doNotAuthorize =
      'university_info_financial_status_doNotAuthorize';
  static const university_info_financial_status_submitted =
      'university_info_financial_status_submitted';
  static const university_info_financial_status_moreThan100K =
      'university_info_financial_status_moreThan100K';
  static const university_info_financial_status_moreThan500K =
      'university_info_financial_status_moreThan500K';
  static const university_info_financial_status_moreThan1M =
      'university_info_financial_status_moreThan1M';
  static const university_info_financial_status_moreThan5M =
      'university_info_financial_status_moreThan5M';
  static const university_info_financial_status_moreThan10M =
      'university_info_financial_status_moreThan10M';
  static const university_info_financial_status_moreThan50M =
      'university_info_financial_status_moreThan50M';
  static const university_info_financial_status_moreThan100M =
      'university_info_financial_status_moreThan100M';
  static const university_info_auth_methods = 'university_info_auth_methods';
  static const university_info_verify_email = 'university_info_verify_email';
  static const university_info_verify_email_note =
      'university_info_verify_email_note';
  static const university_info_verify_cert_paper =
      'university_info_verify_cert_paper';
  static const university_info_verify_cert_paper_note =
      'university_info_verify_cert_paper_note';
  static const verify_email_subtitle = 'verify_email_subtitle';
  static const verify_email_input_PH = 'verify_email_input_PH';
  static const verify_email_note = 'verify_email_note';
  static const verify_email_code_valid_msg = 'verify_email_code_valid_msg';
  static const verify_cert_paper_subtitle = 'verify_cert_paper_subtitle';
  static const verify_cert_paper_input_PH = 'verify_cert_paper_input_PH';
  static const verify_cert_paper_note1 = 'verify_cert_paper_note1';
  static const verify_cert_paper_note2 = 'verify_cert_paper_note2';
  static const verify_cert_paper_note3 = 'verify_cert_paper_note3';
  static const verify_cert_paper_note4 = 'verify_cert_paper_note4';
  static const verify_cert_paper_submit_to_verify =
      'verify_cert_paper_submit_to_verify';
  static const submit_photos_page_title = 'submit_photos_page_title';
  static const submit_photos_title = 'submit_photos_title';
  static const submit_photos_subtitle = 'submit_photos_subtitle';
  static const interesting_title = 'interesting_title';
  static const interesting_subtitle = 'interesting_subtitle';
  static const interesting_input_tip = 'interesting_input_tip';
  static const introduce_title = 'introduce_title';
  static const introduce_subtitle = 'introduce_subtitle';
  static const introduce_input_PH = 'introduce_input_PH';
  static const introduce_note1 = 'introduce_note1';
  static const introduce_note2 = 'introduce_note2';
  static const signUp_completion_title = 'signUp_completion_title';
  static const signUp_completion_subtitle = 'signUp_completion_subtitle';
  static const signUp_completion_phone_verification_title =
      'signUp_completion_phone_verification_title';
  static const signUp_completion_phone_verification_subtitle =
      'signUp_completion_phone_verification_subtitle';
  static const signUp_completion_pending_verification_title =
      'signUp_completion_pending_verification_title';
  static const signUp_completion_pending_verification_subtitle =
      'signUp_completion_pending_verification_subtitle';
  static const signUp_completion_pending_verification_subtitle_2 =
      'signUp_completion_pending_verification_subtitle_2';
  static const signUp_completion_failed_verification_title =
      'signUp_completion_failed_verification_title';
  static const signUp_completion_failed_verification_subtitle =
      'signUp_completion_failed_verification_subtitle';
  static const signUp_completion_back_verify = 'signUp_completion_back_verify';
  static const profile_about_me = 'profile_about_me';
  static const profile_interests = 'profile_interests';
  static const profile_evaluate_card = 'profile_evaluate_card';
  static const profile_rate_to_get_mc = 'profile_rate_to_get_mc';
  static const profile_rating_popup_title = 'profile_rating_popup_title';
  static const profile_rating_popup_subtitle = 'profile_rating_popup_subtitle';
  static const profile_to_rate = 'profile_to_rate';
  static const profile_title = 'profile_title';
  static const profile_rating_result_tip = 'profile_rating_result_tip';
  static const profile_send_heart_message = 'profile_send_heart_message';
  static const profile_send_double_heart_message =
      'profile_send_double_heart_message';
  static const profile_heart_operation_tip_one =
      'profile_heart_operation_tip_one';
  static const profile_heart_operation_tip_two =
      'profile_heart_operation_tip_two';
  static const profile_doubleHeart_operation_tip_one =
      'profile_doubleHeart_operation_tip_one';
  static const profile_doubleHeart_operation_tip_two =
      'profile_doubleHeart_operation_tip_two';
  static const mee_coin_meecoin = 'mee_coin_meecoin';
  static const mee_coin_available_meecoin = 'mee_coin_available_meecoin';
  static const mee_coin_recharge = 'mee_coin_recharge';
  static const mee_coin_transaction = 'mee_coin_transaction';
  static const mee_coin_mct_unlock_given_card =
      'mee_coin_mct_unlock_given_card';
  static const mee_coin_mct_unlock_high_eva = 'mee_coin_mct_unlock_high_eva';
  static const mee_coin_mct_unlock_heart_card =
      'mee_coin_mct_unlock_heart_card';
  static const mee_coin_mct_send_heart = 'mee_coin_mct_send_heart';
  static const mee_coin_mct_send_double_heart =
      'mee_coin_mct_send_double_heart';
  static const mee_coin_mct_buy_premium = 'mee_coin_mct_buy_premium';
  static const mee_coin_mct_eva = 'mee_coin_mct_eva';
  static const mee_coin_mct_double_heart_refund =
      'mee_coin_mct_double_heart_refund';
  static const mee_coin_mct_recharge = 'mee_coin_mct_recharge';
  static const mee_coin_mct_ref2_bonus = 'mee_coin_mct_ref2_bonus';
  static const mee_coin_mct_ref1_bonus = 'mee_coin_mct_ref1_bonus';
  static const mee_coin_mct_pre_reg = 'mee_coin_mct_pre_reg';
  static const mee_coin_mct_deposit = 'mee_coin_mct_deposit';
  static const mee_coin_mct_deduction = 'mee_coin_mct_deduction';
  static const mee_coin_mct_top_post = 'mee_coin_mct_top_post';
  static const mee_coin_recharge_title = 'mee_coin_recharge_title';
  static const mee_coin_recharge_subtitle = 'mee_coin_recharge_subtitle';
  static const mee_coin_practical = 'mee_coin_practical';
  static const mee_coin_popular = 'mee_coin_popular';
  static const mee_coin_bonus_rate = 'mee_coin_bonus_rate';
  static const mee_coin_bonus_rate_title = 'mee_coin_bonus_rate_title';
  static const mee_coin_vnd = 'mee_coin_vnd';
  static const mee_coin_bonus = 'mee_coin_bonus';
  static const mee_coin_recharge_amount = 'mee_coin_recharge_amount';
  static const mee_coin_pay_failed = 'mee_coin_pay_failed';
  static const mee_coin_pay_invalid = 'mee_coin_pay_invalid';
  static const today_cards_recharge_title = 'today_cards_recharge_title';
  static const today_cards_recharge_subtitle = 'today_cards_recharge_subtitle';
  static const passed_cards_empty_msg = 'passed_cards_empty_msg';
  static const unlock_balance = 'unlock_balance';
  static const unlock_recharge = 'unlock_recharge';
  static const unlock_cards = 'unlock_cards';
  static const unlock_premium_cards = 'unlock_premium_cards';
  static const unlock_premium_subtitle_1 = 'unlock_premium_subtitle_1';
  static const unlock_premium_subtitle_2 = 'unlock_premium_subtitle_2';
  static const unlock_premium_subtitle_3 = 'unlock_premium_subtitle_3';
  static const unlock_note = 'unlock_note';
  static const unlock_unlock_single_heart_title =
      'unlock_unlock_single_heart_title';
  static const unlock_unlock_single_heart_subtitle =
      'unlock_unlock_single_heart_subtitle';
  static const unlock_unlock_passed_given_card_title =
      'unlock_unlock_passed_given_card_title';
  static const unlock_unlock_passed_given_card_subtitle =
      'unlock_unlock_passed_given_card_subtitle';
  static const unlock_unlock_high_eva_title = 'unlock_unlock_high_eva_title';
  static const unlock_unlock_high_eva_subtitle =
      'unlock_unlock_high_eva_subtitle';
  static const unlock_send_single_heart_title =
      'unlock_send_single_heart_title';
  static const unlock_send_single_heart_subtitle =
      'unlock_send_single_heart_subtitle';
  static const unlock_send_double_heart_title =
      'unlock_send_double_heart_title';
  static const unlock_send_double_heart_subtitle =
      'unlock_send_double_heart_subtitle';
  static const unlock_not_enough_balance = 'unlock_not_enough_balance';
  static const unlock_not_enough_balance_popup_content =
      'unlock_not_enough_balance_popup_content';
  static const unlock_no_profile_popup_title = 'unlock_no_profile_popup_title';
  static const unlock_no_profile_popup_content =
      'unlock_no_profile_popup_content';
  static const unlock_no_profile_popup_ok_button =
      'unlock_no_profile_popup_ok_button';
  static const subscription_buy = 'subscription_buy';
  static const subscription_normal_heart_name =
      'subscription_normal_heart_name';
  static const subscription_normal_heart_description =
      'subscription_normal_heart_description';
  static const subscription_double_heart_name =
      'subscription_double_heart_name';
  static const subscription_double_heart_description =
      'subscription_double_heart_description';
  static const subscription_tip = 'subscription_tip';
  static const chat_common_hey_there = 'chat_common_hey_there';
  static const chat_common_everyone = 'chat_common_everyone';
  static const chat_common_no_recent_emoji = 'chat_common_no_recent_emoji';
  static const chat_common_read = 'chat_common_read';
  static const chat_common_say_hi = 'chat_common_say_hi';
  static const chat_common_empty_tip = 'chat_common_empty_tip';
  static const chat_common_conversation_empty_tip =
      'chat_common_conversation_empty_tip';
  static const chat_common_today_btn = 'chat_common_today_btn';
  static const chat_common_typing_tip = 'chat_common_typing_tip';
  static const chat_common_synchronizing = 'chat_common_synchronizing';
  static const chat_common_syncFailed = 'chat_common_syncFailed';
  static const chat_common_connecting = 'chat_common_connecting';
  static const chat_common_connectionFailed = 'chat_common_connectionFailed';
  static const chat_messageType_picture = 'chat_messageType_picture';
  static const chat_messageType_video = 'chat_messageType_video';
  static const chat_messageType_voice = 'chat_messageType_voice';
  static const chat_messageType_file = 'chat_messageType_file';
  static const chat_messageType_emoji = 'chat_messageType_emoji';
  static const chat_messageType_unsupportedMessage =
      'chat_messageType_unsupportedMessage';
  static const chat_time_now = 'chat_time_now';
  static const chat_time_justNow = 'chat_time_justNow';
  static const chat_time_ago = 'chat_time_ago';
  static const chat_time_minutes_ago = 'chat_time_minutes_ago';
  static const chat_time_hours_ago = 'chat_time_hours_ago';
  static const chat_time_days_ago = 'chat_time_days_ago';
  static const chat_time_months_ago = 'chat_time_months_ago';
  static const chat_time_years_ago = 'chat_time_years_ago';
  static const chat_time_count_minute = 'chat_time_count_minute';
  static const chat_time_count_hour = 'chat_time_count_hour';
  static const chat_time_date_time_month = 'chat_time_date_time_month';
  static const chat_time_date_time_day = 'chat_time_date_time_day';
  static const chat_empty_matched_info1 = 'chat_empty_matched_info1';
  static const chat_empty_matched_info2 = 'chat_empty_matched_info2';
  static const chat_sys_msg_remove_black = 'chat_sys_msg_remove_black';
  static const compare_heart_item = 'compare_heart_item';
  static const compare_heart_row1 = 'compare_heart_row1';
  static const compare_heart_row2 = 'compare_heart_row2';
  static const compare_heart_row3 = 'compare_heart_row3';
  static const compare_heart_row4 = 'compare_heart_row4';
  static const compare_heart_row4_double_heart =
      'compare_heart_row4_double_heart';
  static const compare_heart_row5 = 'compare_heart_row5';
  static const compare_heart_row5_double_heart =
      'compare_heart_row5_double_heart';
  static const my_page_title = 'my_page_title';
  static const my_page_about = 'my_page_about';
  static const my_page_help_center = 'my_page_help_center';
  static const my_page_feedback = 'my_page_feedback';
  static const my_page_terms_conditions = 'my_page_terms_conditions';
  static const my_page_privacy_policy = 'my_page_privacy_policy';
  static const my_page_safety_and_policy_center =
      'my_page_safety_and_policy_center';
  static const my_page_settings = 'my_page_settings';
  static const my_page_market = 'my_page_market';
  static const my_page_referrer_tip = 'my_page_referrer_tip';
  static const my_page_available_text = 'my_page_available_text';
  static const my_page_logout_btn = 'my_page_logout_btn';
  static const my_page_referrer_pop_title = 'my_page_referrer_pop_title';
  static const my_page_referrer_pop_content = 'my_page_referrer_pop_content';
  static const my_page_invitation = 'my_page_invitation';
  static const my_page_invitation_menu_tip = 'my_page_invitation_menu_tip';
  static const my_page_pending_profile_tip = 'my_page_pending_profile_tip';
  static const my_page_finish_profile_tip = 'my_page_finish_profile_tip';
  static const settings_title = 'settings_title';
  static const settings_manage_black_list = 'settings_manage_black_list';
  static const settings_quit = 'settings_quit';
  static const settings_blocklist = 'settings_blocklist';
  static const settings_unblock = 'settings_unblock';
  static const settings_language = 'settings_language';
  static const settings_english = 'settings_english';
  static const settings_korean = 'settings_korean';
  static const report_main_title = 'report_main_title';
  static const report_sub_title = 'report_sub_title';
  static const report_reason1 = 'report_reason1';
  static const report_reason2 = 'report_reason2';
  static const report_reason3 = 'report_reason3';
  static const report_reason4 = 'report_reason4';
  static const report_reason5 = 'report_reason5';
  static const report_reason6 = 'report_reason6';
  static const report_other_placeholder = 'report_other_placeholder';
  static const report_image_tip = 'report_image_tip';
  static const report_image = 'report_image';
  static const report_upload_tip = 'report_upload_tip';
  static const report_report_tip = 'report_report_tip';
  static const report_report_btn = 'report_report_btn';
  static const report_file_empty_tip = 'report_file_empty_tip';
  static const edit_profile_upload_documents = 'edit_profile_upload_documents';
  static const edit_profile_update_my_attestation =
      'edit_profile_update_my_attestation';
  static const edit_profile_support_document = 'edit_profile_support_document';
  static const edit_profile_support_document_tip =
      'edit_profile_support_document_tip';
  static const edit_profile_enable_discovery = 'edit_profile_enable_discovery';
  static const edit_profile_enable_discovery_note =
      'edit_profile_enable_discovery_note';
  static const edit_profile_enable_discovery_alert_title =
      'edit_profile_enable_discovery_alert_title';
  static const edit_profile_enable_discovery_alert_content =
      'edit_profile_enable_discovery_alert_content';
  static const edit_profile_photo_title = 'edit_profile_photo_title';
  static const edit_profile_address_title = 'edit_profile_address_title';
  static const edit_profile_interests_title = 'edit_profile_interests_title';
  static const edit_profile_aboutme_title = 'edit_profile_aboutme_title';
  static const edit_profile_photo_avatar_require =
      'edit_profile_photo_avatar_require';
  static const edit_profile_interests_require =
      'edit_profile_interests_require';
  static const edit_profile_introduction_require =
      'edit_profile_introduction_require';
  static const edit_profile_unsaved_change_title =
      'edit_profile_unsaved_change_title';
  static const edit_profile_unsaved_change_content =
      'edit_profile_unsaved_change_content';
  static const edit_profile_leave = 'edit_profile_leave';
  static const edit_profile_stay = 'edit_profile_stay';
  static const edit_profile_attestation = 'edit_profile_attestation';
  static const edit_profile_basic_information =
      'edit_profile_basic_information';
  static const edit_profile_others = 'edit_profile_others';
  static const edit_profile_verify_university =
      'edit_profile_verify_university';
  static const edit_profile_university_not_certified =
      'edit_profile_university_not_certified';
  static const edit_profile_university_pending =
      'edit_profile_university_pending';
  static const edit_profile_university_rejected =
      'edit_profile_university_rejected';
  static const edit_profile_university_certified =
      'edit_profile_university_certified';
  static const edit_profile_tap_to_edit_tip = 'edit_profile_tap_to_edit_tip';
  static const edit_profile_edit_save_tip_title =
      'edit_profile_edit_save_tip_title';
  static const edit_profile_edit_save_tip_content =
      'edit_profile_edit_save_tip_content';
  static const edit_profile_university_name_required =
      'edit_profile_university_name_required';
  static const edit_profile_university_university_required =
      'edit_profile_university_university_required';
  static const edit_profile_university_student_id_required =
      'edit_profile_university_student_id_required';
  static const edit_profile_university_email_required =
      'edit_profile_university_email_required';
  static const edit_profile_university_v_code_required =
      'edit_profile_university_v_code_required';
  static const edit_profile_profile_university_required =
      'edit_profile_profile_university_required';
  static const edit_profile_profile_nickname_required =
      'edit_profile_profile_nickname_required';
  static const edit_profile_profile_birth_required =
      'edit_profile_profile_birth_required';
  static const edit_profile_profile_region_required =
      'edit_profile_profile_region_required';
  static const edit_profile_profile_gender_required =
      'edit_profile_profile_gender_required';
  static const edit_profile_profile_phone_required =
      'edit_profile_profile_phone_required';
  static const edit_profile_profile_photos_required =
      'edit_profile_profile_photos_required';
  static const edit_profile_isGraduated = 'edit_profile_isGraduated';
  static const invitation_referrer_tip = 'invitation_referrer_tip';
  static const invitation_referrer_input_tip = 'invitation_referrer_input_tip';
  static const invitation_referrer_popup_content_1 =
      'invitation_referrer_popup_content_1';
  static const invitation_referrer_popup_content_2 =
      'invitation_referrer_popup_content_2';
  static const invitation_referrer_error_title =
      'invitation_referrer_error_title';
  static const invitation_referrer_error_content =
      'invitation_referrer_error_content';
  static const invitation_reward_popup_title = 'invitation_reward_popup_title';
  static const invitation_reward_popup_content =
      'invitation_reward_popup_content';
  static const invitation_referrer_code = 'invitation_referrer_code';
  static const feedback_email_label = 'feedback_email_label';
  static const feedback_email_placeholder = 'feedback_email_placeholder';
  static const feedback_inquiry_label = 'feedback_inquiry_label';
  static const feedback_inquiry_placeholder = 'feedback_inquiry_placeholder';
  static const feedback_content_placeholder = 'feedback_content_placeholder';
  static const feedback_image_label = 'feedback_image_label';
  static const feedback_email_incorrect = 'feedback_email_incorrect';
  static const feedback_content_required = 'feedback_content_required';
  static const feedback_content_max_length = 'feedback_content_max_length';
  static const quit_main_title = 'quit_main_title';
  static const quit_main_desc = 'quit_main_desc';
  static const quit_content_title = 'quit_content_title';
  static const quit_list_1 = 'quit_list_1';
  static const quit_list_1_a = 'quit_list_1_a';
  static const quit_list_2 = 'quit_list_2';
  static const quit_list_2_a = 'quit_list_2_a';
  static const quit_list_3 = 'quit_list_3';
  static const quit_list_3_a = 'quit_list_3_a';
  static const quit_list_4 = 'quit_list_4';
  static const quit_list_4_a = 'quit_list_4_a';
  static const quit_list_5 = 'quit_list_5';
  static const quit_list_5_a = 'quit_list_5_a';
  static const quit_list_6 = 'quit_list_6';
  static const quit_list_6_a = 'quit_list_6_a';
  static const quit_agreement_title = 'quit_agreement_title';
  static const quit_reason_title = 'quit_reason_title';
  static const quit_reason_1 = 'quit_reason_1';
  static const quit_reason_2 = 'quit_reason_2';
  static const quit_reason_3 = 'quit_reason_3';
  static const quit_reason_4 = 'quit_reason_4';
  static const quit_reason_5 = 'quit_reason_5';
  static const quit_reason_placeholder = 'quit_reason_placeholder';
  static const quit_quit = 'quit_quit';
  static const quit_confirm_title = 'quit_confirm_title';
  static const quit_confirm_popup = 'quit_confirm_popup';
  static const chat_notification_title = 'chat_notification_title';
  static const chat_notification_content = 'chat_notification_content';
  static const notification_title = 'notification_title';
  static const community_title = 'community_title';
  static const community_register_tip = 'community_register_tip';
  static const community_fakeName_tip = 'community_fakeName_tip';
  static const community_delete = 'community_delete';
  static const community_post_list_empty = 'community_post_list_empty';
  static const community_comments_list_empty = 'community_comments_list_empty';
  static const community_likes_list_empty = 'community_likes_list_empty';
  static const community_me = 'community_me';
  static const community_post_title = 'community_post_title';
  static const community_post_placeholder = 'community_post_placeholder';
  static const community_post_board_label = 'community_post_board_label';
  static const community_post_post_btn = 'community_post_post_btn';
  static const community_post_photo_upload_tip =
      'community_post_photo_upload_tip';
  static const community_post_content_empty_tip =
      'community_post_content_empty_tip';
  static const community_post_deleted_post = 'community_post_deleted_post';
  static const community_post_show_more = 'community_post_show_more';
  static const community_post_hide_more = 'community_post_hide_more';
  static const community_post_see_likes = 'community_post_see_likes';
  static const community_post_all_comments = 'community_post_all_comments';
  static const community_post_all_likes = 'community_post_all_likes';
  static const community_post_empty = 'community_post_empty';
  static const community_comment_placeholder = 'community_comment_placeholder';
  static const community_comment_more_text = 'community_comment_more_text';
  static const community_comment_end_text = 'community_comment_end_text';
  static const community_comment_mention_empty_tip =
      'community_comment_mention_empty_tip';
  static const community_comment_mention_empty_1 =
      'community_comment_mention_empty_1';
  static const community_comment_mention_empty_2 =
      'community_comment_mention_empty_2';
  static const community_report_reason1 = 'community_report_reason1';
  static const community_report_reason2 = 'community_report_reason2';
  static const community_report_reason3 = 'community_report_reason3';
  static const community_report_reason4 = 'community_report_reason4';
  static const community_report_reason5 = 'community_report_reason5';
  static const community_report_reason6 = 'community_report_reason6';
  static const community_report_reason7 = 'community_report_reason7';
  static const community_report_reason8 = 'community_report_reason8';
  static const community_report_reason9 = 'community_report_reason9';
  static const community_report_reason10 = 'community_report_reason10';
  static const community_report_reason11 = 'community_report_reason11';
  static const community_report_reason12 = 'community_report_reason12';
  static const community_report_reason13 = 'community_report_reason13';
  static const community_profile_posts = 'community_profile_posts';
  static const community_profile_likes = 'community_profile_likes';
  static const community_profile_edit_profile =
      'community_profile_edit_profile';
  static const community_profile_creat_post = 'community_profile_creat_post';
  static const community_profile_comments = 'community_profile_comments';
  static const check_app_version_new_version_title =
      'check_app_version_new_version_title';
  static const check_app_version_new_version_content =
      'check_app_version_new_version_content';
  static const check_app_version_new_version_action =
      'check_app_version_new_version_action';
  static const check_app_version_new_version_action_later =
      'check_app_version_new_version_action_later';
  static const otp_verify_page_title = 'otp_verify_page_title';
  static const otp_verify_page_message = 'otp_verify_page_message';
  static const otp_verify_page_didnt_get_the_code =
      'otp_verify_page_didnt_get_the_code';
  static const otp_verify_page_resend = 'otp_verify_page_resend';
  static const otp_verify_page_resend_after = 'otp_verify_page_resend_after';
  static const otp_verify_page_successfully = 'otp_verify_page_successfully';
  static const otp_verify_page_error_title = 'otp_verify_page_error_title';
  static const sc_str_ai_avatar_error_no_croppable_avatar =
      'sc_str_ai_avatar_error_no_croppable_avatar';
  static const sc_str_ai_avatar_error_web_not_supported =
      'sc_str_ai_avatar_error_web_not_supported';
  static const sc_str_ai_avatar_error_data_format_error =
      'sc_str_ai_avatar_error_data_format_error';
  static const sc_str_ai_avatar_error_download_failed_code =
      'sc_str_ai_avatar_error_download_failed_code';
  static const sc_str_ai_avatar_error_download_failed =
      'sc_str_ai_avatar_error_download_failed';
  static const sc_str_ai_avatar_error_crop_success =
      'sc_str_ai_avatar_error_crop_success';
  static const sc_str_ai_avatar_error_crop_failed =
      'sc_str_ai_avatar_error_crop_failed';
  static const sc_str_ai_avatar_error_upload_first =
      'sc_str_ai_avatar_error_upload_first';
  static const sc_str_ai_avatar_error_generating_wait =
      'sc_str_ai_avatar_error_generating_wait';
  static const sc_str_ai_avatar_error_empty_photo_data =
      'sc_str_ai_avatar_error_empty_photo_data';
  static const sc_str_ai_avatar_error_empty_photo_file =
      'sc_str_ai_avatar_error_empty_photo_file';
  static const sc_str_ai_avatar_error_timeout =
      'sc_str_ai_avatar_error_timeout';
}

class Locales {
  static const en = {
    'sc_str_common_view': 'View',
    'sc_str_common_view_all': 'See all',
    'sc_str_common_members': 'Members',
    'sc_str_common_events': 'Events',
    'sc_str_common_comments': 'Comments',
    'sc_str_common_comment_placeholder': 'Write a comment...',
    'sc_str_common_post': 'Post',
    'sc_str_common_upload': 'Upload',
    'sc_str_common_continue': 'Continue',
    'sc_str_common_publish': 'Publish',
    'sc_str_common_image': 'Image',
    'sc_str_event_intro_recommended': 'Recommended Events',
    'sc_str_home_this_week': 'This Week',
    'sc_str_home_last_week': 'Last Week',
    'sc_str_home_upcoming': 'Upcoming',
    'sc_str_home_weekly_star': 'Weekly Star',
    'sc_str_home_featured_events': 'Featured Events',
    'sc_str_home_notification_content': 'Notification content',
    'sc_str_home_last_week_event_title': 'Last week\'s event',
    'sc_str_home_next_week_star_title': '✨ Next Interviewee.',
    'sc_str_home_next_week_star_subtitle':
        'Next week, we will have |@title| joining us.',
    'sc_str_home_look_forward': 'Looking forward...',
    'sc_str_home_member_suffix': 'Member',
    'sc_str_home_interview_article': 'View Interview Article',
    'sc_str_comment_Write_comment': 'Write a comment',
    'sc_str_comment_your_thought': 'Leave your thoughts',
    'sc_str_comment_all_comments': 'Comments',
    'sc_str_comment_add_comment': 'Add Comment',
    'sc_str_comment_interview_comment_placeholder':
        '✨ Share your thoughts about the interview freely.',
    'sc_str_event_capacity_notice':
        'If capacity is exceeded, participants will be selected.',
    'sc_str_event_event_introduction': 'Event Introduction',
    'sc_str_event_join_application': 'Apply for Event',
    'sc_str_event_application_completed':
        'Application completed. Final selection results will be notified individually 2 days before the event.',
    'sc_str_event_congratulations_selected':
        'Congratulations! You have been selected for this event.',
    'sc_str_event_not_selected_message':
        'We couldn\'t be together this time, but we look forward to the next opportunity. Don\'t miss other events too.',
    'sc_str_event_ended': 'Ended',
    'sc_str_event_confirmed_participants': 'Confirmed Participants',
    'sc_str_event_past_interviews': 'Past Interviews',
    'sc_str_event_interview': 'Interview',
    'sc_str_event_address': 'Address',
    'sc_str_event_private_club_address': 'Private Club in Hannam-dong, Seoul',
    'sc_str_community_register_tip':
        'Craft a distinctive identity for our exclusive community. Your nickname remains private from your main profile.',
    'sc_str_community_name_input_PH': 'Enter your distinguished name',
    'sc_str_community_name_already_taken':
        'This name is already taken. Try another one.',
    'sc_str_community_name_select_tip':
        'Choose a recommended name or create your own',
    'sc_str_community_enter_circle': 'Enter Circle',
    'sc_str_community_fake_name_existed_error': 'This name is already taken',
    'sc_str_community_craft_your_post': 'Craft Your Post',
    'sc_str_community_select_a_circle': 'Select a Circle',
    'sc_str_community_share_your_insight': 'Share your Insight...',
    'sc_str_community_media_upload_tip':
        'Upload up to 10 high-quality images (JPG/PNG, max 5MB each).',
    'sc_str_ai_avatar_generating': 'Generating AI avatar, please wait...',
    'sc_str_ai_avatar_wait_time': 'This may take 1-3 minutes',
    'sc_str_ai_avatar_generated': 'AI enhanced avatar generated',
    'sc_str_ai_avatar_select_again': 'Select Again',
    'sc_str_ai_avatar_crop_avatar': 'Crop Avatar',
    'sc_str_ai_avatar_image_load_failed': 'Image loading failed',
    'sc_str_ai_avatar_image_data_error': 'Image data error',
    'sc_str_ai_avatar_network_image_failed': 'Network image loading failed',
    'sc_str_ai_avatar_no_image': 'No image available',
    'sc_str_ai_avatar_render_error': 'Avatar rendering error',
    'sc_str_ai_avatar_completed': 'AI Avatar Generation Completed',
    'sc_str_ai_avatar_generation_success':
        'AI avatar generated successfully! You can now use this avatar.',
    'sc_str_ai_avatar_generation_failed': 'AI avatar generation failed',
    'sc_str_ai_avatar_generation_cancelled': 'AI avatar generation cancelled',
    'sc_str_ai_avatar_cancel_generation': 'Cancel AI Generation',
    'sc_str_ai_avatar_cancel_generation_confirm':
        'Are you sure you want to cancel AI avatar generation? This action cannot be undone.',
    'sc_str_ai_avatar_regenerate': 'Regenerate',
    'sc_str_ai_avatar_use_this_avatar': 'Use This Avatar',
    'sc_str_ai_avatar_set_success': 'AI avatar set successfully',
    'sc_str_ai_avatar_generating_in_progress': 'AI Generating',
    'sc_str_ai_avatar_estimated_time_remaining':
        'Estimated @seconds seconds remaining',
    'sc_str_ai_avatar_task_restored':
        'AI avatar generation task detected in progress, automatically restored',
    'sc_str_ai_avatar_task_completed': 'AI avatar generation completed!',
    'not_verified_yet_warning_message':
        'Caution! This person is not verified yet.',
    'rejected': 'Rejected',
    'not_verified': 'Not verified',
    'university': 'University',
    'skip_invitation_message':
        'You can enter the referral code within a week of signing up on My page.',
    'input_validation_required_error_message': 'This field is required.',
    'badge_title': 'Badge',
    'badge_university_badges': 'University badges',
    'badge_wealth_badges': 'Wealth badges',
    'badge_badge_policy':
        'All university and wealth verification badges on Starchex are strictly validated and awarded by Starchex\'s verification teams based on appropriate supporting documents submitted by users. This rigorous process ensures the reliability and trustworthiness of our verification-based service.',
    'badge_graduate': 'Graduate',
    'badge_graduate_tip': 'User has graduated from a university.',
    'badge_top_200': 'Top 200',
    'badge_top_200_tip': 'User is from a top 200 university.',
    'badge_more_than_500k': 'More than 500K',
    'badge_more_than_500k_tip': 'User has more than 500K in assets.',
    'badge_more_than_1m': 'More than 1M',
    'badge_more_than_1m_tip': 'User has more than 1M in assets.',
    'badge_more_than_10m': 'More than 10M',
    'badge_more_than_10m_tip': 'User has more than 10M in assets.',
    'asset_verification_title': 'Asset Verification',
    'asset_verification_description':
        'The following assets can be verified with StarChecks.',
    'asset_verification_real_estate': 'Real Estate',
    'asset_verification_financial_assets': 'Financial Assets',
    'asset_verification_crypto_currency': 'Crypto Currency',
    'asset_verification_art_works_cars': 'Art works, Cars, ...',
    'asset_verification_guidance_title': '[Guidance & Disclaimers]',
    'asset_verification_guidance_1':
        'All submitted information is processed securely in accordance with relevant laws and our security policy.',
    'asset_verification_guidance_2':
        'For accurate asset verification, all information and documents must be factual and consistent.',
    'asset_verification_guidance_3':
        'Submission of false information may result in restriction of service use.',
    'asset_verification_next': 'Next',
    'asset_verification_upload_id_title': 'Please submit your ID',
    'asset_verification_upload_id_description': 'You can verify assets below.',
    'asset_verification_id_card': 'ID card',
    'asset_verification_file_format_info': 'JPEG / PNG • Max 10 MB',
    'asset_verification_upload_file': 'Upload file',
    'asset_verification_id_submission_guidelines': '[ID Submission Guidelines]',
    'asset_verification_guideline_clear_image':
        'Clear Image: Please take a photo of your ID so that the entire image is clearly visible. (Be careful of reflections and shadows.)',
    'asset_verification_guideline_include_original':
        'Include Original: Please take a photo of your ID so that the edges are not cut off.',
    'asset_verification_guideline_masking':
        'Masking: Please mask the last 7 digits of your resident registration number and passport number to protect your personal information.',
    'asset_verification_guideline_updated_info':
        'Updated Information: Please submit a valid ID. (Check for expiration)',
    'asset_verification_select_assets_title': 'Please list your assets',
    'asset_verification_select_assets_description':
        'You can register multiple assets for each asset type.',
    'asset_verification_real_estate_desc': 'House, Land, Building',
    'asset_verification_financial_assets_desc':
        'Deposits, savings, stocks, bonds, etc.',
    'asset_verification_crypto_currency_desc': 'Bitcoin, Ethereum, etc.',
    'asset_verification_other_assets': 'Other assets',
    'asset_verification_other_assets_desc': 'Art work, Car, etc.',
    'asset_verification_list_of_added_assets': 'List of added assets',
    'asset_verification_tap_to_add_asset': 'Tap to add this asset type',
    'asset_verification_remove_asset': 'Remove',
    'asset_verification_asset_name': 'Asset Name',
    'asset_verification_document_of_proof': 'Document of Proof',
    'asset_verification_selected_files': 'Selected Files',
    'asset_verification_description_optional': 'Description (optional)',
    'asset_verification_description_placeholder':
        'Please provide any additional information that would be helpful in assessing the value of your asset.',
    'asset_verification_document_upload_guidelines':
        '[Document Upload Guidelines]',
    'asset_verification_real_estate_name_placeholder':
        'e.g. Gangnam-gu Apartment',
    'asset_verification_financial_assets_name_placeholder':
        'e.g. Stocks held in Shinhan Securities account',
    'asset_verification_crypto_currency_name_placeholder':
        'e.g. Bithumb Bitcoin',
    'asset_verification_other_assets_name_placeholder':
        'e.g. Artwork by a famous artist',
    'asset_verification_real_estate_document_desc':
        'Record of Title(Real estate registration copy)',
    'asset_verification_financial_assets_document_desc':
        'Screenshot showing the balance\nScreenshot showing owner information',
    'asset_verification_crypto_currency_document_desc':
        'e.g. wallet screenshot, exchange statement, transaction history',
    'asset_verification_other_assets_document_desc':
        'Documents that can prove ownership and value\ne.g. sales contract, registration certificate, warranty, appraisal report, etc.',
    'asset_verification_real_estate_guideline_1':
        'Please upload an original copy of the registry (including cancellations) issued within the last 3 months by scanning or taking a photo.',
    'asset_verification_real_estate_guideline_2':
        'All sides must be clearly visible and the information must be identifiable.',
    'asset_verification_real_estate_guideline_3':
        'Please mask any personal identification information such as your resident registration number before submitting.',
    'asset_verification_financial_assets_guideline_1':
        'Please take high-resolution photos so that information can be identified. Multiple files can be uploaded.',
    'asset_verification_financial_assets_guideline_2':
        'All sides must be clearly visible and the information must be identifiable.',
    'asset_verification_financial_assets_guideline_3':
        'Please mask any personal identification information such as your resident registration number before submitting.',
    'asset_verification_crypto_currency_guideline_1':
        'Please take high-resolution photos so that information can be identified. Multiple files can be uploaded.',
    'asset_verification_crypto_currency_guideline_2':
        'All sides must be clearly visible and the information must be identifiable.',
    'asset_verification_crypto_currency_guideline_3':
        'Please mask any personal identification information such as your resident registration number before submitting.',
    'asset_verification_other_assets_guideline_1':
        'Please take high-resolution photos so that information can be identified. Multiple files can be uploaded.',
    'asset_verification_other_assets_guideline_2':
        'All sides must be clearly visible and the information must be identifiable.',
    'asset_verification_other_assets_guideline_3':
        'Please mask any personal identification information such as your resident registration number before submitting.',
    'asset_verification_common_guideline_ownership':
        'If the asset is not owned by you, please attach proof: Owned by a corporation (Certificate of History of Incorporation) Owned by a spouse (Certificate of Relations). If there are multiple, you only need to attach one.',
    'asset_verification_document_upload_guidelines_title':
        '[Document Upload Guidelines]',
    'asset_verification_denial_reasons_title':
        'The following assets may be denied.',
    'asset_verification_denial_reason_info_inconsistency':
        'Information inconsistency: Authentication may be rejected if the input information and submitted documents do not match, or if information cannot be verified.',
    'asset_verification_denial_reason_title_inconsistency':
        'Title inconsistency: Authentication may be rejected for assets for which the actual owner and the documented title do not match. (Except for ownership by spouse/corporation)',
    'asset_verification_denial_reason_proof_ownership':
        'If the asset is not owned by you, please attach proof: Owned by a corporation (Certificate of History of Incorporation) Owned by a spouse (Certificate of Relations). If there are multiple, you only need to attach one.',
    'asset_verification_real_estate_denial_unregistered':
        'Unregistered assets: Real estate for which registration procedures have not been completed may be denied for certification.',
    'asset_verification_real_estate_denial_disputes':
        'Ownership disputes: Real estate with restrictions on exercising ownership due to lawsuits, seizures, etc. may be denied certification.',
    'asset_verification_real_estate_denial_other_names':
        'Assets in other people\'s names: Assets with inconsistent real estate ownership and registered names may be denied certification. (Excluding spouse/corporation ownership)',
    'asset_verification_real_estate_denial_forgery':
        'Document forgery: If there is forgery or false information in the submitted documents, certification may be denied and use of the service may be restricted.',
    'asset_verification_financial_denial_uncertain_maturity':
        'Uncertain maturity products: Financial products with unclear contract terms or significantly low liquidity may be rejected for certification.',
    'asset_verification_financial_denial_high_risk':
        'High-risk financial assets: High-risk financial assets with high value volatility or uncertain objective valuation may be rejected for certification.',
    'asset_verification_crypto_denial_unregistered_exchange':
        'Unregistered Exchange Assets: Assets that are not registered on major exchanges and therefore difficult to verify price or transaction history may be rejected for authentication.',
    'asset_verification_crypto_denial_low_liquidity':
        'Low Market Liquidity Assets: Coins/tokens that are outside the top 100 in market capitalization or have significantly low trading volumes, making objective valuation difficult, may be rejected for authentication.',
    'asset_verification_crypto_denial_extreme_volatility':
        'Extreme Volatility/Delisting Risk Assets: High-risk virtual currencies with extreme value volatility or high risk of delisting may be rejected for authentication.',
    'asset_verification_other_denial_market_uncertainty':
        'Market value uncertainty: Assets for which objective market value is difficult to estimate or for which there is no credible valuation standard may be rejected for certification.',
    'asset_verification_other_denial_personal_agreement':
        'Personal agreement value: Assets valued solely by personal agreement value that does not meet our standards may be rejected for certification.',
    'asset_verification_other_denial_unclear_ownership':
        'Unclear ownership: Assets for which ownership cannot be proven or are in dispute may be rejected for certification.',
    'asset_verification_error_no_file_selected': 'No File Selected',
    'asset_verification_error_no_file_selected_desc':
        'Please upload your ID card first',
    'asset_verification_success_assets_submitted': 'Assets Submitted',
    'asset_verification_success_assets_submitted_desc':
        'Successfully submitted @count assets',
    'asset_verification_error_no_assets_added': 'No Assets Added',
    'asset_verification_error_no_assets_added_desc':
        'Please add at least one asset',
    'asset_verification_success_file_selected': 'File Selected',
    'asset_verification_success_file_selected_desc':
        'File @filename selected successfully',
    'asset_verification_error_file_selection': 'Error',
    'asset_verification_error_file_selection_desc':
        'Failed to select file: @error',
    'asset_verification_success_files_selected': 'Files Selected',
    'asset_verification_success_files_selected_desc': 'Selected @count file(s)',
    'asset_verification_success_files_added_desc':
        'Added @count file(s), total: @total files',
    'asset_verification_info_files_already_exist': 'Files Already Exist',
    'asset_verification_info_files_already_exist_desc':
        'All selected files already exist in this asset',
    'asset_verification_error_files_selection_desc':
        'Failed to select files: @error',
    'asset_verification_error_asset_name_required': 'Please enter asset name',
    'asset_verification_error_file_required': 'Please select at least one file',
    'asset_verification_success_asset_added': 'Asset Added',
    'asset_verification_success_asset_added_desc': 'Successfully added @name',
    'asset_verification_success_asset_removed': 'Asset Removed',
    'asset_verification_success_asset_removed_desc': 'Removed @name',
    'asset_verification_asset_type_real_estate': 'Real Estate',
    'asset_verification_asset_type_financial_assets': 'Financial Assets',
    'asset_verification_asset_type_crypto_currency': 'Crypto Currency',
    'asset_verification_asset_type_other_assets': 'Other Assets',
    'asset_verification_default_asset_name_real_estate':
        'Real Estate Property @index',
    'asset_verification_default_asset_name_financial':
        'Financial Portfolio @index',
    'asset_verification_default_asset_name_crypto': 'Crypto Wallet @index',
    'asset_verification_default_asset_name_other': 'Other Asset @index',
    'asset_verification_add_asset': 'Add Asset',
    'asset_verification_add_new_asset': 'Add New Asset',
    'asset_verification_new_asset': 'New Asset',
    'asset_verification_form_complete': 'Complete',
    'asset_verification_form_incomplete': 'Incomplete',
    'asset_verification_error_no_valid_assets': 'No Valid Assets',
    'asset_verification_error_no_valid_assets_desc':
        'Please complete at least one asset form before saving.',
    'asset_verification_success_assets_saved': 'Assets Saved',
    'asset_verification_success_assets_saved_desc':
        'Successfully saved @count @type assets.',
    'asset_verification_uploading_assets': 'Uploading Assets...',
    'asset_verification_uploading_file': 'Uploading file',
    'asset_verification_submitting_assets': 'Submitting Assets...',
    'asset_verification_confirming_assets': 'Confirming Assets...',
    'asset_verification_add_real_estate': 'Add Real Estate',
    'asset_verification_add_financial_asset': 'Add Financial Asset',
    'asset_verification_add_crypto_asset': 'Add Crypto Asset',
    'asset_verification_add_other_assets': 'Add Other Assets',
    'asset_verification_add_more_files': 'Add More',
    'asset_verification_existing_files': 'Existing Files',
    'asset_verification_uploaded_file': 'Already uploaded',
    'asset_verification_error_cannot_delete_existing_file': 'Cannot Delete',
    'asset_verification_error_cannot_delete_existing_file_desc':
        'Cannot delete files from existing assets',
    'asset_verification_error_cannot_delete_existing_asset': 'Cannot Delete',
    'asset_verification_error_cannot_delete_existing_asset_desc':
        'Cannot delete existing assets',
    'asset_verification_confirmation_title': 'Submit for asset verification',
    'asset_verification_confirmation_description':
        'Please check added assets. Can\'t add assets or document after submitted.',
    'asset_verification_agreement_text':
        'I Agree to Starchex\'s asset verification service',
    'asset_verification_submit': 'Submit',
    'asset_verification_error_agreement_required': 'Agreement Required',
    'asset_verification_error_agreement_required_desc':
        'Please agree to the asset verification service terms',
    'asset_verification_under_review_title': 'Assets Under Review',
    'asset_verification_under_review_description':
        'Your asset verification documents have been successfully submitted and are currently under review by our team.',
    'asset_verification_under_review_info_title': 'Review Process',
    'asset_verification_under_review_info_description':
        'Our verification team will review your submitted documents within 1-3 business days. You will be notified once the review is complete.',
    'asset_verification_under_review_status_submitted': 'Documents Submitted',
    'asset_verification_under_review_status_submitted_desc':
        'Successfully uploaded',
    'asset_verification_under_review_status_reviewing': 'Under Review',
    'asset_verification_under_review_status_reviewing_desc': 'In progress...',
    'asset_verification_under_review_status_complete': 'Verification Complete',
    'asset_verification_under_review_status_complete_desc': 'Pending',
    'asset_verification_back_to_home': 'Back to Home',
    'asset_verification_success_title': 'Asset Verification',
    'asset_verification_success_my_certificate': 'My Certificate',
    'asset_verification_success_received_certificate': 'Received Certificate',
    'asset_verification_success_certificate_title':
        'StarChecks Asset Certificate',
    'asset_verification_success_valid_until': 'Valid Until',
    'asset_verification_success_verification_text':
        'StarChecks has verified and issued this asset certificate.Counterfeit/forgery is not allowed and will be strictly managed.',
    'asset_verification_success_share': 'Share',
    'asset_verification_success_qr_scan': 'QR Scan',
    'asset_verification_success_share_title': 'Share Certificate',
    'asset_verification_success_share_desc':
        'Share functionality will be implemented soon.',
    'asset_verification_success_qr_title': 'QR Code',
    'asset_verification_success_qr_desc':
        'QR code display will be implemented soon.',
    'asset_verification_success_certificate_number': 'Certificate Number',
    'asset_verification_success_asset_range': 'Asset Range',
    'asset_verification_success_issue_date': 'Issue Date',
    'asset_verification_success_expiry_date': 'Expiry Date',
    'asset_verification_success_name': 'Name',
    'asset_verification_success_occupation': 'Occupation',
    'asset_verification_success_age_group': 'Age Group',
    'asset_verification_success_nationality': 'Nationality',
    'asset_verification_success_gender': 'Gender',
    'asset_verification_success_five_star_member': '5-Star Member',
    'asset_verification_success_verification_message':
        'StarChecks Asset Certificate is\nverified and guaranteed by experts.',
    'asset_verification_success_tax_accountant': 'Tax Accountant',
    'asset_verification_success_lawyer': 'Lawyer',
    'asset_verification_success_accountant': 'Accountant',
    'asset_verification_success_footer_text':
        'This certificate is issued based on the results verified through StarChecks\' own examination. The accuracy and authenticity of fake or asset information are directly confirmed and guaranteed by StarChecks.',
    'asset_verification_success_share_coming_soon':
        'Share functionality coming soon',
    'asset_verification_success_default_name': 'Yu Sujin',
    'asset_verification_success_default_occupation':
        'Hawaiian Airlines Korea Branch Manager',
    'asset_verification_success_default_age_group': 'Early 40s',
    'asset_verification_success_default_nationality': 'South Korea, USA',
    'asset_verification_success_default_gender': 'Female',
    'asset_verification_success_default_asset_range': '10-30 billion',
    'asset_verification_success_default_issue_date': '2025. 7. 10',
    'asset_verification_success_default_expiry_date': '2027. 7. 10',
    'asset_verification_success_default_certificate_number': 'No. 14852301',
    'asset_verification_received_certificate_empty':
        'No received certificates yet',
    'asset_verification_received_certificate_count': 'certificates',
    'asset_verification_qr_scanner_title': 'QR Scanner',
    'asset_verification_qr_scanner_instruction':
        'Align the QR code within the frame to scan',
    'asset_verification_qr_scanner_continue': 'Continue Scan',
    'asset_verification_qr_scanner_use_data': 'Use This Data',
    'asset_verification_qr_scanner_position_hint':
        'Position the QR code in the frame above',
    'asset_verification_qr_scanner_scanned_data': 'Scanned Data:',
    'asset_verification_qr_scanner_success': 'QR Code scanned successfully!',
    'asset_verification_qr_scanner_failed': 'Scan failed',
    'asset_verification_qr_scanner_permission_denied':
        'Camera permission denied',
    'asset_verification_qr_scanner_start_failed': 'Failed to start QR scanner',
    'common_finance_status': 'Financial status',
    'common_search': 'Search',
    'common_reject': 'Reject',
    'common_rejected_reason': 'Rejected reason',
    'common_reject_popup_title': 'Confirm Rejection',
    'common_reject_popup_subtitle_first':
        'Are you sure you want to reject the heart',
    'common_reject_popup_subtitle_last': 'gave you?',
    'common_reject_popup_content':
        'Once rejected, he/she will no longer be able to send heart to you or match with you.',
    'common_reject_popup_input_hint': 'Please enter a reason (required)',
    'common_continue': 'Continue',
    'common_done': 'Done',
    'common_cancel': 'Cancel',
    'common_save': 'Save',
    'common_back': 'Back',
    'common_refresh': 'Refresh',
    'common_close': 'Exit',
    'common_next': 'Next',
    'common_monday': 'Mon',
    'common_tuesday': 'Tue',
    'common_wednesday': 'Wed',
    'common_thursday': 'Thu',
    'common_friday': 'Fri',
    'common_saturday': 'Sat',
    'common_sunday': 'Sun',
    'common_send': 'Send',
    'common_resend': 'Resend',
    'common_verify': 'Verify',
    'common_reverify': 'Reverify',
    'common_edit': 'Edit',
    'common_add': 'Add',
    'common_preview': 'Preview',
    'common_confirm': 'Confirm',
    'common_ok': 'OK',
    'common_successful': 'Success',
    'common_heart': 'Heart',
    'common_balance': 'Balance',
    'common_double_heart': 'Double heart',
    'common_double_heart_up': 'DoubleHeart',
    'common_item_required_msg': 'This is an invalid field.',
    'common_from_photos_album': 'Photo Library',
    'common_from_photos_documents': 'Choose Files',
    'common_file_too_large_content':
        'The imported file is too large to upload.',
    'common_file_do_not_support_type': 'This file type is not supported.',
    'common_details': 'Details',
    'common_total': 'Total',
    'common_buy': 'Buy',
    'common_price': 'Price',
    'common_report': 'Report',
    'common_block': 'Block',
    'common_block_popup_title': 'Are you sure you want to block this person?',
    'common_block_popup_content':
        'When blocked, you will not be able to match or chat with each other. And you can find him/her and unblock him/her in my page block list.',
    'common_chat': 'Chat',
    'common_matched': 'Matched',
    'common_copy': 'Copy',
    'common_copy_finish_tip': 'Copied successfully.',
    'common_skip': 'Skip',
    'common_today_cards': 'Today\'s Cards',
    'common_got_hearts': 'Got Hearts',
    'common_sent_hearts': 'Sent Hearts',
    'common_given_cards': 'Given Cards',
    'common_passed_cards': 'Passed Cards',
    'common_evaluation_title': 'Who gave me high evaluation',
    'common_locked': 'Locked',
    'common_register_back_title': 'Will you go back to previous step?',
    'common_register_back_content':
        'Will you go back to previous step? If you go back, your current input data will be reset.',
    'common_net_error': 'Network error, please try again later.',
    'common_bottom_btn_today': 'Today',
    'common_bottom_btn_passed': 'Passed',
    'common_bottom_btn_community': 'Community',
    'common_bottom_btn_chat': 'Chat',
    'common_bottom_btn_me': 'Me',
    'common_accept': 'Accept',
    'common_try_again': 'Unknown error, please try again later.',
    'common_remove': 'Remove',
    'common_remove_popup_title':
        'chat history and matched partner will be removed in your matched list, you can still unblock partner in settings.',
    'common_account_kickoff_tip':
        'The account has been logged in on other devices.',
    'permission_photo_galley_alert_title':
        '\'Starchex\' would like to access your photos',
    'permission_photo_galley_alert_content':
        'Access to your photo library is required to browse and edit photos.',
    'permission_camera_alert_title':
        '\'Starchex\' would like to access your camera',
    'permission_camera_alert_content':
        'Access to your camera is required to record videos.',
    'permission_mic_alert_title':
        '\'Starchex\' would like to access your microphone',
    'permission_mic_alert_content':
        'Access to your microphone is required to record videos.',
    'time_just_now': 'Just Now',
    'time_ago': 'ago',
    'time_minutes_ago': 'minute',
    'time_hours_ago': 'hour',
    'time_days_ago': 'day',
    'time_months_ago': 'month',
    'time_years_ago': 'year',
    'splash_title': 'Creating friends from college',
    'splash_start': 'Get start',
    'splash_learn': 'Learn',
    'sign_in_title': 'Hi,  Welcome to Starchex !',
    'sign_in_zalo': 'Sign up with Zalo',
    'sign_in_google': 'Sign up with Google',
    'sign_in_facebook': 'Sign up with Facebook',
    'sign_in_apple': 'Sign up with Apple ID',
    'agreement_title': 'Agree with all terms and conditions',
    'agreement_accept_all_title': 'I agree with all terms and conditions',
    'agreement_age_title': '18 years of age or older (Required)',
    'agreement_terms_title': 'Terms and conditions (Required)',
    'agreement_personal_info_title': 'Privacy policy (Required)',
    'agreement_promotional_title': 'Consent to marketing (Optional)',
    'agreement_safety_and_policy_center': 'Safety and Policy Center (Required)',
    'agreement_see_detail': ' View More',
    'personal_basic_info_nationality': 'Nationality',
    'personal_basic_info_nationality_hint': 'Select your nationality',
    'personal_basic_info_please_enter_your_nationality':
        'Please enter your nationality',
    'personal_basic_info_sign_up': 'Sign up',
    'personal_basic_info_title': 'Please input your information.',
    'personal_basic_info_subtitle':
        'You can modify your information later on profile page, after you successfully register.',
    'personal_basic_info_nick_name': 'Nick name',
    'personal_basic_info_nick_name_PH':
        'This is the nick name you see on Starchex',
    'personal_basic_info_date_birth': 'Date of birth',
    'personal_basic_info_date_birth_PH': 'Please select your date of birth',
    'personal_basic_info_region': 'Region',
    'personal_basic_info_region_PH': 'Select your region',
    'personal_basic_info_gender': 'Gender',
    'personal_basic_info_female': 'Female',
    'personal_basic_info_male': 'Male',
    'personal_basic_info_phone_number': 'Phone number',
    'personal_basic_info_phone_number_PH': 'Please enter your phone number',
    'personal_basic_info_verification_code_PH':
        'Please enter the verification code',
    'personal_basic_info_phone_number_invalid_msg':
        'Mobile Number validate failed.',
    'personal_basic_info_phone_number_need_verify': 'Verification required',
    'personal_basic_info_verification_code_sent_msg':
        'The Verification code has been sent.',
    'personal_basic_info_verification_code_valid_msg':
        'Success! Your mobile number has been verified.',
    'personal_basic_info_verification_code_invalid_msg':
        'Please enter a valid verification code.',
    'personal_basic_info_nick_name_exist_tip':
        'This nickname has been taken, please input others.',
    'personal_basic_info_email_PH': 'Please enter your University Email.',
    'personal_basic_info_location_tip':
        'This data is used for local partner matching.',
    'personal_basic_info_phone_input_tip':
        'Starchex customer service team will contact you verify this is your actual phone number.',
    'personal_basic_info_email_send_tip':
        'If you do not see the email in a few minutes, check your "junk mail" folder or "spam" folder.',
    'personal_basic_info_saving': 'Saving',
    'university_info_page_title': 'Verification Information',
    'university_info_title': 'Verify your university.',
    'university_info_subtitle':
        'Don\'t worry! Your real name is just used as verification and not exposed to the service.',
    'university_info_financial_verify_title': 'Verify your wealthy',
    'university_info_financial_verify_subtitle':
        'Don\'t worry! Your financial status is just used as verification and not exposed to the service.',
    'university_info_name': 'Name',
    'university_info_name_PH': 'Real name',
    'university_info_university': 'University',
    'university_info_university_PH': 'Select your university',
    'university_info_verify_your_wealthly': 'Verify your wealthy',
    'university_info_verify_your_wealthly_hint': 'My financial status is',
    'university_info_financial_status_doNotAuthorize': 'I do not authorize',
    'university_info_financial_status_submitted': 'I have submitted',
    'university_info_financial_status_moreThan100K':
        'I have asset more than 100K USD',
    'university_info_financial_status_moreThan500K':
        'I have asset more than 500K USD',
    'university_info_financial_status_moreThan1M':
        'I have asset more than 1M USD',
    'university_info_financial_status_moreThan5M':
        'I have asset more than 5M USD',
    'university_info_financial_status_moreThan10M':
        'I have asset more than 10M USD',
    'university_info_financial_status_moreThan50M':
        'I have asset more than 50M USD',
    'university_info_financial_status_moreThan100M':
        'I have asset more than 100M USD',
    'university_info_auth_methods': 'Auth Methods',
    'university_info_verify_email': 'Verify by Email',
    'university_info_verify_email_note':
        'Starchex is available immediately upon your verification.',
    'university_info_verify_cert_paper': 'Verify by Cert. paper',
    'university_info_verify_cert_paper_note':
        'Starchex is available after Starchex\'s confirmation within 24 hours usually.',
    'verify_email_subtitle':
        'Please input the email address of the university you have chosen.',
    'verify_email_input_PH': 'Please enter the verification code',
    'verify_email_note':
        'An email with a verification code has been sent at @interval.',
    'verify_email_code_valid_msg': 'Success! Your email has been verified.',
    'verify_cert_paper_subtitle':
        'Please upload your student ID photos or graduate cert.  of the university you have chosen.',
    'verify_cert_paper_input_PH':
        'Upload student ID photos, graduate certificate, or proof of financial status.',
    'verify_cert_paper_note1':
        'Starchex reviews the user\'s asset details to grant verification badges. By submitting your verification documents to Starchex, you signify your consent for Starchex to collect your sensitive personal information. Starchex will use your information solely for the purpose of asset verification and will securely delete your data immediately after verification.',
    'verify_cert_paper_note2':
        'Please prepare and submit various documents to verify your financial status. You can provide documents such as bank account balances and real estate registration certificates according to your situation and nationality. Ensure that your sensitive personal information (e.g., unique passport numbers) is masked before submission.',
    'verify_cert_paper_note3':
        'Starchex will review your documents and grant an asset verification badge. If additional documents are needed, we may request them through in-app messages.',
    'verify_cert_paper_note4':
        'When uploading documents for school certification or property certification, please upload clear images so that Starchex can accurately identify them.',
    'verify_cert_paper_submit_to_verify': 'Submit to verify',
    'submit_photos_page_title': 'Interests',
    'submit_photos_title': 'Please register your photo.',
    'submit_photos_subtitle':
        'Your representative photo will be exposed on the main page of your profile. Please upload a photo that shows your face and full body (no limit on the number of photos)',
    'interesting_title': 'Please select your hobbies.',
    'interesting_subtitle':
        'Find people who share your interests! Add up to 5 items.',
    'interesting_input_tip': 'Can\'t find your hobbies? Please input.',
    'introduce_title': 'Please register freely to introduce yourself.',
    'introduce_subtitle':
        'The first three lines are directly exposed on inside the card.',
    'introduce_input_PH': 'Input your information',
    'introduce_note1':
        'Information recorded that does not match the facts will be subject to criminal penalties.',
    'introduce_note2': 'If there are any issues, please correct them.',
    'signUp_completion_title': '@name,\nWelcome to Starchex!',
    'signUp_completion_subtitle':
        'Now, every hour from 9:00 am, meet a wonderful partner delivered by Starchex.',
    'signUp_completion_phone_verification_title': 'Phone Verification',
    'signUp_completion_phone_verification_subtitle':
        'Please verify your phone number to continue.',
    'signUp_completion_pending_verification_title': 'Pending Verification',
    'signUp_completion_pending_verification_subtitle':
        'Please wait a moment! \nYour information will be verified by Starchex in the next 24 hours.',
    'signUp_completion_pending_verification_subtitle_2':
        'If you update your attestation, your account will be pending until the admin approves it. Would you like to update it?',
    'signUp_completion_failed_verification_title': 'Verification failed',
    'signUp_completion_failed_verification_subtitle':
        'Sorry, we couldn\'t verify student ID photos or graduate cert. \nPlease re-upload the photo and verify.',
    'signUp_completion_back_verify': 'Back to Verify',
    'profile_about_me': 'About me',
    'profile_interests': 'Interests',
    'profile_evaluate_card': 'Evaluate the card',
    'profile_rate_to_get_mc': 'Evaluate the card and you will get MeeCoin',
    'profile_rating_popup_title': 'Evaluate the card',
    'profile_rating_popup_subtitle':
        'You haven\'t evaluate the partner, are you sure you want to quit?',
    'profile_to_rate': 'To rate',
    'profile_title': 'Profile',
    'profile_rating_result_tip':
        'you have got @amount MeeCoin for evaluating partners',
    'profile_send_heart_message':
        'You have sent a heart to @name. It\'s a good start!',
    'profile_send_double_heart_message':
        'You have sent a double heart to @name. It\'s a good start!',
    'profile_heart_operation_tip_one': 'You got Heart from ',
    'profile_heart_operation_tip_two': 'Send your Heart to be matched.',
    'profile_doubleHeart_operation_tip_one': 'You got DoubleHeart from ',
    'profile_doubleHeart_operation_tip_two':
        'If you Accept this, you will be matched.',
    'mee_coin_meecoin': 'MeeCoin',
    'mee_coin_available_meecoin': 'Available MeeCoin',
    'mee_coin_recharge': 'Recharge',
    'mee_coin_transaction': 'Transaction',
    'mee_coin_mct_unlock_given_card': 'Unlock Given card',
    'mee_coin_mct_unlock_high_eva': 'Unlock High evaluation Card',
    'mee_coin_mct_unlock_heart_card': 'Unlock Heart card',
    'mee_coin_mct_send_heart': 'Sent Heart',
    'mee_coin_mct_send_double_heart': 'Sent Double Heart',
    'mee_coin_mct_buy_premium': 'Buy Premium cards',
    'mee_coin_mct_eva': 'Evaluate',
    'mee_coin_mct_double_heart_refund': 'Double heart payback',
    'mee_coin_mct_recharge': 'Recharge',
    'mee_coin_mct_ref2_bonus': 'Recommended subscriber',
    'mee_coin_mct_ref1_bonus': 'Recommender',
    'mee_coin_mct_pre_reg': 'Pre-subscribers',
    'mee_coin_mct_deposit': 'System MeeCoin Reward',
    'mee_coin_mct_deduction': 'System MeeCoin Deduct',
    'mee_coin_mct_top_post': 'Top post',
    'mee_coin_recharge_title': 'Please select the recharge \n amount.',
    'mee_coin_recharge_subtitle':
        'The more recharge amount, \nthe bigger discount!',
    'mee_coin_practical': 'Practical',
    'mee_coin_popular': 'Popular',
    'mee_coin_bonus_rate': 'Added value @ratePercent',
    'mee_coin_bonus_rate_title': 'Added value',
    'mee_coin_vnd': 'VNĐ',
    'mee_coin_bonus': 'Bonus',
    'mee_coin_recharge_amount': 'Recharge amount',
    'mee_coin_pay_failed': 'Pay Failed',
    'mee_coin_pay_invalid': 'Pay Invalid',
    'today_cards_recharge_title': 'Please select the recharge \n amount.',
    'today_cards_recharge_subtitle':
        'The more recharge amount,  \nthe bigger discount!',
    'passed_cards_empty_msg':
        'Passed cards show the cards in the past 7 days.\nYou don\',t have any more cards.\nTap |Today\'s| cards to unlock more cards.',
    'unlock_balance': 'Balance',
    'unlock_recharge': 'Recharge',
    'unlock_cards': '@number cards',
    'unlock_premium_cards': 'Premium Cards',
    'unlock_premium_subtitle_1': 'Unlock @number cards at once.',
    'unlock_premium_subtitle_2': 'Three high-quality partners for you.',
    'unlock_premium_subtitle_3': 'Match them immediately!',
    'unlock_note':
        'By tapping Continue, you will be charged, \nand you agree to our |Terms|.',
    'unlock_unlock_single_heart_title': 'Unlock Heart card',
    'unlock_unlock_single_heart_subtitle': 'See who sent you an Heart card.',
    'unlock_unlock_passed_given_card_title': 'Unlock Given Card',
    'unlock_unlock_passed_given_card_subtitle': 'Unlock passed Given card.',
    'unlock_unlock_high_eva_title': 'Unlock High evaluation Card',
    'unlock_unlock_high_eva_subtitle': 'Who gave you high evaluation.',
    'unlock_send_single_heart_title': 'Send heart card',
    'unlock_send_single_heart_subtitle':
        'After sending the Heart card, the Heart card arrival notification will be sent to the partner through push and SMS.',
    'unlock_send_double_heart_title': 'Send Double Heart card',
    'unlock_send_double_heart_subtitle':
        'Sending Double Heart card. The receiving partner can accept it without paying Heart. If failed, +20Meecoin would be paid back.',
    'unlock_not_enough_balance': 'Not enough balance',
    'unlock_not_enough_balance_popup_content':
        'Your meecoin balance is not enough, please recharge.',
    'unlock_no_profile_popup_title': 'Finish Profile',
    'unlock_no_profile_popup_content': 'Need to finish your profile.',
    'unlock_no_profile_popup_ok_button': 'Go to profile',
    'subscription_buy': 'Buy',
    'subscription_normal_heart_name': 'One Time',
    'subscription_normal_heart_description': 'Send heart card one time.',
    'subscription_double_heart_name': 'Double Heart card',
    'subscription_double_heart_description': 'More likely to get matched!',
    'subscription_tip': 'Compare Heart volumes',
    'chat_common_hey_there': 'Hey there',
    'chat_common_everyone': 'everyone',
    'chat_common_no_recent_emoji': 'No Recent Emoji',
    'chat_common_read': 'Read',
    'chat_common_say_hi': 'Say Hello',
    'chat_common_empty_tip': 'Tap on a new match above to send \n a message.',
    'chat_common_conversation_empty_tip':
        'No chat yet.\n Tap |Today\'s cards| to unlock more cards.',
    'chat_common_today_btn': 'Go to Today\'s cards',
    'chat_common_typing_tip': 'Typing...',
    'chat_common_synchronizing': 'synchronizing...',
    'chat_common_syncFailed': 'syncFailed',
    'chat_common_connecting': 'connecting...',
    'chat_common_connectionFailed': 'connectionFailed',
    'chat_messageType_picture': 'Picture',
    'chat_messageType_video': 'Video',
    'chat_messageType_voice': 'Voice',
    'chat_messageType_file': 'File',
    'chat_messageType_emoji': 'Emoji',
    'chat_messageType_unsupportedMessage': 'Unknown Message',
    'chat_time_now': 'Just Now',
    'chat_time_justNow': 'Just Now',
    'chat_time_ago': 'ago',
    'chat_time_minutes_ago': 'minute',
    'chat_time_hours_ago': 'hour',
    'chat_time_days_ago': 'day',
    'chat_time_months_ago': 'month',
    'chat_time_years_ago': 'year',
    'chat_time_count_minute': 'minute',
    'chat_time_count_hour': 'hour',
    'chat_time_date_time_month': '',
    'chat_time_date_time_day': '',
    'chat_empty_matched_info1': 'and You are matched.',
    'chat_empty_matched_info2': 'Let\'s talk about something interesting.',
    'chat_sys_msg_remove_black': 'User removed from black list.',
    'compare_heart_item': 'Item',
    'compare_heart_row1': 'Partner can accept it for free',
    'compare_heart_row2': 'Show received at the top',
    'compare_heart_row3': 'Special push & SMS message notifications',
    'compare_heart_row4': 'Compensation for failure',
    'compare_heart_row4_double_heart': '20Meecoin',
    'compare_heart_row5': 'Improved matching success rate',
    'compare_heart_row5_double_heart': '4 times bigger chance',
    'my_page_title': 'My page',
    'my_page_about': 'About Starchex',
    'my_page_help_center': 'Help Center',
    'my_page_feedback': 'Feedback',
    'my_page_terms_conditions': 'Terms & Conditions',
    'my_page_privacy_policy': 'Privacy Policy',
    'my_page_safety_and_policy_center': 'Safety and Policy Center',
    'my_page_settings': 'Settings',
    'my_page_market': 'Shop MeeCoin',
    'my_page_referrer_tip': 'Your referral code',
    'my_page_available_text': 'Available',
    'my_page_logout_btn': 'Log out',
    'my_page_referrer_pop_title': 'Refer friends & Earn',
    'my_page_referrer_pop_content':
        'Ask your friends to sign up with your referral code. Once done, both you and your friend each earn',
    'my_page_invitation': 'Input Referrer code',
    'my_page_invitation_menu_tip': 'Earn Meecoin +100',
    'my_page_pending_profile_tip': 'Verification in progress.',
    'my_page_finish_profile_tip': 'Please complete your information.',
    'settings_title': 'Settings',
    'settings_manage_black_list': 'Manage blocked accounts',
    'settings_quit': 'Delete account',
    'settings_blocklist': 'Block List',
    'settings_unblock': 'Unlock',
    'settings_language': 'Language',
    'settings_english': 'English',
    'settings_korean': 'Korean',
    'report_main_title': 'Are you sure you want to report this person?',
    'report_sub_title': 'Please select a reason for reporting.',
    'report_reason1': 'Fake profile',
    'report_reason2': 'Rude or abusive behavior',
    'report_reason3': 'Inappropriate content',
    'report_reason4': 'Scam or commercial ',
    'report_reason5': 'Identity-based hate',
    'report_reason6': 'Others (Direct entry)',
    'report_other_placeholder': 'Please enter in 100 characters or less.',
    'report_image_tip':
        'Please upload a photo related to the report (required)',
    'report_image': 'Image',
    'report_upload_tip':
        'You can attach up to 5 (60MB) related photos in JPG or PNG format.',
    'report_report_tip':
        'The fact of the report is not disclosed to the other party.',
    'report_report_btn': 'Report',
    'report_file_empty_tip': 'Please select a file',
    'edit_profile_upload_documents': 'Upload documents',
    'edit_profile_update_my_attestation': 'Manage my attensation',
    'edit_profile_support_document': 'Support document',
    'edit_profile_support_document_tip':
        'Upload documents that can verify your educational background and assets.',
    'edit_profile_enable_discovery': 'Enable Discovery',
    'edit_profile_enable_discovery_note':
        'When turned off, your profile will be hidden from the card stack and discovery will be disabled. People you have already matched may still see and chat with you.',
    'edit_profile_enable_discovery_alert_title': 'Please enter required items',
    'edit_profile_enable_discovery_alert_content':
        'Sorry, you have to finish or update your profile firstly, then you can enable discovery',
    'edit_profile_photo_title': 'Photo',
    'edit_profile_address_title': 'Address',
    'edit_profile_interests_title': 'Interests',
    'edit_profile_aboutme_title': 'About me',
    'edit_profile_photo_avatar_require': 'Please upload an avatar.',
    'edit_profile_interests_require': 'Please choose interests.',
    'edit_profile_introduction_require': 'Please enter introduction.',
    'edit_profile_unsaved_change_title': 'You have unsaved changes!',
    'edit_profile_unsaved_change_content':
        'You have unsaved changes.\nPlease complete the changes and save.',
    'edit_profile_leave': 'Leave',
    'edit_profile_stay': 'Stay',
    'edit_profile_attestation': 'Attestation',
    'edit_profile_basic_information': 'Basic information',
    'edit_profile_others': 'Others',
    'edit_profile_verify_university': 'Verify university',
    'edit_profile_university_not_certified': 'Not certified',
    'edit_profile_university_pending': 'Certificating',
    'edit_profile_university_rejected': 'Rejected',
    'edit_profile_university_certified': 'Certified',
    'edit_profile_tap_to_edit_tip': 'Tap to edit',
    'edit_profile_edit_save_tip_title': 'Save profile',
    'edit_profile_edit_save_tip_content':
        'Are you sure you want to change your profile? This needs pending verification.',
    'edit_profile_university_name_required': 'Please input your real name',
    'edit_profile_university_university_required':
        'Please select your university',
    'edit_profile_university_student_id_required':
        'Please upload your student ID card or graduate Cert. Paper',
    'edit_profile_university_email_required':
        'Please input your university email address',
    'edit_profile_university_v_code_required':
        'Please enter the verification code from email',
    'edit_profile_profile_university_required':
        'Please finish your university verification',
    'edit_profile_profile_nickname_required': 'Please input your nick name',
    'edit_profile_profile_birth_required': 'Please select your date of birth',
    'edit_profile_profile_region_required': 'Please select your region',
    'edit_profile_profile_gender_required': 'Please select your gender',
    'edit_profile_profile_phone_required': 'Please input your phone number',
    'edit_profile_profile_photos_required': 'Please upload one photo at least',
    'edit_profile_isGraduated': 'Graduated',
    'invitation_referrer_tip': 'Referrer code',
    'invitation_referrer_input_tip':
        'You can only enter it once, please fill it in carefully.',
    'invitation_referrer_popup_content_1':
        'If you have Referrer code, please enter. Both you and your friend each earn',
    'invitation_referrer_popup_content_2': 'If not, press Skip.',
    'invitation_referrer_error_title': 'Referrer code error',
    'invitation_referrer_error_content':
        'Sorry, your referrer code is wrong and there is no new user reward.',
    'invitation_reward_popup_title': 'New User Reward',
    'invitation_reward_popup_content':
        'Welcome to Starchex ! Enjoy the new user reward MeeCoin',
    'invitation_referrer_code': 'Referrer code',
    'feedback_email_label': 'Email address to receive reply',
    'feedback_email_placeholder': 'Please enter your email address.',
    'feedback_inquiry_label': 'Inquiry details',
    'feedback_inquiry_placeholder': 'select',
    'feedback_content_placeholder':
        'Please write the content in 1000 characters or less.',
    'feedback_image_label': 'Please upload a photo related to the Feedback',
    'feedback_email_incorrect': 'Please input correct Email.',
    'feedback_content_required': 'Please input Feedback content.',
    'feedback_content_max_length': 'Content length cannot exceed 1000.',
    'quit_main_title': 'do you really want to quit?',
    'quit_main_desc':
        'Before canceling your membership, please be sure to check the information below.',
    'quit_content_title':
        'Records of contracts or subscription withdrawals, etc.',
    'quit_list_1': 'Records of contracts or subscription withdrawals, etc.',
    'quit_list_1_a':
        'Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 5 years)',
    'quit_list_2': 'Records of payment and supply of goods, etc.',
    'quit_list_2_a':
        'Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 5 years)',
    'quit_list_3': 'Records of consumer complaints or dispute resolution',
    'quit_list_3_a':
        'Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 3 years)',
    'quit_list_4': 'Records of display/advertisement',
    'quit_list_4_a':
        'Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 6 months)',
    'quit_list_5': 'Records of illegal use, etc.',
    'quit_list_5_a':
        'Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 5 years)',
    'quit_list_6': 'Service visit records – access records, IP addresses, etc.',
    'quit_list_6_a':
        'Based law: Communication Secrets Protection Act (retention period: 3 months)',
    'quit_agreement_title': 'I have confirmed all of the above. (essential)',
    'quit_reason_title':
        'Why do you want to leave Starchex?\n (Required) *Multiple selection possible',
    'quit_reason_1': 'Not used',
    'quit_reason_2': 'I want to sign up again',
    'quit_reason_3': 'Inconvenient to use',
    'quit_reason_4': 'No desired content',
    'quit_reason_5': 'Etc',
    'quit_reason_placeholder':
        'If you tell us in detail why you are leaving, it will greatly help us improve the quality of our service.',
    'quit_quit': 'Delete',
    'quit_confirm_title': 'Delete your account',
    'quit_confirm_popup':
        'If you withdraw from Starchex, all of your activity history (MeeCoin points, photos, history, etc.) will be deleted. Please keep this in mind when making your decision.Would you like to withdraw?',
    'chat_notification_title': 'Chat notification',
    'chat_notification_content': 'You have a new message. Please check it.',
    'notification_title': 'Notification',
    'community_title': 'Community',
    'community_register_tip':
        'Input your community nickname. This nickname is used for only community boards service, and it\'s  different from the nickname in the main matching service.',
    'community_fakeName_tip': 'Can\'t think of one? Use one of these:',
    'community_delete': 'Delete',
    'community_post_list_empty': 'No contents. \nLooking forward to your post.',
    'community_comments_list_empty':
        'No comments.\nLooking forward to your comment.',
    'community_likes_list_empty':
        'No likes.\nYou will become the first person to like.',
    'community_me': 'Me',
    'community_post_title': 'Create a post',
    'community_post_placeholder':
        'What\'s on your mind? (10000 characters limitation)',
    'community_post_board_label': 'Community',
    'community_post_post_btn': 'Post',
    'community_post_photo_upload_tip':
        'You can attach up to Max 10 photos (each photo < 5MB) in JPG or PNG format.',
    'community_post_content_empty_tip': 'Post content can\'t be empty',
    'community_post_deleted_post': 'This is deleted by user',
    'community_post_show_more': 'More',
    'community_post_hide_more': 'Hide',
    'community_post_see_likes': 'See likes',
    'community_post_all_comments': 'All Comments',
    'community_post_all_likes': 'All likes',
    'community_post_empty': 'This is nothing',
    'community_comment_placeholder': 'Comment...(10000 characters limitation)',
    'community_comment_more_text': 'See More Comments',
    'community_comment_end_text': 'End',
    'community_comment_mention_empty_tip':
        'Sorry, we couldn\'t find the user \${searchText}\$ \n Please check the nickname or try mentioning someone else.',
    'community_comment_mention_empty_1': 'Sorry, we couldn\'t find the user ',
    'community_comment_mention_empty_2':
        'Please check the nickname or try mentioning someone else.',
    'community_report_reason1': 'Harassment',
    'community_report_reason2': 'Threatening violence',
    'community_report_reason3': 'Hate',
    'community_report_reason4': 'Minor abuse or sexualization',
    'community_report_reason5': 'Sharing personal information',
    'community_report_reason6': 'Non-consensual intimate media',
    'community_report_reason7': 'Prohibited transaction',
    'community_report_reason8': 'Impersonation',
    'community_report_reason9': 'Copyright violation',
    'community_report_reason10': 'Trademark violation',
    'community_report_reason11': 'Self-harm or suicide',
    'community_report_reason12': 'Spam',
    'community_report_reason13': 'Others ',
    'community_profile_posts': 'Posts',
    'community_profile_likes': 'Likes',
    'community_profile_edit_profile': 'Edit profile',
    'community_profile_creat_post': 'Create a post',
    'community_profile_comments': 'Comments',
    'check_app_version_new_version_title': 'Update App',
    'check_app_version_new_version_content':
        'Starchex has improved usability by reflecting users\' opinions. Please update to the latest version for better service use.',
    'check_app_version_new_version_action': 'Go to App Store',
    'check_app_version_new_version_action_later': 'Later',
    'otp_verify_page_title': 'OTP Verification',
    'otp_verify_page_message':
        'A text message with a 6-digit verification code\nwas sent to @phone_number',
    'otp_verify_page_didnt_get_the_code': 'Didn\'t get the code?',
    'otp_verify_page_resend': 'Resend',
    'otp_verify_page_resend_after': 'Please verify the code in @time',
    'otp_verify_page_successfully': 'Successfully verified your phone number',
    'otp_verify_page_error_title': 'OTP code error',
    'sc_str_ai_avatar_error_no_croppable_avatar':
        'No AI avatar available for cropping',
    'sc_str_ai_avatar_error_web_not_supported':
        'Web version does not support avatar cropping',
    'sc_str_ai_avatar_error_data_format_error':
        'AI avatar data format error, cannot crop',
    'sc_str_ai_avatar_error_download_failed_code':
        'Failed to download AI avatar, status code',
    'sc_str_ai_avatar_error_download_failed': 'Failed to download AI avatar',
    'sc_str_ai_avatar_error_crop_success': 'Avatar cropping completed',
    'sc_str_ai_avatar_error_crop_failed': 'Failed to crop avatar',
    'sc_str_ai_avatar_error_upload_first':
        'Please upload a photo first to generate AI avatar',
    'sc_str_ai_avatar_error_generating_wait':
        'Generating AI avatar, please wait patiently, this may take 1-3 minutes...',
    'sc_str_ai_avatar_error_empty_photo_data': 'Photo data is empty',
    'sc_str_ai_avatar_error_empty_photo_file': 'Photo file is empty',
    'sc_str_ai_avatar_error_timeout':
        'AI avatar generation timed out, server processing time is too long, please try again later',
  };
  static const ko = {
    'sc_str_common_view': '보기',
    'sc_str_common_view_all': '모두보기',
    'sc_str_common_members': '참가자',
    'sc_str_common_events': '이벤트',
    'sc_str_common_comments': '댓글',
    'sc_str_common_comment_placeholder': '댓글을 작성하세요...',
    'sc_str_common_post': '게시하다',
    'sc_str_common_upload': '업로드',
    'sc_str_common_continue': '계속하기',
    'sc_str_common_publish': '게시하기',
    'sc_str_common_image': '이미지',
    'sc_str_event_intro_recommended': '추천 이벤트',
    'sc_str_home_this_week': '이번 주',
    'sc_str_home_last_week': '지난 주',
    'sc_str_home_upcoming': '예정된',
    'sc_str_home_weekly_star': '주간 스타',
    'sc_str_home_featured_events': '추천 이벤트',
    'sc_str_home_notification_content': '알림 내용',
    'sc_str_home_last_week_event_title': '지난주 이벤트',
    'sc_str_home_next_week_star_title': '✨ 다음 인터뷰이.',
    'sc_str_home_next_week_star_subtitle': '다음 주에는 |@title|님이 함께할 예정입니다.',
    'sc_str_home_look_forward': '기대해 주세요...',
    'sc_str_home_member_suffix': '회원',
    'sc_str_home_interview_article': '인터뷰 기사 보기',
    'sc_str_comment_Write_comment': '댓글 남기기',
    'sc_str_comment_your_thought': '당신의 생각을 남겨보세요',
    'sc_str_comment_all_comments': '댓글',
    'sc_str_comment_add_comment': '댓글 남기기',
    'sc_str_comment_interview_comment_placeholder':
        '✨ 인터뷰를 보고 느낀 점을 자유롭게 남겨보세요.',
    'sc_str_event_capacity_notice': '정원 초과 시, 참가자는 선정됩니다.',
    'sc_str_event_event_introduction': '이벤트 소개',
    'sc_str_event_join_application': '이벤트 참가신청',
    'sc_str_event_application_completed':
        '참가 신청이 완료되었습니다. 최종 선정 결과는 행사 2일 전 개별 안내드립니다.',
    'sc_str_event_congratulations_selected': '축하드립니다! 귀하는 이번 행사에 최종 선정되셨습니다.',
    'sc_str_event_not_selected_message':
        '이번엔 함께하지 못했지만, 다음 인연을 기대합니다. 다른 이벤트도 놓치지 마세요.',
    'sc_str_event_ended': '종료됨',
    'sc_str_event_confirmed_participants': '확정된 참가자',
    'sc_str_event_past_interviews': '지난 인터뷰',
    'sc_str_event_interview': '인터뷰',
    'sc_str_event_address': '주소',
    'sc_str_event_private_club_address': '서울 한남동 프라이빗 클럽',
    'sc_str_community_register_tip':
        '우리의 독점 커뮤니티를 위한 특별한 아이덴티티를 만드세요. 닉네임은 메인 프로필에서 비공개로 유지됩니다.',
    'sc_str_community_name_input_PH': '특별한 이름을 입력하세요',
    'sc_str_community_name_already_taken': '이 이름은 이미 사용 중입니다. 다른 이름을 시도해보세요.',
    'sc_str_community_name_select_tip': '추천 이름을 선택하거나 직접 만드세요',
    'sc_str_community_enter_circle': '원에 들어가기',
    'sc_str_community_fake_name_existed_error': '이 이름은 이미 사용 중입니다',
    'sc_str_community_craft_your_post': '게시물 작성하기',
    'sc_str_community_select_a_circle': '커뮤니티 선택하기',
    'sc_str_community_share_your_insight': '당신의 생각을 공유하세요...',
    'sc_str_community_media_upload_tip':
        '최대 10개의 고품질 이미지를 업로드하세요 (JPG/PNG, 각 5MB 이하).',
    'sc_str_ai_avatar_generating': 'AI 아바타 생성 중, 잠시만 기다려 주세요...',
    'sc_str_ai_avatar_wait_time': '1-3분 정도 소요될 수 있습니다',
    'sc_str_ai_avatar_generated': 'AI 향상된 아바타 생성됨',
    'sc_str_ai_avatar_select_again': '다시 선택',
    'sc_str_ai_avatar_crop_avatar': '아바타 자르기',
    'sc_str_ai_avatar_image_load_failed': '이미지 로딩 실패',
    'sc_str_ai_avatar_image_data_error': '이미지 데이터 오류',
    'sc_str_ai_avatar_network_image_failed': '네트워크 이미지 로딩 실패',
    'sc_str_ai_avatar_no_image': '사용 가능한 이미지 없음',
    'sc_str_ai_avatar_render_error': '아바타 렌더링 오류',
    'sc_str_ai_avatar_completed': 'AI 아바타 생성 완료',
    'sc_str_ai_avatar_generation_success':
        'AI 아바타가 성공적으로 생성되었습니다! 이제 이 아바타를 사용할 수 있습니다.',
    'sc_str_ai_avatar_generation_failed': 'AI 아바타 생성 실패',
    'sc_str_ai_avatar_generation_cancelled': 'AI 아바타 생성 취소됨',
    'sc_str_ai_avatar_cancel_generation': 'AI 생성 취소',
    'sc_str_ai_avatar_cancel_generation_confirm':
        'AI 아바타 생성을 취소하시겠습니까? 이 작업은 되돌릴 수 없습니다.',
    'sc_str_ai_avatar_regenerate': '다시 생성',
    'sc_str_ai_avatar_use_this_avatar': '이 아바타 사용',
    'sc_str_ai_avatar_set_success': 'AI 아바타 설정 성공',
    'sc_str_ai_avatar_generating_in_progress': 'AI 생성 중',
    'sc_str_ai_avatar_estimated_time_remaining': '예상 @seconds 초 남음',
    'sc_str_ai_avatar_task_restored': '진행 중인 AI 아바타 생성 작업이 감지되어 자동으로 복원되었습니다',
    'sc_str_ai_avatar_task_completed': 'AI 아바타 생성이 완료되었습니다!',
    'not_verified_yet_warning_message': '주의! 이 사용자는 아직 인증되지 않았습니다.',
    'rejected': '거부됨',
    'not_verified': '인증되지 않음',
    'university': '대학교',
    'skip_invitation_message': '가입 후 1주일 이내에 마이페이지에서 추천인 코드를 입력할 수 있습니다.',
    'input_validation_required_error_message': '이 필드는 필수입니다.',
    'badge_title': '배지',
    'badge_university_badges': '대학교 배지',
    'badge_wealth_badges': '자산 배지',
    'badge_badge_policy':
        'Starchex의 모든 대학 및 자산 인증 배지는 사용자가 제출한 적절한 증빙 서류를 기반으로 Starchex의 인증 팀에 의해 엄격하게 검증되고 수여됩니다. 이 엄격한 과정은 인증 기반 서비스의 신뢰성과 신뢰도를 보장합니다.',
    'badge_graduate': '졸업',
    'badge_graduate_tip': '사용자가 대학을 졸업했습니다.',
    'badge_top_200': '상위 200',
    'badge_top_200_tip': '사용자는 상위 200위 대학 출신입니다.',
    'badge_more_than_500k': '50만 이상',
    'badge_more_than_500k_tip': '사용자는 50만 이상의 자산을 보유하고 있습니다.',
    'badge_more_than_1m': '100만 이상',
    'badge_more_than_1m_tip': '사용자는 100만 이상의 자산을 보유하고 있습니다.',
    'badge_more_than_10m': '1000만 이상',
    'badge_more_than_10m_tip': '사용자는 1000만 이상의 자산을 보유하고 있습니다.',
    'common_finance_status': '재정 상태',
    'common_search': '검색',
    'common_reject': '거절',
    'common_rejected_reason': '거절 이유',
    'common_reject_popup_title': '거절 확인',
    'common_reject_popup_subtitle_first': '하트를 거절하시겠습니까',
    'common_reject_popup_subtitle_last': '당신에게 보낸?',
    'common_reject_popup_content':
        '한번 거절하면, 그/그녀는 더 이상 당신에게 하트를 보내거나 매칭할 수 없습니다.',
    'common_reject_popup_input_hint': '이유를 입력해 주세요 (필수)',
    'common_continue': '계속하기',
    'common_done': '완료',
    'common_cancel': '취소',
    'common_save': '저장',
    'common_back': '뒤로',
    'common_refresh': '새로고침',
    'common_close': '나가기',
    'common_next': '다음',
    'common_monday': '월',
    'common_tuesday': '화',
    'common_wednesday': '수',
    'common_thursday': '목',
    'common_friday': '금',
    'common_saturday': '토',
    'common_sunday': '일',
    'common_send': '보내기',
    'common_resend': '재전송',
    'common_verify': '인증',
    'common_reverify': '재인증',
    'common_edit': '편집',
    'common_add': '추가',
    'common_preview': '미리보기',
    'common_confirm': '확인',
    'common_ok': '확인',
    'common_successful': '성공',
    'common_heart': '하트',
    'common_balance': '잔액',
    'common_double_heart': '더블 하트',
    'common_double_heart_up': '더블하트',
    'common_item_required_msg': '이 필드는 유효하지 않습니다.',
    'common_from_photos_album': '사진 라이브러리',
    'common_from_photos_documents': '파일 선택',
    'common_file_too_large_content': '가져온 파일이 업로드하기에 너무 큽니다.',
    'common_file_do_not_support_type': '이 파일 유형은 지원되지 않습니다.',
    'common_details': '상세정보',
    'common_total': '총계',
    'common_buy': '구매',
    'common_price': '가격',
    'common_report': '신고',
    'common_block': '차단',
    'common_block_popup_title': '이 사람을 차단하시겠습니까?',
    'common_block_popup_content':
        '차단하면 서로 매치하거나 채팅할 수 없게 됩니다. 마이페이지 차단 목록에서 그/그녀를 찾아 차단을 해제할 수 있습니다.',
    'common_chat': '채팅',
    'common_matched': '매치됨',
    'common_copy': '복사',
    'common_copy_finish_tip': '성공적으로 복사되었습니다.',
    'common_skip': '건너뛰기',
    'common_today_cards': '오늘의 카드',
    'common_got_hearts': '받은 하트',
    'common_sent_hearts': '보낸 하트',
    'common_given_cards': '받은 카드',
    'common_passed_cards': '지난 카드',
    'common_evaluation_title': '나에게 높은 평가를 준 사람',
    'common_locked': '잠김',
    'common_register_back_title': '이전 단계로 돌아가시겠습니까?',
    'common_register_back_content': '이전 단계로 돌아가시겠습니까? 돌아가면 현재 입력한 데이터가 초기화됩니다.',
    'common_net_error': '네트워크 오류가 발생했습니다. 나중에 다시 시도해 주세요.',
    'common_bottom_btn_today': '오늘',
    'common_bottom_btn_passed': '지난',
    'common_bottom_btn_community': '커뮤니티',
    'common_bottom_btn_chat': '채팅',
    'common_bottom_btn_me': '나',
    'common_accept': '수락',
    'common_try_again': '알 수 없는 오류가 발생했습니다. 나중에 다시 시도해 주세요.',
    'common_remove': '삭제',
    'common_remove_popup_title':
        '채팅 기록과 매치된 파트너가 매치 목록에서 삭제됩니다. 설정에서 파트너 차단을 해제할 수 있습니다.',
    'common_account_kickoff_tip': '계정이 다른 기기에서 로그인되었습니다.',
    'permission_photo_galley_alert_title': '\'Starchex\'이(가) 사진에 접근하려고 합니다',
    'permission_photo_galley_alert_content':
        '사진을 탐색하고 편집하려면 사진 라이브러리에 대한 접근 권한이 필요합니다.',
    'permission_camera_alert_title': '\'Starchex\'이(가) 카메라에 접근하려고 합니다',
    'permission_camera_alert_content': '동영상을 녹화하려면 카메라에 대한 접근 권한이 필요합니다.',
    'permission_mic_alert_title': '\'Starchex\'이(가) 마이크에 접근하려고 합니다',
    'permission_mic_alert_content': '동영상을 녹화하려면 마이크에 대한 접근 권한이 필요합니다.',
    'time_just_now': '방금',
    'time_ago': '전',
    'time_minutes_ago': '분',
    'time_hours_ago': '시간',
    'time_days_ago': '일',
    'time_months_ago': '개월',
    'time_years_ago': '년',
    'splash_title': '대학에서 친구 만들기',
    'splash_start': '시작하기',
    'splash_learn': '알아보기',
    'sign_in_title': '안녕하세요, Starchex에 오신 것을 환영합니다!',
    'sign_in_zalo': 'Zalo로 가입하기',
    'sign_in_google': 'Google로 가입하기',
    'sign_in_facebook': 'Facebook으로 가입하기',
    'sign_in_apple': 'Apple ID로 가입하기',
    'agreement_title': '모든 이용약관에 동의하기',
    'agreement_accept_all_title': '모든 이용약관에 동의합니다',
    'agreement_age_title': '만 18세 이상 (필수)',
    'agreement_terms_title': '이용약관 (필수)',
    'agreement_personal_info_title': '개인정보 처리방침 (필수)',
    'agreement_promotional_title': '마케팅 수신 동의 (선택)',
    'agreement_safety_and_policy_center': '안전 및 정책 센터 (필수)',
    'agreement_see_detail': ' 더 보기',
    'personal_basic_info_nationality': '국적',
    'personal_basic_info_nationality_hint': '국적을 선택하세요',
    'personal_basic_info_please_enter_your_nationality': '국적을 입력해 주세요',
    'personal_basic_info_sign_up': '가입하기',
    'personal_basic_info_title': '정보를 입력해 주세요.',
    'personal_basic_info_subtitle': '성공적으로 가입한 후 프로필 페이지에서 정보를 수정할 수 있습니다.',
    'personal_basic_info_nick_name': '닉네임',
    'personal_basic_info_nick_name_PH': 'Starchex에서 보이는 닉네임입니다',
    'personal_basic_info_date_birth': '생년월일',
    'personal_basic_info_date_birth_PH': '생년월일을 선택해 주세요',
    'personal_basic_info_region': '지역',
    'personal_basic_info_region_PH': '지역을 선택하세요',
    'personal_basic_info_gender': '성별',
    'personal_basic_info_female': '여성',
    'personal_basic_info_male': '남성',
    'personal_basic_info_phone_number': '전화번호',
    'personal_basic_info_phone_number_PH': '전화번호를 입력해 주세요',
    'personal_basic_info_verification_code_PH': '인증 코드를 입력해 주세요',
    'personal_basic_info_phone_number_invalid_msg': '휴대폰 번호 인증에 실패했습니다.',
    'personal_basic_info_phone_number_need_verify': '인증이 필요합니다',
    'personal_basic_info_verification_code_sent_msg': '인증 코드가 전송되었습니다.',
    'personal_basic_info_verification_code_valid_msg': '성공! 휴대폰 번호가 인증되었습니다.',
    'personal_basic_info_verification_code_invalid_msg': '유효한 인증 코드를 입력해 주세요.',
    'personal_basic_info_nick_name_exist_tip':
        '이 닉네임은 이미 사용 중입니다. 다른 닉네임을 입력해 주세요.',
    'personal_basic_info_email_PH': '대학 이메일을 입력해 주세요.',
    'personal_basic_info_location_tip': '이 데이터는 지역 파트너 매칭에 사용됩니다.',
    'personal_basic_info_phone_input_tip':
        'Starchex 고객 서비스 팀이 이 전화번호가 실제 사용자의 번호인지 확인하기 위해 연락할 것입니다.',
    'personal_basic_info_email_send_tip':
        '몇 분 내에 이메일을 받지 못한 경우, "정크 메일" 폴더나 "스팸" 폴더를 확인해 주세요.',
    'personal_basic_info_saving': '저장 중',
    'university_info_page_title': '인증 정보',
    'university_info_title': '대학교를 인증하세요.',
    'university_info_subtitle': '걱정하지 마세요! 실명은 인증용으로만 사용되며 서비스에 노출되지 않습니다.',
    'university_info_financial_verify_title': '자산을 인증하세요',
    'university_info_financial_verify_subtitle':
        '걱정하지 마세요! 재정 상태는 인증용으로만 사용되며 서비스에 노출되지 않습니다.',
    'university_info_name': '이름',
    'university_info_name_PH': '실명',
    'university_info_university': '대학교',
    'university_info_university_PH': '대학교를 선택하세요',
    'university_info_verify_your_wealthly': '자산 인증하기',
    'university_info_verify_your_wealthly_hint': '나의 재정 상태는',
    'university_info_financial_status_doNotAuthorize': '인증하지 않음',
    'university_info_financial_status_submitted': '인증 제출',
    'university_info_financial_status_moreThan100K': '자산 10만 달러 이상 보유',
    'university_info_financial_status_moreThan500K': '자산 50만 달러 이상 보유',
    'university_info_financial_status_moreThan1M': '자산 100만 달러 이상 보유',
    'university_info_financial_status_moreThan5M': '자산 500만 달러 이상 보유',
    'university_info_financial_status_moreThan10M': '자산 1000만 달러 이상 보유',
    'university_info_financial_status_moreThan50M': '자산 5000만 달러 이상 보유',
    'university_info_financial_status_moreThan100M': '자산 1억 달러 이상 보유',
    'university_info_auth_methods': '인증 방법',
    'university_info_verify_email': '이메일로 인증',
    'university_info_verify_email_note': '인증 즉시 Starchex를 이용할 수 있습니다.',
    'university_info_verify_cert_paper': '증명서로 인증',
    'university_info_verify_cert_paper_note':
        '보통 24시간 이내에 Starchex의 확인 후 이용 가능합니다.',
    'verify_email_subtitle': '선택한 대학교의 이메일 주소를 입력해 주세요.',
    'verify_email_input_PH': '인증 코드를 입력해 주세요',
    'verify_email_note': '인증 코드가 포함된 이메일이 @interval에 전송되었습니다.',
    'verify_email_code_valid_msg': '성공! 이메일이 인증되었습니다.',
    'verify_cert_paper_subtitle': '선택한 대학교의 학생증 사진이나 졸업 증명서를 업로드해 주세요.',
    'verify_cert_paper_input_PH': '학생증 사진, 졸업 증명서 또는 자산 증명 서류를 업로드하세요.',
    'verify_cert_paper_note1':
        'Starchex는 사용자의 자산 정보를 검토하여 인증 배지를 부여합니다. 인증 서류를 Starchex에 제출함으로써, 민감한 개인 정보를 수집하는 데 동의하는 것입니다. Starchex는 자산 인증 목적으로만 정보를 사용하며 인증 후 즉시 데이터를 안전하게 삭제할 것입니다.',
    'verify_cert_paper_note2':
        '재정 상태를 인증하기 위해 다양한 서류를 준비하여 제출해 주세요. 상황과 국적에 따라 은행 계좌 잔액, 부동산 등록증 등의 서류를 제공할 수 있습니다. 제출 전 민감한 개인 정보(예: 여권 번호)를 가려주세요.',
    'verify_cert_paper_note3':
        'Starchex는 서류를 검토하고 자산 인증 배지를 부여합니다. 추가 서류가 필요한 경우 앱 내 메시지로 요청할 수 있습니다.',
    'verify_cert_paper_note4':
        '학교 인증이나 자산 인증을 위한 서류를 업로드할 때 Starchex가 정확히 식별할 수 있도록 선명한 이미지를 업로드해 주세요.',
    'verify_cert_paper_submit_to_verify': '인증을 위해 제출',
    'submit_photos_page_title': '취미',
    'submit_photos_title': '사진을 등록해 주세요.',
    'submit_photos_subtitle':
        '대표 사진은 프로필 메인 페이지에 노출됩니다. 얼굴과 전신이 보이는 사진을 업로드해 주세요 (사진 수에 제한 없음)',
    'interesting_title': '취미를 선택해 주세요.',
    'interesting_subtitle': '같은 관심사를 가진 사람들을 찾아보세요! 최대 5개까지 추가할 수 있습니다.',
    'interesting_input_tip': '취미를 찾을 수 없나요? 직접 입력해 주세요.',
    'introduce_title': '자유롭게 자신을 소개해 주세요.',
    'introduce_subtitle': '처음 세 줄은 카드 내부에 직접 노출됩니다.',
    'introduce_input_PH': '정보를 입력하세요',
    'introduce_note1': '사실과 일치하지 않는 정보를 기재할 경우 형사처벌을 받을 수 있습니다.',
    'introduce_note2': '문제가 있으면 수정해 주세요.',
    'signUp_completion_title': '@name님,\nStarchex에 오신 것을 환영합니다!',
    'signUp_completion_subtitle':
        '이제 매일 오전 9시부터 매 시간마다 Starchex가 선별한 멋진 파트너를 만나보세요.',
    'signUp_completion_phone_verification_title': '휴대폰 인증',
    'signUp_completion_phone_verification_subtitle': '계속하려면 휴대폰 번호를 인증해 주세요.',
    'signUp_completion_pending_verification_title': '인증 대기 중',
    'signUp_completion_pending_verification_subtitle':
        '잠시만 기다려 주세요! \n귀하의 정보는 향후 24시간 내에 Starchex에 의해 인증될 예정입니다.',
    'signUp_completion_pending_verification_subtitle_2':
        '인증서를 업데이트하면 관리자의 승인이 있을 때까지 계정이 대기 상태가 됩니다. 업데이트하시겠습니까?',
    'signUp_completion_failed_verification_title': '인증 실패',
    'signUp_completion_failed_verification_subtitle':
        '죄송합니다. 학생증 사진이나 졸업 증명서를 확인할 수 없습니다. \n사진을 다시 업로드하고 인증해 주세요.',
    'signUp_completion_back_verify': '인증으로 돌아가기',
    'profile_about_me': '자기소개',
    'profile_interests': '취미',
    'profile_evaluate_card': '카드 평가하기',
    'profile_rate_to_get_mc': '카드를 평가하고 미코인을 받으세요',
    'profile_rating_popup_title': '카드 평가하기',
    'profile_rating_popup_subtitle': '파트너를 평가하지 않았습니다. 종료하시겠습니까?',
    'profile_to_rate': '평가하기',
    'profile_title': '프로필',
    'profile_rating_result_tip': '파트너 평가로 @amount 미코인을 받았습니다',
    'profile_send_heart_message': '@name님에게 하트를 보냈습니다. 좋은 시작입니다!',
    'profile_send_double_heart_message': '@name님에게 더블 하트를 보냈습니다. 좋은 시작입니다!',
    'profile_heart_operation_tip_one': '님으로부터 하트를 받았습니다 ',
    'profile_heart_operation_tip_two': '매칭되려면 하트를 보내세요.',
    'profile_doubleHeart_operation_tip_one': '님으로부터 더블하트를 받았습니다 ',
    'profile_doubleHeart_operation_tip_two': '수락하면 매칭됩니다.',
    'mee_coin_meecoin': '미코인',
    'mee_coin_available_meecoin': '사용 가능한 미코인',
    'mee_coin_recharge': '충전',
    'mee_coin_transaction': '거래 내역',
    'mee_coin_mct_unlock_given_card': '받은 카드 열기',
    'mee_coin_mct_unlock_high_eva': '높은 평가 카드 열기',
    'mee_coin_mct_unlock_heart_card': '하트 카드 열기',
    'mee_coin_mct_send_heart': '하트 보내기',
    'mee_coin_mct_send_double_heart': '더블 하트 보내기',
    'mee_coin_mct_buy_premium': '프리미엄 카드 구매',
    'mee_coin_mct_eva': '평가',
    'mee_coin_mct_double_heart_refund': '더블 하트 환불',
    'mee_coin_mct_recharge': '충전',
    'mee_coin_mct_ref2_bonus': '추천받은 구독자',
    'mee_coin_mct_ref1_bonus': '추천인',
    'mee_coin_mct_pre_reg': '사전 구독자',
    'mee_coin_mct_deposit': '시스템 미코인 보상',
    'mee_coin_mct_deduction': '시스템 미코인 차감',
    'mee_coin_mct_top_post': '인기 게시물',
    'mee_coin_recharge_title': '충전 금액을 \n선택해 주세요.',
    'mee_coin_recharge_subtitle': '충전 금액이 많을수록 \n더 큰 할인을 받으세요!',
    'mee_coin_practical': '실용적',
    'mee_coin_popular': '인기',
    'mee_coin_bonus_rate': '부가 가치 @ratePercent',
    'mee_coin_bonus_rate_title': '부가 가치',
    'mee_coin_vnd': 'VNĐ',
    'mee_coin_bonus': '보너스',
    'mee_coin_recharge_amount': '충전 금액',
    'mee_coin_pay_failed': '결제 실패',
    'mee_coin_pay_invalid': '결제 무효',
    'today_cards_recharge_title': '충전 금액을 \n선택해 주세요.',
    'today_cards_recharge_subtitle': '충전 금액이 많을수록 \n더 큰 할인을 받으세요!',
    'passed_cards_empty_msg':
        '지난 카드는 지난 7일 동안의 카드를 보여줍니다.\n더 이상 카드가 없습니다.\n더 많은 카드를 열려면 |오늘의| 카드를 탭하세요.',
    'unlock_balance': '잔액',
    'unlock_recharge': '충전',
    'unlock_cards': '@number 카드',
    'unlock_premium_cards': '프리미엄 카드',
    'unlock_premium_subtitle_1': '@number 카드를 한 번에 열기.',
    'unlock_premium_subtitle_2': '당신을 위한 세 명의 고품질 파트너.',
    'unlock_premium_subtitle_3': '즉시 매칭해 보세요!',
    'unlock_note': '계속을 탭하면 요금이 부과되며, \n우리의 |이용약관|에 동의하게 됩니다.',
    'unlock_unlock_single_heart_title': '하트 카드 열기',
    'unlock_unlock_single_heart_subtitle': '누가 당신에게 하트 카드를 보냈는지 확인하세요.',
    'unlock_unlock_passed_given_card_title': '받은 카드 열기',
    'unlock_unlock_passed_given_card_subtitle': '지난 받은 카드를 열어보세요.',
    'unlock_unlock_high_eva_title': '높은 평가 카드 열기',
    'unlock_unlock_high_eva_subtitle': '누가 당신에게 높은 평가를 했는지 확인하세요.',
    'unlock_send_single_heart_title': '하트 카드 보내기',
    'unlock_send_single_heart_subtitle':
        '하트 카드를 보내면 푸시 알림과 SMS를 통해 파트너에게 하트 카드 도착 알림이 전송됩니다.',
    'unlock_send_double_heart_title': '더블 하트 카드 보내기',
    'unlock_send_double_heart_subtitle':
        '더블 하트 카드를 보냅니다. 받는 파트너는 하트를 지불하지 않고 수락할 수 있습니다. 실패할 경우 +20미코인이 환불됩니다.',
    'unlock_not_enough_balance': '잔액 부족',
    'unlock_not_enough_balance_popup_content': '미코인 잔액이 부족합니다. 충전해 주세요.',
    'unlock_no_profile_popup_title': '프로필 완성하기',
    'unlock_no_profile_popup_content': '프로필을 완성해야 합니다.',
    'unlock_no_profile_popup_ok_button': '프로필로 이동',
    'subscription_buy': '구매',
    'subscription_normal_heart_name': '일회성',
    'subscription_normal_heart_description': '하트 카드를 한 번 보냅니다.',
    'subscription_double_heart_name': '더블 하트 카드',
    'subscription_double_heart_description': '매칭 성공 확률이 높아집니다!',
    'subscription_tip': '하트 볼륨 비교',
    'chat_common_hey_there': '안녕하세요',
    'chat_common_everyone': '모두',
    'chat_common_no_recent_emoji': '최근 이모티콘 없음',
    'chat_common_read': '읽음',
    'chat_common_say_hi': '인사하기',
    'chat_common_empty_tip': '위에서 새로운 매치를 탭하여 \n메시지를 보내세요.',
    'chat_common_conversation_empty_tip':
        '아직 채팅이 없습니다.\n |오늘의 카드|를 탭하여 더 많은 카드를 열어보세요.',
    'chat_common_today_btn': '오늘의 카드로 이동',
    'chat_common_typing_tip': '입력 중...',
    'chat_common_synchronizing': '동기화 중...',
    'chat_common_syncFailed': '동기화 실패',
    'chat_common_connecting': '연결 중...',
    'chat_common_connectionFailed': '연결 실패',
    'chat_messageType_picture': '사진',
    'chat_messageType_video': '동영상',
    'chat_messageType_voice': '음성',
    'chat_messageType_file': '파일',
    'chat_messageType_emoji': '이모티콘',
    'chat_messageType_unsupportedMessage': '알 수 없는 메시지',
    'chat_time_now': '방금',
    'chat_time_justNow': '방금',
    'chat_time_ago': '전',
    'chat_time_minutes_ago': '분',
    'chat_time_hours_ago': '시간',
    'chat_time_days_ago': '일',
    'chat_time_months_ago': '개월',
    'chat_time_years_ago': '년',
    'chat_time_count_minute': '분',
    'chat_time_count_hour': '시간',
    'chat_time_date_time_month': '',
    'chat_time_date_time_day': '',
    'chat_empty_matched_info1': '님과 매칭되었습니다.',
    'chat_empty_matched_info2': '흥미로운 대화를 나눠보세요.',
    'chat_sys_msg_remove_black': '사용자가 차단 목록에서 제거되었습니다.',
    'compare_heart_item': '항목',
    'compare_heart_row1': '파트너가 무료로 수락할 수 있음',
    'compare_heart_row2': '받은 메시지를 상단에 표시',
    'compare_heart_row3': '특별 푸시 및 SMS 메시지 알림',
    'compare_heart_row4': '실패 시 보상',
    'compare_heart_row4_double_heart': '20미코인',
    'compare_heart_row5': '매칭 성공률 향상',
    'compare_heart_row5_double_heart': '4배 더 높은 확률',
    'my_page_title': '마이페이지',
    'my_page_about': 'Starchex 소개',
    'my_page_help_center': '고객센터',
    'my_page_feedback': '피드백',
    'my_page_terms_conditions': '이용약관',
    'my_page_privacy_policy': '개인정보 처리방침',
    'my_page_safety_and_policy_center': '안전 및 정책 센터',
    'my_page_settings': '설정',
    'my_page_market': '미코인 구매',
    'my_page_referrer_tip': '추천인 코드',
    'my_page_available_text': '사용 가능',
    'my_page_logout_btn': '로그아웃',
    'my_page_referrer_pop_title': '친구 추천 및 보상 받기',
    'my_page_referrer_pop_content':
        '친구에게 추천 코드로 가입하도록 요청하세요. 완료되면 귀하와 친구 모두 다음을 받습니다',
    'my_page_invitation': '추천인 코드 입력',
    'my_page_invitation_menu_tip': '미코인 +100 획득',
    'my_page_pending_profile_tip': '인증 진행 중',
    'my_page_finish_profile_tip': '정보를 완성해 주세요.',
    'settings_title': '설정',
    'settings_manage_black_list': '차단된 계정 관리',
    'settings_quit': '계정 삭제',
    'settings_blocklist': '차단 목록',
    'settings_unblock': '차단 해제',
    'settings_language': '언어',
    'settings_english': '영어',
    'settings_korean': '한국어',
    'report_main_title': '이 사람을 신고하시겠습니까?',
    'report_sub_title': '신고 이유를 선택해 주세요.',
    'report_reason1': '가짜 프로필',
    'report_reason2': '무례하거나 공격적인 행동',
    'report_reason3': '부적절한 콘텐츠',
    'report_reason4': '사기 또는 상업적 행위',
    'report_reason5': '정체성 기반 혐오',
    'report_reason6': '기타 (직접 입력)',
    'report_other_placeholder': '100자 이내로 입력해 주세요.',
    'report_image_tip': '신고와 관련된 사진을 업로드해 주세요 (필수)',
    'report_image': '이미지',
    'report_upload_tip': 'JPG 또는 PNG 형식으로 최대 5개(60MB)의 관련 사진을 첨부할 수 있습니다.',
    'report_report_tip': '신고 사실은 상대방에게 공개되지 않습니다.',
    'report_report_btn': '신고',
    'report_file_empty_tip': '파일을 선택해 주세요',
    'edit_profile_upload_documents': '문서 업로드',
    'edit_profile_update_my_attestation': '내 인증 관리',
    'edit_profile_support_document': '지원 문서',
    'edit_profile_support_document_tip': '교육 배경과 자산을 증명할 수 있는 문서를 업로드하세요.',
    'edit_profile_enable_discovery': '검색 활성화',
    'edit_profile_enable_discovery_note':
        '꺼져 있으면 귀하의 프로필이 카드 스택에서 숨겨지고 검색이 비활성화됩니다. 이미 매치된 사람들은 여전히 귀하를 보고 채팅할 수 있습니다.',
    'edit_profile_enable_discovery_alert_title': '필수 항목을 입력해 주세요',
    'edit_profile_enable_discovery_alert_content':
        '죄송합니다. 먼저 프로필을 완성하거나 업데이트한 후에 검색을 활성화할 수 있습니다.',
    'edit_profile_photo_title': '사진',
    'edit_profile_address_title': '주소',
    'edit_profile_interests_title': '관심사',
    'edit_profile_aboutme_title': '자기소개',
    'edit_profile_photo_avatar_require': '프로필 사진을 업로드해 주세요.',
    'edit_profile_interests_require': '관심사를 선택해 주세요.',
    'edit_profile_introduction_require': '자기소개를 입력해 주세요.',
    'edit_profile_unsaved_change_title': '저장되지 않은 변경사항이 있습니다!',
    'edit_profile_unsaved_change_content':
        '저장되지 않은 변경사항이 있습니다.\n변경사항을 완료하고 저장해 주세요.',
    'edit_profile_leave': '나가기',
    'edit_profile_stay': '머무르기',
    'edit_profile_attestation': '인증',
    'edit_profile_basic_information': '기본 정보',
    'edit_profile_others': '기타',
    'edit_profile_verify_university': '대학교 인증',
    'edit_profile_university_not_certified': '인증되지 않음',
    'edit_profile_university_pending': '인증 중',
    'edit_profile_university_rejected': '거부됨',
    'edit_profile_university_certified': '인증됨',
    'edit_profile_tap_to_edit_tip': '탭하여 편집',
    'edit_profile_edit_save_tip_title': '프로필 저장',
    'edit_profile_edit_save_tip_content': '프로필을 변경하시겠습니까? 이 작업은 인증 대기가 필요합니다.',
    'edit_profile_university_name_required': '실명을 입력해 주세요',
    'edit_profile_university_university_required': '대학교를 선택해 주세요',
    'edit_profile_university_student_id_required': '학생증 사진이나 졸업 증명서를 업로드해 주세요',
    'edit_profile_university_email_required': '대학교 이메일 주소를 입력해 주세요',
    'edit_profile_university_v_code_required': '이메일의 인증 코드를 입력해 주세요',
    'edit_profile_profile_university_required': '대학교 인증을 완료해 주세요',
    'edit_profile_profile_nickname_required': '닉네임을 입력해 주세요',
    'edit_profile_profile_birth_required': '생년월일을 선택해 주세요',
    'edit_profile_profile_region_required': '지역을 선택해 주세요',
    'edit_profile_profile_gender_required': '성별을 선택해 주세요',
    'edit_profile_profile_phone_required': '전화번호를 입력해 주세요',
    'edit_profile_profile_photos_required': '최소한 한 장의 사진을 업로드해 주세요',
    'edit_profile_isGraduated': '졸업',
    'invitation_referrer_tip': '추천인 코드',
    'invitation_referrer_input_tip': '코드는 한 번만 입력할 수 있습니다. 신중하게 입력해 주세요.',
    'invitation_referrer_popup_content_1':
        '추천인 코드가 있으면 입력해 주세요. 귀하와 친구 모두 다음을 받습니다',
    'invitation_referrer_popup_content_2': '코드가 없으면 건너뛰기를 누르세요.',
    'invitation_referrer_error_title': '추천인 코드 오류',
    'invitation_referrer_error_content':
        '죄송합니다. 추천인 코드가 잘못되었으며 신규 사용자 보상이 없습니다.',
    'invitation_reward_popup_title': '신규 사용자 보상',
    'invitation_reward_popup_content':
        'Starchex에 오신 것을 환영합니다! 신규 사용자 보상 미코인을 즐기세요',
    'invitation_referrer_code': '추천인 코드',
    'feedback_email_label': '답변을 받을 이메일 주소',
    'feedback_email_placeholder': '이메일 주소를 입력해 주세요.',
    'feedback_inquiry_label': '문의 내용',
    'feedback_inquiry_placeholder': '선택',
    'feedback_content_placeholder': '내용을 1000자 이내로 작성해 주세요.',
    'feedback_image_label': '피드백과 관련된 사진을 업로드해 주세요',
    'feedback_email_incorrect': '올바른 이메일을 입력해 주세요.',
    'feedback_content_required': '피드백 내용을 입력해 주세요.',
    'feedback_content_max_length': '내용은 1000자를 초과할 수 없습니다.',
    'quit_main_title': '정말 탈퇴하시겠습니까?',
    'quit_main_desc': '회원 탈퇴 전, 아래 정보를 반드시 확인해 주세요.',
    'quit_content_title': '계약 또는 구독 철회 등의 기록',
    'quit_list_1': '계약 또는 구독 철회 등의 기록',
    'quit_list_1_a': '근거 법률: 전자상거래 등에서의 소비자보호에 관한 법률 (보존기간: 5년)',
    'quit_list_2': '상품 결제 및 공급 기록',
    'quit_list_2_a': '근거 법률: 전자상거래 등에서의 소비자보호에 관한 법률 (보존기간: 5년)',
    'quit_list_3': '소비자 불만 또는 분쟁 해결 기록',
    'quit_list_3_a': '근거 법률: 전자상거래 등에서의 소비자보호에 관한 법률 (보존기간: 3년)',
    'quit_list_4': '전시/광고 기록',
    'quit_list_4_a': '근거 법률: 전자상거래 등에서의 소비자보호에 관한 법률 (보존기간: 6개월)',
    'quit_list_5': '불법 이용 등의 기록',
    'quit_list_5_a': '근거 법률: 전자상거래 등에서의 소비자보호에 관한 법률 (보존기간: 5년)',
    'quit_list_6': '서비스 방문 기록 - 접속 기록, IP 주소 등',
    'quit_list_6_a': '근거 법률: 통신비밀보호법 (보존기간: 3개월)',
    'quit_agreement_title': '위 내용을 모두 확인했습니다. (필수)',
    'quit_reason_title': 'Starchex를 탈퇴하는 이유가 무엇인가요?\n(필수) *복수 선택 가능',
    'quit_reason_1': '사용하지 않음',
    'quit_reason_2': '다시 가입하고 싶음',
    'quit_reason_3': '사용이 불편함',
    'quit_reason_4': '원하는 콘텐츠가 없음',
    'quit_reason_5': '기타',
    'quit_reason_placeholder': '탈퇴 이유를 자세히 알려주시면 서비스 품질 향상에 큰 도움이 됩니다.',
    'quit_quit': '삭제',
    'quit_confirm_title': '계정 삭제',
    'quit_confirm_popup':
        'Starchex에서 탈퇴하면 모든 활동 기록(미코인 포인트, 사진, 히스토리 등)이 삭제됩니다. 결정을 내릴 때 이 점을 명심해 주세요. 탈퇴하시겠습니까?',
    'chat_notification_title': 'Chat notification',
    'chat_notification_content': 'You have a new message. Please check it.',
    'notification_title': 'Notification',
    'community_title': '커뮤니티',
    'community_register_tip':
        '커뮤니티 닉네임을 입력하세요. 이 닉네임은 커뮤니티 게시판 서비스에서만 사용되며, 메인 매칭 서비스의 닉네임과 다릅니다.',
    'community_fakeName_tip': '생각이 나지 않나요? 다음 중 하나를 사용하세요:',
    'community_delete': '삭제',
    'community_post_list_empty': '콘텐츠가 없습니다. \n당신의 게시물을 기대합니다.',
    'community_comments_list_empty': '댓글이 없습니다.\n당신의 댓글을 기대합니다.',
    'community_likes_list_empty': '좋아요가 없습니다.\n첫 번째 좋아요를 누른 사람이 되세요.',
    'community_me': '나',
    'community_post_title': '게시물 작성',
    'community_post_placeholder': '무슨 생각을 하고 계신가요? (10000자 제한)',
    'community_post_board_label': '커뮤니티',
    'community_post_post_btn': '게시',
    'community_post_photo_upload_tip':
        'JPG 또는 PNG 형식으로 최대 10장의 사진(각 사진 < 5MB)을 첨부할 수 있습니다.',
    'community_post_content_empty_tip': '게시물 내용은 비워둘 수 없습니다',
    'community_post_deleted_post': '사용자에 의해 삭제되었습니다',
    'community_post_show_more': '더 보기',
    'community_post_hide_more': '숨기기',
    'community_post_see_likes': '좋아요 보기',
    'community_post_all_comments': '모든 댓글',
    'community_post_all_likes': '모든 좋아요',
    'community_post_empty': '내용이 없습니다',
    'community_comment_placeholder': '댓글...(10000자 제한)',
    'community_comment_more_text': '더 많은 댓글 보기',
    'community_comment_end_text': '끝',
    'community_comment_mention_empty_tip':
        '죄송합니다. \${searchText}\$ 사용자를 찾을 수 없습니다. \n닉네임을 확인하거나 다른 사람을 언급해 보세요.',
    'community_comment_mention_empty_1': '죄송합니다. 사용자를 찾을 수 없습니다 ',
    'community_comment_mention_empty_2': '닉네임을 확인하거나 다른 사람을 언급해 보세요.',
    'community_report_reason1': '괴롭힘',
    'community_report_reason2': '폭력 위협',
    'community_report_reason3': '혐오 발언',
    'community_report_reason4': '미성년자 학대 또는 성적 대상화',
    'community_report_reason5': '개인정보 공유',
    'community_report_reason6': '동의 없는 음란물',
    'community_report_reason7': '금지된 거래',
    'community_report_reason8': '사칭',
    'community_report_reason9': '저작권 침해',
    'community_report_reason10': '상표권 침해',
    'community_report_reason11': '자해 또는 자살',
    'community_report_reason12': '스팸',
    'community_report_reason13': '기타',
    'community_profile_posts': '게시물',
    'community_profile_likes': '좋아요',
    'community_profile_edit_profile': '프로필 편집',
    'community_profile_creat_post': '게시물 작성',
    'community_profile_comments': '댓글',
    'check_app_version_new_version_title': '앱 업데이트',
    'check_app_version_new_version_content':
        'Starchex는 사용자 의견을 반영하여 사용성을 개선했습니다. 더 나은 서비스 이용을 위해 최신 버전으로 업데이트해 주세요.',
    'check_app_version_new_version_action': '앱스토어로 이동',
    'check_app_version_new_version_action_later': '나중에',
    'otp_verify_page_title': 'OTP 인증',
    'otp_verify_page_message': '6자리 인증코드가 포함된 문자 메시지가\n@phone_number로 전송되었습니다',
    'otp_verify_page_didnt_get_the_code': '코드를 받지 못하셨나요?',
    'otp_verify_page_resend': '재전송',
    'otp_verify_page_resend_after': '@time 후에 코드를 다시 확인하세요',
    'otp_verify_page_successfully': '전화번호 인증이 성공적으로 완료되었습니다',
    'otp_verify_page_error_title': 'OTP 코드 오류',
    'sc_str_ai_avatar_error_no_croppable_avatar': '자를 수 있는 AI 아바타가 없습니다',
    'sc_str_ai_avatar_error_web_not_supported': '웹 버전은 아바타 자르기를 지원하지 않습니다',
    'sc_str_ai_avatar_error_data_format_error': 'AI 아바타 데이터 형식 오류, 자를 수 없습니다',
    'sc_str_ai_avatar_error_download_failed_code': 'AI 아바타 다운로드 실패, 상태 코드',
    'sc_str_ai_avatar_error_download_failed': 'AI 아바타 다운로드 실패',
    'sc_str_ai_avatar_error_crop_success': '아바타 자르기 완료',
    'sc_str_ai_avatar_error_crop_failed': '아바타 자르기 실패',
    'sc_str_ai_avatar_error_upload_first': 'AI 아바타를 생성하려면 먼저 사진을 업로드하세요',
    'sc_str_ai_avatar_error_generating_wait':
        'AI 아바타 생성 중, 기다려 주세요, 1-3분 정도 소요될 수 있습니다...',
    'sc_str_ai_avatar_error_empty_photo_data': '사진 데이터가 비어 있습니다',
    'sc_str_ai_avatar_error_empty_photo_file': '사진 파일이 비어 있습니다',
    'sc_str_ai_avatar_error_timeout':
        'AI 아바타 생성 시간 초과, 서버 처리 시간이 너무 깁니다, 나중에 다시 시도하세요',
    'asset_verification_title': '자산 인증',
    'asset_verification_description': '다음 자산들은 StarChecks로 인증할 수 있습니다.',
    'asset_verification_real_estate': '부동산',
    'asset_verification_financial_assets': '금융 자산',
    'asset_verification_crypto_currency': '암호화폐',
    'asset_verification_art_works_cars': '예술품, 자동차, ...',
    'asset_verification_guidance_title': '[안내 및 면책사항]',
    'asset_verification_guidance_1': '제출된 모든 정보는 관련 법률 및 보안 정책에 따라 안전하게 처리됩니다.',
    'asset_verification_guidance_2':
        '정확한 자산 인증을 위해 모든 정보와 문서는 사실적이고 일관성이 있어야 합니다.',
    'asset_verification_guidance_3': '허위 정보 제출 시 서비스 이용 제한이 있을 수 있습니다.',
    'asset_verification_next': '다음',
    'asset_verification_upload_id_title': '신분증을 제출해 주세요',
    'asset_verification_upload_id_description': '아래에서 자산을 인증할 수 있습니다.',
    'asset_verification_id_card': '신분증',
    'asset_verification_file_format_info': 'JPEG / PNG • 최대 10 MB',
    'asset_verification_upload_file': '자료 업로드하기',
    'asset_verification_id_submission_guidelines': '[신분증 제출 가이드라인]',
    'asset_verification_guideline_clear_image':
        '선명한 이미지: 전체 이미지가 선명하게 보이도록 신분증 사진을 찍어주세요. (반사 및 그림자에 주의하세요.)',
    'asset_verification_guideline_include_original':
        '원본 포함: 가장자리가 잘리지 않도록 신분증 사진을 찍어주세요.',
    'asset_verification_guideline_masking':
        '마스킹: 개인정보 보호를 위해 주민등록번호와 여권번호의 뒤 7자리를 가려주세요.',
    'asset_verification_guideline_updated_info':
        '최신 정보: 유효한 신분증을 제출해 주세요. (만료일 확인)',
    'asset_verification_select_assets_title': '자산을 나열해 주세요',
    'asset_verification_select_assets_description':
        '각 자산 유형에 대해 여러 자산을 등록할 수 있습니다.',
    'asset_verification_real_estate_desc': '주택, 토지, 건물',
    'asset_verification_financial_assets_desc': '예금, 저축, 주식, 채권 등',
    'asset_verification_crypto_currency_desc': '비트코인, 이더리움 등',
    'asset_verification_other_assets': '기타 자산',
    'asset_verification_other_assets_desc': '예술품, 자동차 등',
    'asset_verification_list_of_added_assets': '추가된 자산 목록',
    'asset_verification_tap_to_add_asset': '이 자산 유형을 추가하려면 탭하세요',
    'asset_verification_remove_asset': '제거',
    'asset_verification_asset_name': '자산 이름',
    'asset_verification_document_of_proof': '증명 서류',
    'asset_verification_selected_files': '선택된 파일',
    'asset_verification_description_optional': '설명 (선택사항)',
    'asset_verification_description_placeholder':
        '자산 가치 평가에 도움이 될 추가 정보를 제공해 주세요.',
    'asset_verification_document_upload_guidelines': '[서류 업로드 가이드라인]',
    'asset_verification_real_estate_name_placeholder': '예: 강남구 아파트',
    'asset_verification_financial_assets_name_placeholder': '예: 신한증권 계좌 보유 주식',
    'asset_verification_crypto_currency_name_placeholder': '예: 빗썸 비트코인',
    'asset_verification_other_assets_name_placeholder': '예: 유명 작가의 작품',
    'asset_verification_real_estate_document_desc': '등기부등본(부동산 등기 사본)',
    'asset_verification_financial_assets_document_desc':
        '잔액을 보여주는 스크린샷\n소유자 정보를 보여주는 스크린샷',
    'asset_verification_crypto_currency_document_desc':
        '예: 지갑 스크린샷, 거래소 명세서, 거래 내역',
    'asset_verification_other_assets_document_desc':
        '소유권과 가치를 증명할 수 있는 서류\n예: 매매계약서, 등록증, 보증서, 감정서 등',
    'asset_verification_real_estate_guideline_1':
        '스캔 또는 사진 촬영으로 최근 3개월 이내에 발급된 등기부등본(말소 포함) 원본을 업로드해 주세요.',
    'asset_verification_real_estate_guideline_2':
        '모든 면이 선명하게 보여야 하며 정보를 식별할 수 있어야 합니다.',
    'asset_verification_real_estate_guideline_3':
        '제출하기 전에 주민등록번호 등 개인 식별 정보를 가려주세요.',
    'asset_verification_financial_assets_guideline_1':
        '정보를 식별할 수 있도록 고해상도 사진을 찍어주세요. 여러 파일을 업로드할 수 있습니다.',
    'asset_verification_financial_assets_guideline_2':
        '모든 면이 선명하게 보여야 하며 정보를 식별할 수 있어야 합니다.',
    'asset_verification_financial_assets_guideline_3':
        '제출하기 전에 주민등록번호 등 개인 식별 정보를 가려주세요.',
    'asset_verification_crypto_currency_guideline_1':
        '정보를 식별할 수 있도록 고해상도 사진을 찍어주세요. 여러 파일을 업로드할 수 있습니다.',
    'asset_verification_crypto_currency_guideline_2':
        '모든 면이 선명하게 보여야 하며 정보를 식별할 수 있어야 합니다.',
    'asset_verification_crypto_currency_guideline_3':
        '제출하기 전에 주민등록번호 등 개인 식별 정보를 가려주세요.',
    'asset_verification_other_assets_guideline_1':
        '정보를 식별할 수 있도록 고해상도 사진을 찍어주세요. 여러 파일을 업로드할 수 있습니다.',
    'asset_verification_other_assets_guideline_2':
        '모든 면이 선명하게 보여야 하며 정보를 식별할 수 있어야 합니다.',
    'asset_verification_other_assets_guideline_3':
        '제출하기 전에 주민등록번호 등 개인 식별 정보를 가려주세요.',
    'asset_verification_common_guideline_ownership':
        '자산이 본인 소유가 아닌 경우 증명서를 첨부해 주세요: 법인 소유(법인등기부등본) 배우자 소유(가족관계증명서). 여러 개가 있는 경우 하나만 첨부하면 됩니다.',
    'asset_verification_document_upload_guidelines_title': '[서류 업로드 가이드라인]',
    'asset_verification_denial_reasons_title': '다음 자산은 거부될 수 있습니다.',
    'asset_verification_denial_reason_info_inconsistency':
        '정보 불일치: 입력 정보와 제출 서류가 일치하지 않거나 정보를 확인할 수 없는 경우 인증이 거부될 수 있습니다.',
    'asset_verification_denial_reason_title_inconsistency':
        '소유권 불일치: 실제 소유자와 문서상 소유권이 일치하지 않는 자산의 경우 인증이 거부될 수 있습니다. (배우자/법인 소유 제외)',
    'asset_verification_denial_reason_proof_ownership':
        '본인 소유가 아닌 자산의 경우 증빙서류를 첨부해 주세요: 법인 소유(법인 등기사항전부증명서) 배우자 소유(가족관계증명서). 여러 개가 있는 경우 하나만 첨부하시면 됩니다.',
    'asset_verification_real_estate_denial_unregistered':
        '미등기 자산: 등기 절차가 완료되지 않은 부동산은 인증이 거부될 수 있습니다.',
    'asset_verification_real_estate_denial_disputes':
        '소유권 분쟁: 소송, 압류 등으로 소유권 행사에 제한이 있는 부동산은 인증이 거부될 수 있습니다.',
    'asset_verification_real_estate_denial_other_names':
        '타인 명의 자산: 부동산 소유권과 등록된 이름이 일치하지 않는 자산은 인증이 거부될 수 있습니다. (배우자/법인 소유 제외)',
    'asset_verification_real_estate_denial_forgery':
        '서류 위조: 제출된 서류에 위조나 허위 정보가 있는 경우 인증이 거부되고 서비스 이용이 제한될 수 있습니다.',
    'asset_verification_financial_denial_uncertain_maturity':
        '불확실한 만기 상품: 계약 조건이 불명확하거나 유동성이 현저히 낮은 금융 상품은 인증이 거부될 수 있습니다.',
    'asset_verification_financial_denial_high_risk':
        '고위험 금융 자산: 가치 변동성이 높거나 객관적 평가가 불확실한 고위험 금융 자산은 인증이 거부될 수 있습니다.',
    'asset_verification_crypto_denial_unregistered_exchange':
        '미등록 거래소 자산: 주요 거래소에 등록되지 않아 가격이나 거래 내역 확인이 어려운 자산은 인증이 거부될 수 있습니다.',
    'asset_verification_crypto_denial_low_liquidity':
        '낮은 시장 유동성 자산: 시가총액 상위 100위 밖이거나 거래량이 현저히 낮아 객관적 평가가 어려운 코인/토큰은 인증이 거부될 수 있습니다.',
    'asset_verification_crypto_denial_extreme_volatility':
        '극심한 변동성/상장폐지 위험 자산: 가치 변동성이 극심하거나 상장폐지 위험이 높은 고위험 가상화폐는 인증이 거부될 수 있습니다.',
    'asset_verification_other_denial_market_uncertainty':
        '시장 가치 불확실성: 객관적 시장 가치 추정이 어렵거나 신뢰할 만한 평가 기준이 없는 자산은 인증이 거부될 수 있습니다.',
    'asset_verification_other_denial_personal_agreement':
        '개인 합의 가치: 우리 기준에 맞지 않는 개인 합의 가치로만 평가되는 자산은 인증이 거부될 수 있습니다.',
    'asset_verification_other_denial_unclear_ownership':
        '불명확한 소유권: 소유권을 증명할 수 없거나 분쟁 중인 자산은 인증이 거부될 수 있습니다.',
    'asset_verification_error_no_file_selected': '파일이 선택되지 않음',
    'asset_verification_error_no_file_selected_desc': '먼저 신분증을 업로드해 주세요',
    'asset_verification_success_assets_submitted': '자산 제출됨',
    'asset_verification_success_assets_submitted_desc':
        '@count개 자산이 성공적으로 제출되었습니다',
    'asset_verification_error_no_assets_added': '추가된 자산 없음',
    'asset_verification_error_no_assets_added_desc': '최소 하나의 자산을 추가해 주세요',
    'asset_verification_success_file_selected': '파일 선택됨',
    'asset_verification_success_file_selected_desc':
        '파일 @filename이 성공적으로 선택되었습니다',
    'asset_verification_error_file_selection': '오류',
    'asset_verification_error_file_selection_desc': '파일 선택 실패: @error',
    'asset_verification_success_files_selected': '파일 선택됨',
    'asset_verification_success_files_selected_desc': '@count개 파일이 선택되었습니다',
    'asset_verification_success_files_added_desc':
        '@count개 파일을 추가했습니다, 총 @total개 파일',
    'asset_verification_info_files_already_exist': '파일이 이미 존재합니다',
    'asset_verification_info_files_already_exist_desc':
        '선택한 모든 파일이 이미 이 자산에 존재합니다',
    'asset_verification_error_files_selection_desc': '파일 선택 실패: @error',
    'asset_verification_error_asset_name_required': '자산 이름을 입력해 주세요',
    'asset_verification_error_file_required': '최소 하나의 파일을 선택해 주세요',
    'asset_verification_success_asset_added': '자산 추가됨',
    'asset_verification_success_asset_added_desc': '@name이 성공적으로 추가되었습니다',
    'asset_verification_success_asset_removed': '자산 제거됨',
    'asset_verification_success_asset_removed_desc': '@name이 제거되었습니다',
    'asset_verification_asset_type_real_estate': '부동산',
    'asset_verification_asset_type_financial_assets': '금융 자산',
    'asset_verification_asset_type_crypto_currency': '암호화폐',
    'asset_verification_asset_type_other_assets': '기타 자산',
    'asset_verification_default_asset_name_real_estate': '부동산 자산 @index',
    'asset_verification_default_asset_name_financial': '금융 포트폴리오 @index',
    'asset_verification_default_asset_name_crypto': '암호화폐 지갑 @index',
    'asset_verification_default_asset_name_other': '기타 자산 @index',
    'asset_verification_add_asset': '자산 추가',
    'asset_verification_add_new_asset': '새 자산 추가',
    'asset_verification_new_asset': '새 자산',
    'asset_verification_form_complete': '완료',
    'asset_verification_form_incomplete': '미완료',
    'asset_verification_error_no_valid_assets': '유효한 자산 없음',
    'asset_verification_error_no_valid_assets_desc':
        '저장하기 전에 최소 하나의 자산 양식을 완료해 주세요.',
    'asset_verification_success_assets_saved': '자산 저장됨',
    'asset_verification_success_assets_saved_desc':
        '@count개의 @type 자산이 성공적으로 저장되었습니다.',
    'asset_verification_uploading_assets': '자산 업로드 중...',
    'asset_verification_uploading_file': '자료 업로드하기',
    'asset_verification_submitting_assets': '자산 제출 중...',
    'asset_verification_confirming_assets': '자산 확인 중...',
    'asset_verification_add_real_estate': '부동산 추가',
    'asset_verification_add_financial_asset': '금융 자산 추가',
    'asset_verification_add_crypto_asset': '암호화폐 추가',
    'asset_verification_add_other_assets': '기타 자산 추가',
    'asset_verification_add_more_files': '더 추가',
    'asset_verification_existing_files': '기존 파일',
    'asset_verification_uploaded_file': '업로드 완료',
    'asset_verification_error_cannot_delete_existing_file': '삭제 불가',
    'asset_verification_error_cannot_delete_existing_file_desc':
        '기존 자산의 파일은 삭제할 수 없습니다',
    'asset_verification_error_cannot_delete_existing_asset': '삭제 불가',
    'asset_verification_error_cannot_delete_existing_asset_desc':
        '기존 자산은 삭제할 수 없습니다',
    'asset_verification_confirmation_title': '자산 인증 제출',
    'asset_verification_confirmation_description':
        '추가된 자산을 확인해 주세요. 제출 후에는 자산이나 문서를 추가할 수 없습니다.',
    'asset_verification_agreement_text': 'Starchex의 자산 인증 서비스에 동의합니다',
    'asset_verification_submit': '제출',
    'asset_verification_error_agreement_required': '동의 필요',
    'asset_verification_error_agreement_required_desc': '자산 인증 서비스 약관에 동의해 주세요',
    'asset_verification_under_review_title': '자산 심사 중',
    'asset_verification_under_review_description':
        '자산 인증 서류가 성공적으로 제출되었으며 현재 저희 팀에서 검토 중입니다.',
    'asset_verification_under_review_info_title': '심사 과정',
    'asset_verification_under_review_info_description':
        '저희 인증팀에서 제출하신 서류를 영업일 기준 1-3일 내에 검토할 예정입니다. 검토가 완료되면 알려드리겠습니다.',
    'asset_verification_under_review_status_submitted': '서류 제출 완료',
    'asset_verification_under_review_status_submitted_desc': '업로드 성공',
    'asset_verification_under_review_status_reviewing': '심사 중',
    'asset_verification_under_review_status_reviewing_desc': '진행 중...',
    'asset_verification_under_review_status_complete': '인증 완료',
    'asset_verification_under_review_status_complete_desc': '대기 중',
    'asset_verification_back_to_home': '홈으로 돌아가기',
    'asset_verification_success_title': '자산 인증서',
    'asset_verification_success_my_certificate': '내 인증서',
    'asset_verification_success_received_certificate': '받은 인증서',
    'asset_verification_success_certificate_title': '스타체스 자산 인증서',
    'asset_verification_success_valid_until': '유효기간',
    'asset_verification_success_verification_text':
        '스타체스가 검증하고 발행한 자산 인증서입니다.\n위/변조 불가능한 형태로 안전하게 보관합니다.',
    'asset_verification_success_share': '공유하기',
    'asset_verification_success_qr_scan': 'QR 촬영',
    'asset_verification_success_share_title': '인증서 공유',
    'asset_verification_success_share_desc': '공유 기능이 곧 구현될 예정입니다.',
    'asset_verification_success_qr_title': 'QR 코드',
    'asset_verification_success_qr_desc': 'QR 코드 표시 기능이 곧 구현될 예정입니다.',
    'asset_verification_success_certificate_number': '인증번호',
    'asset_verification_success_asset_range': '자산규모',
    'asset_verification_success_issue_date': '발급일자',
    'asset_verification_success_expiry_date': '만료일자',
    'asset_verification_success_name': '이름',
    'asset_verification_success_occupation': '직업',
    'asset_verification_success_age_group': '연령대',
    'asset_verification_success_nationality': '국적',
    'asset_verification_success_gender': '성별',
    'asset_verification_success_five_star_member': '5-Star Member',
    'asset_verification_success_verification_message':
        '스타체스 자산 인증서는\n전문가가 직접 검증하고 보증합니다.',
    'asset_verification_success_tax_accountant': '세무사',
    'asset_verification_success_lawyer': '변호사',
    'asset_verification_success_accountant': '회계사',
    'asset_verification_success_footer_text':
        '본 인증서는 스타체스가 자체 심사를 거쳐 검증한 결과를 기반으로 발급됩니다. 가짜나 자산 정보의 정확성과 진위는 스타체스가 직접 확인하고 보장합니다.',
    'asset_verification_success_share_coming_soon': '공유 기능이 곧 구현될 예정입니다',
    'asset_verification_success_default_name': '유수진',
    'asset_verification_success_default_occupation': '하와이안항공 한국 지사장',
    'asset_verification_success_default_age_group': '40대 초반',
    'asset_verification_success_default_nationality': '대한민국, 미국',
    'asset_verification_success_default_gender': '여성',
    'asset_verification_success_default_asset_range': '100-300억',
    'asset_verification_success_default_issue_date': '2025. 7. 10',
    'asset_verification_success_default_expiry_date': '2027. 7. 10',
    'asset_verification_success_default_certificate_number': 'No. 14852301',
    'asset_verification_received_certificate_empty': '받은 인증서가 없습니다',
    'asset_verification_received_certificate_count': '개',
    'asset_verification_qr_scanner_title': 'QR 스캐너',
    'asset_verification_qr_scanner_instruction': 'QR 코드를 프레임 안에 맞춰서 스캔하세요',
    'asset_verification_qr_scanner_continue': '계속 스캔',
    'asset_verification_qr_scanner_use_data': '이 데이터 사용',
    'asset_verification_qr_scanner_position_hint': '위 프레임에 QR 코드를 맞춰주세요',
    'asset_verification_qr_scanner_scanned_data': '스캔된 데이터:',
    'asset_verification_qr_scanner_success': 'QR 코드 스캔 성공!',
    'asset_verification_qr_scanner_failed': '스캔 실패',
    'asset_verification_qr_scanner_permission_denied': '카메라 권한이 거부되었습니다',
    'asset_verification_qr_scanner_start_failed': 'QR 스캐너 시작 실패',
  };
}
