{"sc_str": {"common": {"view": "View", "view_all": "See all", "members": "Members", "events": "Events", "comments": "Comments", "comment_placeholder": "Write a comment...", "post": "Post", "upload": "Upload", "continue": "Continue", "publish": "Publish", "image": "Image"}, "event_intro": {"recommended": "Recommended Events"}, "home": {"this_week": "This Week", "last_week": "Last Week", "upcoming": "Upcoming", "weekly_star": "Weekly Star", "featured_events": "Featured Events", "notification_content": "Notification content", "last_week_event_title": "Last week's event", "next_week_star_title": "✨ Next Interviewee.", "next_week_star_subtitle": "Next week, we will have |@title| joining us.", "look_forward": "Looking forward...", "member_suffix": "Member", "interview_article": "View Interview Article"}, "comment": {"Write_comment": "Write a comment", "your_thought": "Leave your thoughts", "all_comments": "Comments", "add_comment": "Add Comment", "interview_comment_placeholder": "✨ Share your thoughts about the interview freely."}, "event": {"capacity_notice": "If capacity is exceeded, participants will be selected.", "event_introduction": "Event Introduction", "join_application": "Apply for Event", "application_completed": "Application completed. Final selection results will be notified individually 2 days before the event.", "congratulations_selected": "Congratulations! You have been selected for this event.", "not_selected_message": "We couldn't be together this time, but we look forward to the next opportunity. Don't miss other events too.", "ended": "Ended", "confirmed_participants": "Confirmed Participants", "past_interviews": "Past Interviews", "interview": "Interview", "address": "Address", "private_club_address": "Private Club in Hannam-dong, Seoul"}, "community": {"register_tip": "Craft a distinctive identity for our exclusive community. Your nickname remains private from your main profile.", "name_input_PH": "Enter your distinguished name", "name_already_taken": "This name is already taken. Try another one.", "name_select_tip": "Select from premium suggestions:", "enter_circle": "Enter the Circle", "fake_name_existed_error": "This name is already taken. Try another one.", "craft_your_post": "Craft Your Post", "select_a_circle": "Select a Circle", "share_your_insight": "Share your Insight...", "media_upload_tip": "Upload up to 10 high-quality images (JPG/PNG, max 5MB each)."}, "ai_avatar": {"generating": "Generating AI avatar, please wait...", "wait_time": "This may take 1-3 minutes", "generated": "🎉 AI Avatar Generated", "select_again": "Select Again", "crop_avatar": "Crop Avatar", "image_load_failed": "Image loading failed", "image_data_error": "Image data error", "network_image_failed": "Network image loading failed", "no_image": "No image available", "render_error": "Avatar rendering error", "completed": "AI Avatar Generation Completed", "generation_success": "AI avatar generated successfully! You can now use this avatar.", "generation_failed": "AI avatar generation failed", "generation_cancelled": "AI avatar generation cancelled", "cancel_generation": "Cancel AI Generation", "cancel_generation_confirm": "Are you sure you want to cancel AI avatar generation? This action cannot be undone.", "regenerate": "Regenerate", "use_this_avatar": "Use This Avatar", "set_success": "AI avatar set successfully", "generating_in_progress": "AI Generating", "estimated_time_remaining": "Estimated @seconds seconds remaining", "task_restored": "AI avatar generation task detected in progress, automatically restored", "task_completed": "AI avatar generation completed!"}}, "not_verified_yet_warning_message": "Caution! This person is not verified yet.", "rejected": "Rejected", "not_verified": "Not verified", "university": "University", "skip_invitation_message": "You can enter the referral code within a week of signing up on My page.", "input_validation": {"required_error_message": "This field is required."}, "badge": {"title": "Badge", "university_badges": "University badges", "wealth_badges": "Wealth badges", "badge_policy": "All university and wealth verification badges on Starchex are strictly validated and awarded by Starchex's verification teams based on appropriate supporting documents submitted by users. This rigorous process ensures the reliability and trustworthiness of our verification-based service.", "graduate": "Graduate", "graduate_tip": "User has graduated from a university.", "top_200": "Top 200", "top_200_tip": "User is from a top 200 university.", "more_than_500k": "More than 500K", "more_than_500k_tip": "User has more than 500K in assets.", "more_than_1m": "More than 1M", "more_than_1m_tip": "User has more than 1M in assets.", "more_than_10m": "More than 10M", "more_than_10m_tip": "User has more than 10M in assets."}, "asset_verification": {"title": "Asset Verification", "description": "The following assets can be verified with StarChecks.", "real_estate": "Real Estate", "financial_assets": "Financial Assets", "crypto_currency": "Crypto Currency", "art_works_cars": "Art works, Cars, ...", "guidance_title": "[Guidance & Disclaimers]", "guidance_1": "All submitted information is processed securely in accordance with relevant laws and our security policy.", "guidance_2": "For accurate asset verification, all information and documents must be factual and consistent.", "guidance_3": "Submission of false information may result in restriction of service use.", "next": "Next", "upload_id_title": "Please submit your ID", "upload_id_description": "You can verify assets below.", "id_card": "ID card", "file_format_info": "JPEG / PNG • Max 10 MB", "upload_file": "Upload file", "id_submission_guidelines": "[ID Submission Guidelines]", "guideline_clear_image": "Clear Image: Please take a photo of your <PERSON> so that the entire image is clearly visible. (Be careful of reflections and shadows.)", "guideline_include_original": "Include Original: Please take a photo of your ID so that the edges are not cut off.", "guideline_masking": "Masking: Please mask the last 7 digits of your resident registration number and passport number to protect your personal information.", "guideline_updated_info": "Updated Information: Please submit a valid ID. (Check for expiration)", "select_assets_title": "Please list your assets", "select_assets_description": "You can register multiple assets for each asset type.", "real_estate_desc": "House, Land, Building", "financial_assets_desc": "Deposits, savings, stocks, bonds, etc.", "crypto_currency_desc": "Bitcoin, Ethereum, etc.", "other_assets": "Other assets", "other_assets_desc": "Art work, Car, etc.", "list_of_added_assets": "List of added assets", "tap_to_add_asset": "Tap to add this asset type", "remove_asset": "Remove", "asset_name": "Asset Name", "document_of_proof": "Document of Proof", "selected_files": "Selected Files", "description_optional": "Description (optional)", "description_placeholder": "Please provide any additional information that would be helpful in assessing the value of your asset.", "document_upload_guidelines": "[Document Upload Guidelines]", "real_estate_name_placeholder": "e.g. Gangnam-gu Apartment", "financial_assets_name_placeholder": "e.g. Stocks held in Shinhan Securities account", "crypto_currency_name_placeholder": "e.g. Bithumb Bitcoin", "other_assets_name_placeholder": "e.g. Artwork by a famous artist", "real_estate_document_desc": "Record of Title(Real estate registration copy)", "financial_assets_document_desc": "Screenshot showing the balance\nScreenshot showing owner information", "crypto_currency_document_desc": "e.g. wallet screenshot, exchange statement, transaction history", "other_assets_document_desc": "Documents that can prove ownership and value\ne.g. sales contract, registration certificate, warranty, appraisal report, etc.", "real_estate_guideline_1": "Please upload an original copy of the registry (including cancellations) issued within the last 3 months by scanning or taking a photo.", "real_estate_guideline_2": "All sides must be clearly visible and the information must be identifiable.", "real_estate_guideline_3": "Please mask any personal identification information such as your resident registration number before submitting.", "financial_assets_guideline_1": "Please take high-resolution photos so that information can be identified. Multiple files can be uploaded.", "financial_assets_guideline_2": "All sides must be clearly visible and the information must be identifiable.", "financial_assets_guideline_3": "Please mask any personal identification information such as your resident registration number before submitting.", "crypto_currency_guideline_1": "Please take high-resolution photos so that information can be identified. Multiple files can be uploaded.", "crypto_currency_guideline_2": "All sides must be clearly visible and the information must be identifiable.", "crypto_currency_guideline_3": "Please mask any personal identification information such as your resident registration number before submitting.", "other_assets_guideline_1": "Please take high-resolution photos so that information can be identified. Multiple files can be uploaded.", "other_assets_guideline_2": "All sides must be clearly visible and the information must be identifiable.", "other_assets_guideline_3": "Please mask any personal identification information such as your resident registration number before submitting.", "common_guideline_ownership": "If the asset is not owned by you, please attach proof: Owned by a corporation (Certificate of History of Incorporation) Owned by a spouse (Certificate of Relations). If there are multiple, you only need to attach one.", "document_upload_guidelines_title": "[Document Upload Guidelines]", "denial_reasons_title": "The following assets may be denied.", "denial_reason_info_inconsistency": "Information inconsistency: Authentication may be rejected if the input information and submitted documents do not match, or if information cannot be verified.", "denial_reason_title_inconsistency": "Title inconsistency: Authentication may be rejected for assets for which the actual owner and the documented title do not match. (Except for ownership by spouse/corporation)", "denial_reason_proof_ownership": "If the asset is not owned by you, please attach proof: Owned by a corporation (Certificate of History of Incorporation) Owned by a spouse (Certificate of Relations). If there are multiple, you only need to attach one.", "real_estate_denial_unregistered": "Unregistered assets: Real estate for which registration procedures have not been completed may be denied for certification.", "real_estate_denial_disputes": "Ownership disputes: Real estate with restrictions on exercising ownership due to lawsuits, seizures, etc. may be denied certification.", "real_estate_denial_other_names": "Assets in other people's names: Assets with inconsistent real estate ownership and registered names may be denied certification. (Excluding spouse/corporation ownership)", "real_estate_denial_forgery": "Document forgery: If there is forgery or false information in the submitted documents, certification may be denied and use of the service may be restricted.", "financial_denial_uncertain_maturity": "Uncertain maturity products: Financial products with unclear contract terms or significantly low liquidity may be rejected for certification.", "financial_denial_high_risk": "High-risk financial assets: High-risk financial assets with high value volatility or uncertain objective valuation may be rejected for certification.", "crypto_denial_unregistered_exchange": "Unregistered Exchange Assets: Assets that are not registered on major exchanges and therefore difficult to verify price or transaction history may be rejected for authentication.", "crypto_denial_low_liquidity": "Low Market Liquidity Assets: Coins/tokens that are outside the top 100 in market capitalization or have significantly low trading volumes, making objective valuation difficult, may be rejected for authentication.", "crypto_denial_extreme_volatility": "Extreme Volatility/Delisting Risk Assets: High-risk virtual currencies with extreme value volatility or high risk of delisting may be rejected for authentication.", "other_denial_market_uncertainty": "Market value uncertainty: Assets for which objective market value is difficult to estimate or for which there is no credible valuation standard may be rejected for certification.", "other_denial_personal_agreement": "Personal agreement value: Assets valued solely by personal agreement value that does not meet our standards may be rejected for certification.", "other_denial_unclear_ownership": "Unclear ownership: Assets for which ownership cannot be proven or are in dispute may be rejected for certification.", "error_no_file_selected": "No File Selected", "error_no_file_selected_desc": "Please upload your ID card first", "success_assets_submitted": "Assets Submitted", "success_assets_submitted_desc": "Successfully submitted @count assets", "error_no_assets_added": "No Assets Added", "error_no_assets_added_desc": "Please add at least one asset", "success_file_selected": "File Selected", "success_file_selected_desc": "File @filename selected successfully", "error_file_selection": "Error", "error_file_selection_desc": "Failed to select file: @error", "success_files_selected": "Files Selected", "success_files_selected_desc": "Selected @count file(s)", "success_files_added_desc": "Added @count file(s), total: @total files", "info_files_already_exist": "Files Already Exist", "info_files_already_exist_desc": "All selected files already exist in this asset", "error_files_selection_desc": "Failed to select files: @error", "error_asset_name_required": "Please enter asset name", "error_file_required": "Please select at least one file", "success_asset_added": "Asset Added", "success_asset_added_desc": "Successfully added @name", "success_asset_removed": "As<PERSON> Removed", "success_asset_removed_desc": "Removed @name", "asset_type_real_estate": "Real Estate", "asset_type_financial_assets": "Financial Assets", "asset_type_crypto_currency": "Crypto Currency", "asset_type_other_assets": "Other Assets", "default_asset_name_real_estate": "Real Estate Property @index", "default_asset_name_financial": "Financial Portfolio @index", "default_asset_name_crypto": "Crypto Wallet @index", "default_asset_name_other": "Other Asset @index", "add_asset": "Add <PERSON>set", "add_new_asset": "Add New Asset", "new_asset": "New Asset", "form_complete": "Complete", "form_incomplete": "Incomplete", "error_no_valid_assets": "No Valid Assets", "error_no_valid_assets_desc": "Please complete at least one asset form before saving.", "success_assets_saved": "Assets Saved", "success_assets_saved_desc": "Successfully saved @count @type assets.", "uploading_assets": "Uploading Assets...", "uploading_file": "Uploading file", "submitting_assets": "Submitting Assets...", "confirming_assets": "Confirming Assets...", "add_real_estate": "Add Real Estate", "add_financial_asset": "Add Financial Asset", "add_crypto_asset": "Add Crypto Asset", "add_other_assets": "Add Other Assets", "add_more_files": "Add More", "existing_files": "Existing Files", "uploaded_file": "Already uploaded", "error_cannot_delete_existing_file": "Cannot Delete", "error_cannot_delete_existing_file_desc": "Cannot delete files from existing assets", "error_cannot_delete_existing_asset": "Cannot Delete", "error_cannot_delete_existing_asset_desc": "Cannot delete existing assets", "confirmation_title": "Submit for asset verification", "confirmation_description": "Please check added assets. Can't add assets or document after submitted.", "agreement_text": "I Agree to Starchex's asset verification service", "submit": "Submit", "error_agreement_required": "Agreement Required", "error_agreement_required_desc": "Please agree to the asset verification service terms", "under_review_title": "Assets Under Review", "under_review_description": "Your asset verification documents have been successfully submitted and are currently under review by our team.", "under_review_info_title": "Review Process", "under_review_info_description": "Our verification team will review your submitted documents within 1-3 business days. You will be notified once the review is complete.", "under_review_status_submitted": "Documents Submitted", "under_review_status_submitted_desc": "Successfully uploaded", "under_review_status_reviewing": "Under Review", "under_review_status_reviewing_desc": "In progress...", "under_review_status_complete": "Verification Complete", "under_review_status_complete_desc": "Pending", "back_to_home": "Back to Home", "success_title": "Asset Verification", "success_my_certificate": "My Certificate", "success_received_certificate": "Received Certificate", "success_certificate_title": "StarChecks Asset Certificate", "success_valid_until": "<PERSON>id <PERSON>", "success_verification_text": "StarChecks has verified and issued this asset certificate.Counterfeit/forgery is not allowed and will be strictly managed.", "success_share": "Share", "success_qr_scan": "QR <PERSON>", "success_share_title": "Share Certificate", "success_share_desc": "Share functionality will be implemented soon.", "success_qr_title": "QR Code", "success_qr_desc": "QR code display will be implemented soon.", "success_certificate_number": "Certificate Number", "success_asset_range": "Asset Range", "success_issue_date": "Issue Date", "success_expiry_date": "Expiry Date", "success_name": "Name", "success_occupation": "Occupation", "success_age_group": "Age Group", "success_nationality": "Nationality", "success_gender": "Gender", "success_five_star_member": "5-Star Member", "success_verification_message": "StarChecks Asset Certificate is\nverified and guaranteed by experts.", "success_tax_accountant": "Tax Accountant", "success_lawyer": "Lawyer", "success_accountant": "Accountant", "success_footer_text": "This certificate is issued based on the results verified through StarChecks' own examination. The accuracy and authenticity of fake or asset information are directly confirmed and guaranteed by StarChecks.", "success_share_coming_soon": "Share functionality coming soon", "success_default_name": "Yu <PERSON>", "success_default_occupation": "Hawaiian Airlines Korea Branch Manager", "success_default_age_group": "Early 40s", "success_default_nationality": "South Korea, USA", "success_default_gender": "Female", "success_default_asset_range": "10-30 billion", "success_default_issue_date": "2025. 7. 10", "success_default_expiry_date": "2027. 7. 10", "success_default_certificate_number": "No. ********", "received_certificate_empty": "No received certificates yet", "received_certificate_count": "certificates", "qr_scanner_title": "QR Scanner", "qr_scanner_instruction": "Align the QR code within the frame to scan", "qr_scanner_continue": "<PERSON><PERSON><PERSON>", "qr_scanner_use_data": "Use This Data", "qr_scanner_position_hint": "Position the QR code in the frame above", "qr_scanner_scanned_data": "Scanned Data:", "qr_scanner_success": "QR Code scanned successfully!", "qr_scanner_failed": "<PERSON>an failed", "qr_scanner_permission_denied": "Camera permission denied", "qr_scanner_start_failed": "Failed to start QR scanner"}, "common": {"finance_status": "Financial status", "search": "Search", "reject": "Reject", "rejected_reason": "Rejected reason", "reject_popup_title": "Confirm Rejection", "reject_popup_subtitle": {"first": "Are you sure you want to reject the heart", "last": "gave you?"}, "reject_popup_content": "Once rejected, he/she will no longer be able to send heart to you or match with you.", "reject_popup_input_hint": "Please enter a reason (required)", "continue": "Continue", "done": "Done", "cancel": "Cancel", "save": "Save", "back": "Back", "refresh": "Refresh", "close": "Exit", "next": "Next", "monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat", "sunday": "Sun", "send": "Send", "resend": "Resend", "verify": "Verify", "reverify": "Reverify", "edit": "Edit", "add": "Add", "preview": "Preview", "confirm": "Confirm", "ok": "OK", "successful": "Success", "heart": "Heart", "balance": "Balance", "double_heart": "Double heart", "double_heart_up": "<PERSON><PERSON><PERSON><PERSON>", "item_required_msg": "This is an invalid field.", "from_photos_album": "Photo Library", "from_photos_documents": "<PERSON><PERSON>", "file_too_large_content": "The imported file is too large to upload.", "file_do_not_support_type": "This file type is not supported.", "details": "Details", "total": "Total", "buy": "Buy", "price": "Price", "report": "Report", "block": "Block", "block_popup_title": "Are you sure you want to block this person?", "block_popup_content": "When blocked, you will not be able to match or chat with each other. And you can find him/her and unblock him/her in my page block list.", "chat": "Cha<PERSON>", "matched": "Matched", "copy": "Copy", "copy_finish_tip": "<PERSON>pied successfully.", "skip": "<PERSON><PERSON>", "today_cards": "Today's Cards", "got_hearts": "Got Hearts", "sent_hearts": "Sent Hearts", "given_cards": "Given Cards", "passed_cards": "Passed Cards", "evaluation_title": "Who gave me high evaluation", "locked": "Locked", "register_back_title": "Will you go back to previous step?", "register_back_content": "Will you go back to previous step? If you go back, your current input data will be reset.", "net_error": "Network error, please try again later.", "bottom_btn_today": "Today", "bottom_btn_passed": "Passed", "bottom_btn_community": "Community", "bottom_btn_chat": "Cha<PERSON>", "bottom_btn_me": "Me", "accept": "Accept", "try_again": "Unknown error, please try again later.", "remove": "Remove", "remove_popup_title": "chat history and matched partner will be removed in your matched list, you can still unblock partner in settings.", "account_kickoff_tip": "The account has been logged in on other devices."}, "permission": {"photo_galley_alert_title": "'Starchex' would like to access your photos", "photo_galley_alert_content": "Access to your photo library is required to browse and edit photos.", "camera_alert_title": "'Starchex' would like to access your camera", "camera_alert_content": "Access to your camera is required to record videos.", "mic_alert_title": "'Starchex' would like to access your microphone", "mic_alert_content": "Access to your microphone is required to record videos."}, "time": {"just_now": "Just Now", "ago": "ago", "minutes_ago": "minute", "hours_ago": "hour", "days_ago": "day", "months_ago": "month", "years_ago": "year"}, "splash": {"title": "Creating friends from college", "start": "Get start", "learn": "Learn"}, "sign_in": {"title": "Hi,  Welcome to Starchex !", "zalo": "Sign up with <PERSON><PERSON>", "google": "Sign up with Google", "facebook": "Sign up with Facebook", "apple": "Sign up with Apple ID"}, "agreement": {"title": "Agree with all terms and conditions", "accept_all_title": "I agree with all terms and conditions", "age_title": "18 years of age or older (Required)", "terms_title": "Terms and conditions (Required)", "personal_info_title": "Privacy policy (Required)", "promotional_title": "Consent to marketing (Optional)", "safety_and_policy_center": "Safety and Policy Center (Required)", "see_detail": " View More"}, "personal_basic_info": {"nationality": "Nationality", "nationality_hint": "Select your nationality", "please_enter_your_nationality": "Please enter your nationality", "sign_up": "Sign up", "title": "Please input your information.", "subtitle": "You can modify your information later on profile page, after you successfully register.", "nick_name": "Nick name", "nick_name_PH": "This is the nick name you see on Starchex", "date_birth": "Date of birth", "date_birth_PH": "Please select your date of birth", "region": "Region", "region_PH": "Select your region", "gender": "Gender", "female": "Female", "male": "Male", "phone_number": "Phone number", "phone_number_PH": "Please enter your phone number", "verification_code_PH": "Please enter the verification code", "phone_number_invalid_msg": "Mobile Number validate failed.", "phone_number_need_verify": "Verification required", "verification_code_sent_msg": "The Verification code has been sent.", "verification_code_valid_msg": "Success! Your mobile number has been verified.", "verification_code_invalid_msg": "Please enter a valid verification code.", "nick_name_exist_tip": "This nickname has been taken, please input others.", "email_PH": "Please enter your University Email.", "location_tip": "This data is used for local partner matching.", "phone_input_tip": "Starchex customer service team will contact you verify this is your actual phone number.", "email_send_tip": "If you do not see the email in a few minutes, check your \"junk mail\" folder or \"spam\" folder.", "saving": "Saving"}, "university_info": {"page_title": "Verification Information", "title": "Verify your university.", "subtitle": "Don't worry! Your real name is just used as verification and not exposed to the service.", "financial_verify_title": "Verify your wealthy", "financial_verify_subtitle": "Don't worry! Your financial status is just used as verification and not exposed to the service.", "name": "Name", "name_PH": "Real name", "university": "University", "university_PH": "Select your university", "verify_your_wealthly": "Verify your wealthy", "verify_your_wealthly_hint": "My financial status is", "financial_status": {"doNotAuthorize": "I do not authorize", "submitted": "I have submitted", "moreThan100K": "I have asset more than 100K USD", "moreThan500K": "I have asset more than 500K USD", "moreThan1M": "I have asset more than 1M USD", "moreThan5M": "I have asset more than 5M USD", "moreThan10M": "I have asset more than 10M USD", "moreThan50M": "I have asset more than 50M USD", "moreThan100M": "I have asset more than 100M USD"}, "auth_methods": "Auth Methods", "verify_email": "Verify by <PERSON><PERSON>", "verify_email_note": "Starchex is available immediately upon your verification.", "verify_cert_paper": "Verify by Cert. paper", "verify_cert_paper_note": "Starchex is available after Starchex's confirmation within 24 hours usually."}, "verify_email": {"subtitle": "Please input the email address of the university you have chosen.", "input_PH": "Please enter the verification code", "note": "An email with a verification code has been sent at @interval.", "code_valid_msg": "Success! Your email has been verified."}, "verify_cert_paper": {"subtitle": "Please upload your student ID photos or graduate cert.  of the university you have chosen.", "input_PH": "Upload student ID photos, graduate certificate, or proof of financial status.", "note1": "Starchex reviews the user's asset details to grant verification badges. By submitting your verification documents to Starchex, you signify your consent for Starchex to collect your sensitive personal information. Starchex will use your information solely for the purpose of asset verification and will securely delete your data immediately after verification.", "note2": "Please prepare and submit various documents to verify your financial status. You can provide documents such as bank account balances and real estate registration certificates according to your situation and nationality. Ensure that your sensitive personal information (e.g., unique passport numbers) is masked before submission.", "note3": "Starchex will review your documents and grant an asset verification badge. If additional documents are needed, we may request them through in-app messages.", "note4": "When uploading documents for school certification or property certification, please upload clear images so that Starchex can accurately identify them.", "submit_to_verify": "Submit to verify"}, "submit_photos": {"page_title": "Interests", "title": "Please register your photo.", "subtitle": "Your representative photo will be exposed on the main page of your profile. Please upload a photo that shows your face and full body (no limit on the number of photos)"}, "interesting": {"title": "Please select your hobbies.", "subtitle": "Find people who share your interests! Add up to 5 items.", "input_tip": "Can't find your hobbies? Please input."}, "introduce": {"title": "Please register freely to introduce yourself.", "subtitle": "The first three lines are directly exposed on inside the card.", "input_PH": "Input your information", "note1": "Information recorded that does not match the facts will be subject to criminal penalties.", "note2": "If there are any issues, please correct them."}, "signUp_completion": {"title": "@name,\nWelcome to Starchex!", "subtitle": "Now, every hour from 9:00 am, meet a wonderful partner delivered by Starchex.", "phone_verification_title": "Phone Verification", "phone_verification_subtitle": "Please verify your phone number to continue.", "pending_verification_title": "Pending Verification", "pending_verification_subtitle": "Please wait a moment! \nYour information will be verified by Starchex in the next 24 hours.", "pending_verification_subtitle_2": "If you update your attestation, your account will be pending until the admin approves it. Would you like to update it?", "failed_verification_title": "Verification failed", "failed_verification_subtitle": "Sorry, we couldn't verify student ID photos or graduate cert. \nPlease re-upload the photo and verify.", "back_verify": "Back to Verify"}, "profile": {"about_me": "About me", "interests": "Interests", "evaluate_card": "Evaluate the card", "rate_to_get_mc": "Evaluate the card and you will get <PERSON><PERSON><PERSON><PERSON><PERSON>", "rating_popup_title": "Evaluate the card", "rating_popup_subtitle": "You haven't evaluate the partner, are you sure you want to quit?", "to_rate": "To rate", "title": "Profile", "rating_result_tip": "you have got @amount MeeCoin for evaluating partners", "send_heart_message": "You have sent a heart to @name. It's a good start!", "send_double_heart_message": "You have sent a double heart to @name. It's a good start!", "heart_operation_tip_one": "You got Heart from ", "heart_operation_tip_two": "Send your Heart to be matched.", "doubleHeart_operation_tip_one": "You got <PERSON><PERSON><PERSON><PERSON> from ", "doubleHeart_operation_tip_two": "If you Accept this, you will be matched."}, "mee_coin": {"meecoin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "available_meecoin": "Available Mee<PERSON><PERSON>n", "recharge": "Recharge", "transaction": "Transaction", "mct_unlock_given_card": "Unlock Given card", "mct_unlock_high_eva": "Unlock High evaluation Card", "mct_unlock_heart_card": "Unlock Heart card", "mct_send_heart": "<PERSON>t Heart", "mct_send_double_heart": "Sent Double Heart", "mct_buy_premium": "Buy Premium cards", "mct_eva": "Evaluate", "mct_double_heart_refund": "Double heart payback", "mct_recharge": "Recharge", "mct_ref2_bonus": "Recommended subscriber", "mct_ref1_bonus": "Recommender", "mct_pre_reg": "Pre-subscribers", "mct_deposit": "System MeeCoin Reward", "mct_deduction": "System MeeCoin Deduct", "mct_top_post": "Top post", "recharge_title": "Please select the recharge \n amount.", "recharge_subtitle": "The more recharge amount, \nthe bigger discount!", "practical": "Practical", "popular": "Popular", "bonus_rate": "Added value @ratePercent", "bonus_rate_title": "Added value", "vnd": "VNĐ", "bonus": "Bonus", "recharge_amount": "Recharge amount", "pay_failed": "Pay Failed", "pay_invalid": "Pay Invalid"}, "today_cards": {"recharge_title": "Please select the recharge \n amount.", "recharge_subtitle": "The more recharge amount,  \nthe bigger discount!"}, "passed_cards": {"empty_msg": "Passed cards show the cards in the past 7 days.\nYou don',t have any more cards.\nTap |Today's| cards to unlock more cards."}, "unlock": {"balance": "Balance", "recharge": "Recharge", "cards": "@number cards", "premium_cards": "Premium Cards", "premium_subtitle_1": "Unlock @number cards at once.", "premium_subtitle_2": "Three high-quality partners for you.", "premium_subtitle_3": "Match them immediately!", "note": "By tapping Continue, you will be charged, \nand you agree to our |Terms|.", "unlock_single_heart_title": "Unlock Heart card", "unlock_single_heart_subtitle": "See who sent you an Heart card.", "unlock_passed_given_card_title": "Unlock Given Card", "unlock_passed_given_card_subtitle": "<PERSON><PERSON> passed Given card.", "unlock_high_eva_title": "Unlock High evaluation Card", "unlock_high_eva_subtitle": "Who gave you high evaluation.", "send_single_heart_title": "Send heart card", "send_single_heart_subtitle": "After sending the Heart card, the Heart card arrival notification will be sent to the partner through push and SMS.", "send_double_heart_title": "Send Double Heart card", "send_double_heart_subtitle": "Sending Double Heart card. The receiving partner can accept it without paying Heart. If failed, +20Meecoin would be paid back.", "not_enough_balance": "Not enough balance", "not_enough_balance_popup_content": "Your meecoin balance is not enough, please recharge.", "no_profile_popup_title": "Finish Profile", "no_profile_popup_content": "Need to finish your profile.", "no_profile_popup_ok_button": "Go to profile"}, "subscription": {"buy": "Buy", "normal_heart_name": "One Time", "normal_heart_description": "Send heart card one time.", "double_heart_name": "Double Heart card", "double_heart_description": "More likely to get matched!", "tip": "Compare Heart volumes"}, "chat": {"common": {"hey_there": "Hey there", "everyone": "everyone", "no_recent_emoji": "No Recent Emoji", "read": "Read", "say_hi": "Say Hello", "empty_tip": "Tap on a new match above to send \n a message.", "conversation_empty_tip": "No chat yet.\n Tap |Today's cards| to unlock more cards.", "today_btn": "Go to Today's cards", "typing_tip": "Typing...", "synchronizing": "synchronizing...", "syncFailed": "syncFailed", "connecting": "connecting...", "connectionFailed": "connectionFailed"}, "messageType": {"picture": "Picture", "video": "Video", "voice": "Voice", "file": "File", "emoji": "<PERSON><PERSON><PERSON>", "unsupportedMessage": "Unknown Message"}, "time": {"now": "Just Now", "justNow": "Just Now", "ago": "ago", "minutes_ago": "minute", "hours_ago": "hour", "days_ago": "day", "months_ago": "month", "years_ago": "year", "count_minute": "minute", "count_hour": "hour", "date_time_month": "", "date_time_day": ""}, "empty": {"matched_info1": "and You are matched.", "matched_info2": "Let's talk about something interesting."}, "sys_msg": {"remove_black": "User removed from black list."}}, "compare_heart": {"item": "<PERSON><PERSON>", "row1": "Partner can accept it for free", "row2": "Show received at the top", "row3": "Special push & SMS message notifications", "row4": "Compensation for failure", "row4_double_heart": "20Meecoin", "row5": "Improved matching success rate", "row5_double_heart": "4 times bigger chance"}, "my_page": {"title": "My page", "about": "About Starchex", "help_center": "Help Center", "feedback": "<PERSON><PERSON><PERSON>", "terms_conditions": "Terms & Conditions", "privacy_policy": "Privacy Policy", "safety_and_policy_center": "Safety and Policy Center", "settings": "Settings", "market": "Shop MeeCoin", "referrer_tip": "Your referral code", "available_text": "Available", "logout_btn": "Log out", "referrer_pop_title": "Refer friends & Earn", "referrer_pop_content": "Ask your friends to sign up with your referral code. Once done, both you and your friend each earn", "invitation": "Input Referrer code", "invitation_menu_tip": "<PERSON><PERSON><PERSON> +100", "pending_profile_tip": "Verification in progress.", "finish_profile_tip": "Please complete your information."}, "settings": {"title": "Settings", "manage_black_list": "Manage blocked accounts", "quit": "Delete account", "blocklist": "Block List", "unblock": "Unlock", "language": "Language", "english": "English", "korean": "Korean"}, "report": {"main_title": "Are you sure you want to report this person?", "sub_title": "Please select a reason for reporting.", "reason1": "Fake profile", "reason2": "Rude or abusive behavior", "reason3": "Inappropriate content", "reason4": "Scam or commercial ", "reason5": "Identity-based hate", "reason6": "Others (Direct entry)", "other_placeholder": "Please enter in 100 characters or less.", "image_tip": "Please upload a photo related to the report (required)", "image": "Image", "upload_tip": "You can attach up to 5 (60MB) related photos in JPG or PNG format.", "report_tip": "The fact of the report is not disclosed to the other party.", "report_btn": "Report", "file_empty_tip": "Please select a file"}, "edit_profile": {"upload_documents": "Upload documents", "update_my_attestation": "Manage my attensation", "support_document": "Support document", "support_document_tip": "Upload documents that can verify your educational background and assets.", "enable_discovery": "Enable Discovery", "enable_discovery_note": "When turned off, your profile will be hidden from the card stack and discovery will be disabled. People you have already matched may still see and chat with you.", "enable_discovery_alert_title": "Please enter required items", "enable_discovery_alert_content": "Sorry, you have to finish or update your profile firstly, then you can enable discovery", "photo_title": "Photo", "address_title": "Address", "interests_title": "Interests", "aboutme_title": "About me", "photo_avatar_require": "Please upload an avatar.", "interests_require": "Please choose interests.", "introduction_require": "Please enter introduction.", "unsaved_change_title": "You have unsaved changes!", "unsaved_change_content": "You have unsaved changes.\nPlease complete the changes and save.", "leave": "Leave", "stay": "Stay", "attestation": "Attestation", "basic_information": "Basic information", "others": "Others", "verify_university": "Verify university", "university_not_certified": "Not certified", "university_pending": "Certificating", "university_rejected": "Rejected", "university_certified": "Certified", "tap_to_edit_tip": "Tap to edit", "edit_save_tip_title": "Save profile", "edit_save_tip_content": "Are you sure you want to change your profile? This needs pending verification.", "university_name_required": "Please input your real name", "university_university_required": "Please select your university", "university_student_id_required": "Please upload your student ID card or graduate Cert. Paper", "university_email_required": "Please input your university email address", "university_v_code_required": "Please enter the verification code from email", "profile_university_required": "Please finish your university verification", "profile_nickname_required": "Please input your nick name", "profile_birth_required": "Please select your date of birth", "profile_region_required": "Please select your region", "profile_gender_required": "Please select your gender", "profile_phone_required": "Please input your phone number", "profile_photos_required": "Please upload one photo at least", "isGraduated": "Graduated"}, "invitation": {"referrer_tip": "Referrer code", "referrer_input_tip": "You can only enter it once, please fill it in carefully.", "referrer_popup_content_1": "If you have Referrer code, please enter. Both you and your friend each earn", "referrer_popup_content_2": "If not, press Skip.", "referrer_error_title": "Referrer code error", "referrer_error_content": "Sorry, your referrer code is wrong and there is no new user reward.", "reward_popup_title": "New User Reward", "reward_popup_content": "Welcome to Starchex ! Enjoy the new user reward <PERSON><PERSON><PERSON><PERSON><PERSON>", "referrer_code": "Referrer code"}, "feedback": {"email_label": "Email address to receive reply", "email_placeholder": "Please enter your email address.", "inquiry_label": "Inquiry details", "inquiry_placeholder": "select", "content_placeholder": "Please write the content in 1000 characters or less.", "image_label": "Please upload a photo related to the Feedback", "email_incorrect": "Please input correct Email.", "content_required": "Please input Feedback content.", "content_max_length": "Content length cannot exceed 1000."}, "quit": {"main_title": "do you really want to quit?", "main_desc": "Before canceling your membership, please be sure to check the information below.", "content_title": "Records of contracts or subscription withdrawals, etc.", "list_1": "Records of contracts or subscription withdrawals, etc.", "list_1_a": "Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 5 years)", "list_2": "Records of payment and supply of goods, etc.", "list_2_a": "Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 5 years)", "list_3": "Records of consumer complaints or dispute resolution", "list_3_a": "Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 3 years)", "list_4": "Records of display/advertisement", "list_4_a": "Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 6 months)", "list_5": "Records of illegal use, etc.", "list_5_a": "Based law: Act on Consumer Protection in Electronic Commerce, etc. (Retention period: 5 years)", "list_6": "Service visit records – access records, IP addresses, etc.", "list_6_a": "Based law: Communication Secrets Protection Act (retention period: 3 months)", "agreement_title": "I have confirmed all of the above. (essential)", "reason_title": "Why do you want to leave Starchex?\n (Required) *Multiple selection possible", "reason_1": "Not used", "reason_2": "I want to sign up again", "reason_3": "Inconvenient to use", "reason_4": "No desired content", "reason_5": "Etc", "reason_placeholder": "If you tell us in detail why you are leaving, it will greatly help us improve the quality of our service.", "quit": "Delete", "confirm_title": "Delete your account", "confirm_popup": "If you withdraw from Starchex, all of your activity history (MeeCoin points, photos, history, etc.) will be deleted. Please keep this in mind when making your decision.Would you like to withdraw?"}, "chat_notification": {"title": "Chat notification", "content": "You have a new message. Please check it."}, "notification": {"title": "Notification"}, "community": {"title": "Community", "register_tip": "Input your community nickname. This nickname is used for only community boards service, and it's  different from the nickname in the main matching service.", "fakeName_tip": "Can't think of one? Use one of these:", "delete": "Delete", "post_list_empty": "No contents. \nLooking forward to your post.", "comments_list_empty": "No comments.\nLooking forward to your comment.", "likes_list_empty": "No likes.\nYou will become the first person to like.", "me": "Me", "post": {"title": "Create a post", "placeholder": "What's on your mind? (10000 characters limitation)", "board_label": "Community", "post_btn": "Post", "photo_upload_tip": "You can attach up to Max 10 photos (each photo < 5MB) in JPG or PNG format.", "content_empty_tip": "Post content can't be empty", "deleted_post": "This is deleted by user", "show_more": "More", "hide_more": "<PERSON>de", "see_likes": "See likes", "all_comments": "All Comments", "all_likes": "All likes", "empty": "This is nothing"}, "comment": {"placeholder": "Comment...(10000 characters limitation)", "more_text": "See More Comments", "end_text": "End", "mention_empty_tip": "Sorry, we couldn't find the user ${searchText}$ \n Please check the nickname or try mentioning someone else.", "mention_empty_1": "Sorry, we couldn't find the user ", "mention_empty_2": "Please check the nickname or try mentioning someone else."}, "report": {"reason1": "Harassment", "reason2": "Threatening violence", "reason3": "Hate", "reason4": "Minor abuse or sexualization", "reason5": "Sharing personal information", "reason6": "Non-consensual intimate media", "reason7": "Prohibited transaction", "reason8": "Impersonation", "reason9": "Copyright violation", "reason10": "Trademark violation", "reason11": "Self-harm or suicide", "reason12": "Spam", "reason13": "Others "}, "profile": {"posts": "Posts", "likes": "<PERSON>s", "edit_profile": "Edit profile", "creat_post": "Create a post", "comments": "Comments"}}, "check_app_version": {"new_version_title": "Update App", "new_version_content": "Starchex has improved usability by reflecting users' opinions. Please update to the latest version for better service use.", "new_version_action": "Go to App Store", "new_version_action_later": "Later"}, "otp_verify_page": {"title": "OTP Verification", "message": "A text message with a 6-digit verification code\nwas sent to @phone_number", "didnt_get_the_code": "Didn't get the code?", "resend": "Resend", "resend_after": "Please verify the code in @time", "successfully": "Successfully verified your phone number", "error_title": "OTP code error"}, "sc_str_community_name_select_tip": "Choose a recommended name or create your own", "sc_str_community_enter_circle": "Enter Circle", "sc_str_community_fake_name_existed_error": "This name is already taken", "sc_str_ai_avatar_generated": "AI enhanced avatar generated", "sc_str_ai_avatar_error": {"no_croppable_avatar": "No AI avatar available for cropping", "web_not_supported": "Web version does not support avatar cropping", "data_format_error": "AI avatar data format error, cannot crop", "download_failed_code": "Failed to download AI avatar, status code", "download_failed": "Failed to download AI avatar", "crop_success": "Avatar cropping completed", "crop_failed": "Failed to crop avatar", "upload_first": "Please upload a photo first to generate AI avatar", "generating_wait": "Generating AI avatar, please wait patiently, this may take 1-3 minutes...", "empty_photo_data": "Photo data is empty", "empty_photo_file": "Photo file is empty", "timeout": "AI avatar generation timed out, server processing time is too long, please try again later"}}