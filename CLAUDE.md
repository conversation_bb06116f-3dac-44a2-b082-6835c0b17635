# Attention
## Get
遵循Getx的开发规范和最佳实践！
## Translation
当增加或修改翻译的时候，先更新在 assets/locales 目录下的 en.json 和 ko.json, 保持key的准确性，然后运行一下命令，会自动生成和修改 lib/generated/locales.g.dart 文件，不要直接修改 lib/generated/locales.g.dart 这个文件！
- **Generate Locales:**
  ```bash
  get generate locales assets/locales
  ```
## API 和 数据模型定义
当修改 API 和 数据模型 的时候
API：修改 lib/app/shared/networking/BPRestAPI.dart，遵循规范，然后运行以下命令，会自动生成 lib/app/shared/networking/BPRestAPI.g.dart 文件，不要直接修改 lib/app/shared/networking/BPRestAPI.g.dart 这个文件！
数据模型：， 和API相同道理，修改后运行以下命令，生成
- **Run build_runner and delete conflicting outputs:**
  ```bash
  dart run build_runner build --delete-conflicting-outputs
  ```
  *This command is also used for generating Assets & Models.*

# QR扫码功能实现

## 概述
已成功实现了完整的QR扫码功能，包括相机权限管理、扫码界面、数据解析和多语言支持。

## 实现的功能

### 1. QR扫码页面 (`lib/app/modules/QRScanner/views/qr_scanner_page.dart`)
- 使用 `qr_code_scanner` 库实现扫码功能
- 支持闪光灯开关
- 实时扫码预览
- 扫码结果确认界面
- 支持继续扫码或使用扫码数据

### 2. QR扫码工具类 (`lib/app/shared/components/BPQRScanner.dart`)
- 自动处理相机权限请求
- 权限被拒绝时的引导设置
- 数据类型自动识别（URL、邮箱、电话、JSON、文本）
- 错误处理和用户友好的提示

### 3. 多语言支持
在 `assets/locales/` 中添加了以下翻译键：
- `qr_scanner_title`: QR扫码器标题
- `qr_scanner_instruction`: 扫码指引
- `qr_scanner_continue`: 继续扫码
- `qr_scanner_use_data`: 使用数据
- `qr_scanner_position_hint`: 位置提示
- `qr_scanner_success`: 成功消息
- `qr_scanner_failed`: 失败消息
- `qr_scanner_permission_denied`: 权限拒绝
- `qr_scanner_start_failed`: 启动失败

### 4. 资产验证页面集成
在 `lib/app/modules/AssetVerification/views/asset_verification_success_page.dart` 中：
- 集成了真实的扫码功能
- 根据扫码数据类型显示不同的处理界面
- 支持URL、邮箱、电话、JSON和文本数据的处理

## 使用方法

### 基本使用
```dart
import 'package:starchex/app/shared/components/BPQRScanner.dart';

// 启动扫码
BPQRScanner.startScan(
  onScanResult: (String scannedData) {
    // 处理扫码结果
    print('扫码结果: $scannedData');
  },
  onError: (String error) {
    // 处理错误
    print('扫码错误: $error');
  },
);
```

### 数据解析
```dart
// 解析扫码数据
final parsedData = BPQRScanner.parseQRData(scannedData);
final dataType = parsedData['type'] as String; // url, email, phone, json, text

switch (dataType) {
  case 'url':
    final url = parsedData['url'] as String;
    // 处理URL
    break;
  case 'email':
    final email = parsedData['email'] as String;
    // 处理邮箱
    break;
  // ... 其他类型
}
```

### 数据验证
```dart
// 验证QR码数据
if (BPQRScanner.isValidQRData(scannedData)) {
  // 数据有效，继续处理
}
```

## 依赖
添加了以下依赖到 `pubspec.yaml`：
```yaml
dependencies:
  qr_code_scanner: ^1.0.1
```

## 权限配置

### Android (`android/app/src/main/AndroidManifest.xml`)
```xml
<uses-permission android:name="android.permission.CAMERA" />
```

### iOS (`ios/Runner/Info.plist`)
```xml
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to scan QR codes</string>
```

## 特性
1. **自动权限管理**: 自动检查和请求相机权限
2. **用户友好**: 权限被拒绝时引导用户去设置
3. **多数据类型支持**: 自动识别URL、邮箱、电话等格式
4. **错误处理**: 完善的错误处理和用户提示
5. **多语言**: 支持英语和韩语
6. **UI一致性**: 使用项目统一的UI组件和颜色

## 注意事项
1. 确保在真机上测试，模拟器无法使用相机功能
2. 首次使用时需要用户授权相机权限
3. 在低光环境下可以使用闪光灯功能
4. 扫码距离建议在10-30cm之间效果最佳
