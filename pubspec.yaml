name: starchex
version: 1.0.7+48
publish_to: none
description: Starchex.
environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  android_id: ^0.4.0
  app_links: ^6.3.2
  awesome_notifications: ^0.9.3+1
  awesome_notifications_core: ^0.9.3
  badges: ^3.1.2
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  chip_list: ^3.1.0
  circle_flags: ^5.0.0
  clipboard: ^0.1.3
  collection: ^1.18.0
  country_ip: ^3.0.0
  country_picker: ^2.0.26
  cupertino_icons: ^1.0.8
  detectable_text_field: ^3.0.2
  device_info_plus: ^10.1.2
  dio: ^5.7.0
  dotted_border: ^2.1.0
  dotted_dashed_line: ^0.0.3
  dropdown_button2: ^2.3.9
  easy_debounce: ^2.0.3
  easy_image_viewer: ^1.5.1
  emoji_picker_flutter: ^3.0.0
  expandable_section: ^0.0.3
  extended_image: ^8.2.4
  extended_text: ^14.1.0
  extended_text_field: ^16.0.0
  faker: ^2.2.0
  file: ^7.0.0
  file_picker: ^8.1.2
  firebase_analytics: ^11.3.2
  firebase_core: ^3.5.0
  firebase_crashlytics: ^4.1.2
  firebase_messaging: ^15.1.2
  flip_card: ^0.7.0
  flutter:
    sdk: flutter
  flutter_animate: ^4.5.0
  flutter_cache_manager: ^3.4.1
  flutter_cached_pdfview: ^0.4.2
  flutter_chat_bubble: ^2.0.2
  flutter_easyloading: ^3.0.5
  flutter_facebook_auth: ^7.1.1
  flutter_form_builder: ^9.4.1
  flutter_local_notifications: ^17.2.3
  flutter_localizations:
    sdk: flutter
  flutter_markdown: ^0.7.7

  flutter_pdfview: ^1.3.3
  flutter_rating_bar: ^4.0.1
  flutter_reorderable_grid_view: ^4.0.0
  flutter_screenutil: ^5.9.3
  flutter_shake_animated: ^0.0.5
  flutter_staggered_grid_view: ^0.7.0
  flutter_svg: ^2.0.10+1
  fluttertoast: ^8.2.8
  form_builder_validators: ^11.0.0
  get: ^4.6.6
  google_maps_flutter: ^2.9.0
  google_sign_in: ^6.2.1
  http_multi_server: ^3.2.1
  http_parser: ^4.0.2
  image_cropper: ^8.0.2
  image_picker: ^1.1.2
  in_app_purchase: ^3.2.0
  in_app_purchase_storekit: ^0.3.18+1
  intl: ^0.18.1
  json_annotation: ^4.9.0
  loading_indicator: ^3.1.1
  logger: ^2.4.0
  lottie: ^3.1.2
  no_screenshot: ^0.3.1
  onesignal_flutter: ^5.2.5
  package_info_plus: ^8.0.2
  path_provider: ^2.1.4
  pdf_render: ^1.4.12
  permission_handler: ^11.3.1
  phone_numbers_parser: ^9.0.0
  pinput: ^5.0.0
  qr_code_scanner: ^1.0.1
  pretty_dio_logger: ^1.4.0
  pull_to_refresh_flutter3: ^2.0.2
  random_avatar: ^0.0.8
  readmore: ^3.0.0
  retrofit: ^4.4.1
  rxdart: ^0.28.0
  scrollable_positioned_list: ^0.3.8
  shared_preferences: ^2.3.2
  sign_in_with_apple: ^6.1.2
  skeletonizer: ^1.4.2
  sprintf: ^7.0.0
  swipe_image_gallery: ^0.8.8
  ua_client_hints: ^1.3.1
  url_launcher: ^6.3.0
  uuid: ^4.5.0
  vibration: ^2.0.0
  video_player: ^2.9.1
  video_thumbnail: ^0.5.3
  webview_flutter: ^4.9.0
  date_format: ^2.0.7
  # xxim_sdk_flutter: ^1.0.3
  visibility_detector: ^0.4.0+2

dependency_overrides:
  intl: ^0.19.0

dev_dependencies:
  build_runner: ">=2.4.12 <4.0.0"
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter
  json_serializable: ^6.8.0
  retrofit_generator: ^9.1.2

flutter:
  assets:
    - assets/locales/
    - assets/images/common/
    - assets/images/svgs/
    - assets/images/scsvgs/
    - assets/lottie/
  fonts:
    - family: AppleSDGothicNeo
      fonts:
        - asset: assets/fonts/AppleSDGothicNeo-Heavy.ttf
          weight: 900
        - asset: assets/fonts/AppleSDGothicNeo-Bold.ttf
          weight: 700
        - asset: assets/fonts/AppleSDGothicNeo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/AppleSDGothicNeo-Medium.ttf
          weight: 500
        - asset: assets/fonts/AppleSDGothicNeo-Regular.ttf
          weight: 400

  uses-material-design: true
