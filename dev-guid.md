# StarCheck Flutter 开发环境安装指南

## 1. 安装 FVM (Flutter Version Management)

FVM 是 Flutter 版本管理工具，用于管理和切换不同的 Flutter 版本。

### 安装 FVM

```bash
# 使用 dart pub 安装 FVM
dart pub global activate fvm

# 或者使用 Homebrew (macOS)
brew tap leoafarias/fvm
brew install fvm
```

### 配置 FVM

```bash
# 安装本项目所需的 Flutter 版本
fvm install 3.24.5

# 在项目根目录中使用指定的 Flutter 版本
fvm use 3.24.5

# 验证 Flutter 版本
fvm flutter --version
```

### VS Code 中的 FVM 配置

为了在 VS Code 中正确使用 FVM 管理的 Flutter 版本，需要进行以下配置：

#### 1. 安装 VS Code 插件

- **Flutter**: 提供 Flutter 开发支持
- **Dart**: 提供 Dart 语言支持
- **FVM**: 提供 FVM 集成支持

#### 2. 配置 VS Code 设置

在项目根目录创建 `.vscode/settings.json` 文件（如果不存在），添加以下配置：

```json
{
  "dart.flutterSdkPath": ".fvm/flutter_sdk",
  "dart.flutterSdkPaths": [".fvm/flutter_sdk"],
  "search.exclude": {
    "**/.fvm": true
  },
  "files.watcherExclude": {
    "**/.fvm": true
  }
}
```

#### 3. 重新加载 VS Code

配置完成后，重新加载 VS Code 窗口：
- 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows/Linux)
- 输入 "Developer: Reload Window" 并执行

配置完成后，VS Code 中的所有 Flutter 命令将自动使用 FVM 管理的 Flutter 版本，无需手动添加 `fvm` 前缀。

## 2. 环境依赖安装

### 2.1 安装项目依赖

```bash
# 使用 FVM 安装 Flutter 依赖
fvm flutter pub get
```

### 2.2 生成代码文件

项目使用代码生成工具，需要运行以下命令：

```bash
# 生成 API 和数据模型文件
fvm flutter packages pub run build_runner build --delete-conflicting-outputs

# 生成本地化文件
get generate locales assets/locales
```

## 3. 平台特定配置

### 3.1 Android 配置

- 确保安装了 Android Studio 和 Android SDK
- 配置 `android/key.properties` 文件（如需要）
- 将 `google-services.json` 文件放置在 `android/app/` 目录中

### 3.2 iOS 配置

- 确保安装了 Xcode 和 iOS SDK
- 运行 `cd ios && pod install` 安装 CocoaPods 依赖
- 将 `GoogleService-Info.plist` 文件放置在 `ios/Runner/` 目录中

## 4. 运行项目

```bash
# 检查设备连接
fvm flutter devices

# 运行项目（调试模式）
fvm flutter run

# 运行项目（指定设备）
fvm flutter run -d [device_id]
```

## 5. 开发注意事项

### 5.1 代码规范

- 项目使用 GetX 状态管理框架，请遵循 GetX 开发规范
- 修改翻译文件时，请先更新 `assets/locales/` 目录下的 JSON 文件
- 修改 API 或数据模型时，请运行 `build_runner` 命令生成代码

### 5.2 依赖管理

- 项目使用 FVM 管理 Flutter 版本，请确保使用正确的版本
- 当前项目使用 Flutter 3.24.5 版本

### 5.3 构建脚本

项目提供了构建脚本：
- `build_android.sh` - Android 构建脚本
- `build_ios.sh` - iOS 构建脚本
- `ios_testflight.sh` - iOS TestFlight 发布脚本

## 6. 常见问题

### 6.1 FVM 命令未找到

确保 FVM 已正确安装并添加到系统 PATH 中：

```bash
# 检查 FVM 是否安装
fvm --version

# 如果未找到，请重新安装或检查 PATH 配置
```

### 6.2 依赖冲突

如果遇到依赖冲突，可以尝试：

```bash
# 清理缓存
fvm flutter clean

# 重新安装依赖
fvm flutter pub get

# 重新生成代码
fvm flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 6.3 iOS 构建问题

```bash
# 清理 iOS 构建缓存
cd ios && rm -rf Pods && pod install

# 重新构建
fvm flutter build ios
```

## 7. 开发工具推荐

### VS Code 推荐配置

- **必装插件**: 
  - Flutter
  - Dart
  - FVM (Flutter Version Management)
  - GetX Snippets
  - Error Lens (显示错误信息)
  - Flutter Widget Snippets

### Android Studio 推荐配置

- **必装插件**:
  - Flutter
  - Dart
  - GetX Snippets

### 调试工具

- **Flutter Inspector**: 用于调试 Widget 层次结构
- **Flutter DevTools**: 性能分析和调试工具
- **Dart DevTools**: Dart 代码调试工具

---

开始开发前，请确保完成以上所有步骤，并验证环境配置正确。